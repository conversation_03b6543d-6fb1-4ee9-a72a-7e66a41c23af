# Guide de déploiement de l'application Waabo sur Replit

Ce guide explique comment déployer l'application Waabo sur Replit, comment la convertir en application mobile avec Capacitor, et comment la tester avec Expo Go.

## Déploiement sur Replit

### Prérequis
- Un compte Replit
- Votre projet Waabo importé sur Replit

### Étapes pour le déploiement

1. **Préparer l'application pour le déploiement**
   
   Exécutez le script de déploiement:
   ```bash
   ./deploy-app.sh
   ```
   
   Ce script effectuera les actions suivantes:
   - Construction de l'application (build)
   - Configuration des variables d'environnement nécessaires
   - Synchronisation avec Capacitor

2. **Déployer sur Replit**

   Une fois l'application préparée:
   - Assurez-vous que le workflow `Start application` est en cours d'exécution
   - Cliquez sur le bouton `Run` (si ce n'est pas déjà fait)
   - Cliquez sur le bouton `Deploy` dans l'interface Replit
   - Votre application sera déployée à l'adresse: `https://<your-repl-slug>.<your-repl-owner>.repl.co`

## Test avec Capacitor

L'application Waabo est configurée pour utiliser Capacitor, qui permet de convertir votre application web en application mobile native.

### Pour Android

1. **Ajouter la plateforme Android**
   ```bash
   npx cap add android
   ```

2. **Synchroniser l'application**
   ```bash
   npx cap sync android
   ```

3. **Ouvrir dans Android Studio**
   ```bash
   npx cap open android
   ```

### Pour iOS

1. **Ajouter la plateforme iOS**
   ```bash
   npx cap add ios
   ```

2. **Synchroniser l'application**
   ```bash
   npx cap sync ios
   ```

3. **Ouvrir dans Xcode**
   ```bash
   npx cap open ios
   ```

## Test avec Expo Go

Bien que l'application utilise principalement Capacitor, il est possible de la tester sur Expo Go en suivant ces étapes:

1. **Installer l'application Expo Go** sur votre appareil mobile (disponible sur App Store et Play Store)

2. **Accéder à l'application déployée**
   - Ouvrez Expo Go
   - Sélectionnez "Enter URL manually"
   - Entrez l'URL de votre application déployée sur Replit: `https://<your-repl-slug>.<your-repl-owner>.repl.co`

3. **Alternative: Utiliser un QR code**
   - Générez un QR code pointant vers votre application déployée
   - Scannez ce QR code avec l'application Expo Go

## Conseils pour le déploiement

- Assurez-vous que votre base de données est correctement configurée avant le déploiement
- Vérifiez que toutes les variables d'environnement nécessaires sont définies
- Pour les tests sur appareil, vérifiez que votre application accepte les connexions HTTPS

## Résolution de problèmes

Si vous rencontrez des problèmes lors du déploiement:

1. **Erreurs de build**
   - Vérifiez les logs de build dans la console Replit
   - Assurez-vous que toutes les dépendances sont correctement installées

2. **Problèmes avec Capacitor**
   - Vérifiez que la configuration dans `capacitor.config.ts` est correcte
   - Assurez-vous que le chemin `webDir` pointe vers le répertoire de build

3. **Problèmes avec Expo Go**
   - Vérifiez que votre application est accessible via HTTPS
   - Assurez-vous que CORS est correctement configuré pour permettre l'accès depuis l'application Expo Go