# Documentation Complète du Projet Waabo

## Table des matières

1. [Introduction](#introduction)
2. [Architecture du Projet](#architecture-du-projet)
3. [Technologies et Dépendances](#technologies-et-dépendances)
4. [Configuration du Projet](#configuration-du-projet)
5. [Base de Données](#base-de-données)
6. [API Backend](#api-backend)
7. [Frontend](#frontend)
8. [Authentification](#authentification)
9. [Fonctionnalités Principales](#fonctionnalités-principales)
10. [Gestion des Notifications](#gestion-des-notifications)
11. [Administration](#administration)
12. [Conversion Mobile](#conversion-mobile)
13. [Déploiement](#déploiement)
14. [Maintenance et Dépannage](#maintenance-et-dépannage)
15. [Guides Associés](#guides-associés)

---

## Introduction

Waabo est une plateforme logistique de commerce électronique transfrontalier spécialisée dans le commerce international entre la Chine et le Burkina Faso. L'application offre des solutions d'expédition intelligentes et conviviales, avec une interface utilisateur et une conception d'interaction améliorées.

### Objectifs du Projet

- Faciliter le suivi des colis entre la Chine et le Burkina Faso
- Fournir une interface utilisateur intuitive et réactive
- Permettre l'enregistrement et la gestion des commandes
- Offrir un tableau de bord d'administration pour gérer les utilisateurs et les commandes
- Proposer un système de notifications en temps réel
- Permettre la conversion en applications mobiles iOS et Android

---

## Architecture du Projet

Le projet Waabo suit une architecture full-stack avec une séparation claire entre le frontend et le backend.

### Structure des Dossiers

```
waabo/
├── attached_assets/           # Ressources statiques importées 
├── client/                    # Code frontend
│   ├── src/
│   │   ├── assets/            # Ressources statiques (images, icônes)
│   │   ├── components/        # Composants React réutilisables
│   │   ├── hooks/             # Hooks React personnalisés
│   │   ├── lib/               # Utilitaires et fonctions d'aide
│   │   ├── pages/             # Pages/routes de l'application
│   │   │   ├── Admin/         # Pages du panneau d'administration
│   │   ├── styles/            # Fichiers de style (CSS, SCSS)
│   │   ├── App.tsx            # Composant racine
│   │   ├── index.tsx          # Point d'entrée
│   │   └── manifest.json      # Configuration PWA
├── server/                    # Code backend
│   ├── auth.ts                # Gestion de l'authentification
│   ├── routes.ts              # Définition des routes API
│   ├── storage.ts             # Interface de stockage
│   ├── db.ts                  # Configuration de la base de données
│   ├── email-service.ts       # Service d'envoi d'emails
│   ├── index.ts               # Point d'entrée du serveur
│   ├── apple-auth.ts          # Configuration de l'authentification Apple
│   └── google-auth.ts         # Configuration de l'authentification Google
├── shared/                    # Code partagé entre client et serveur
│   └── schema.ts              # Schéma de données et types
├── types/                     # Définitions de types TypeScript
├── build-app.js               # Script de build pour l'application
├── capacitor.config.ts        # Configuration Capacitor pour les apps mobiles
├── package-for-ios.js         # Script pour préparer l'app iOS
├── package-for-android.js     # Script pour préparer l'app Android
├── prepare-mobile-app.js      # Script combiné pour préparer les apps mobiles
└── divers guides et fichiers de configuration
```

### Flux des Données

1. **Interactions Utilisateur**: L'utilisateur interagit avec l'interface React
2. **Requêtes API**: Le frontend effectue des requêtes API au backend Express
3. **Traitement Backend**: Le serveur traite les requêtes via les routes définies
4. **Accès aux Données**: Le serveur interroge la base de données PostgreSQL
5. **Réponse API**: Les données sont renvoyées au client sous format JSON
6. **Rendu Frontend**: Le frontend affiche les données à l'utilisateur

---

## Technologies et Dépendances

### Backend

- **Node.js**: Environnement d'exécution JavaScript côté serveur
- **Express**: Framework web pour Node.js
- **TypeScript**: Superset typé de JavaScript
- **PostgreSQL**: Base de données relationnelle
- **Drizzle ORM**: ORM pour TypeScript
- **Passport.js**: Middleware d'authentification
- **SendGrid**: Service d'envoi d'emails

### Frontend

- **React**: Bibliothèque UI
- **TypeScript**: Pour le typage statique
- **Tailwind CSS**: Framework CSS utilitaire
- **Shadcn/UI**: Composants UI basés sur Radix UI
- **TanStack Query (React Query)**: Gestion des requêtes API
- **Wouter**: Routage côté client léger
- **Framer Motion**: Animations
- **React Hook Form**: Gestion des formulaires
- **Zod**: Validation de schéma

### Outils de Développement

- **Vite**: Bundler et serveur de développement
- **ESBuild**: Compilateur JavaScript ultra-rapide
- **Capacitor**: Framework pour convertir les applications web en applications mobiles

### Mobile

- **Capacitor Core**: Noyau du framework Capacitor
- **Capacitor iOS**: Support iOS pour Capacitor
- **Capacitor Android**: Support Android pour Capacitor
- **Capacitor Splash Screen**: Plugin d'écran de démarrage

---

## Configuration du Projet

### Variables d'Environnement

Le projet utilise plusieurs variables d'environnement, stockées dans le fichier `.env` :

```
DATABASE_URL=...          # URL de connexion à la base de données PostgreSQL
SENDGRID_API_KEY=...      # Clé API SendGrid pour l'envoi d'emails
SESSION_SECRET=...        # Secret pour les sessions Express
APPLE_TEAM_ID=...         # ID d'équipe Apple Developer
APPLE_KEY_ID=...          # ID de clé Apple
APPLE_PRIVATE_KEY=...     # Clé privée Apple pour l'authentification
FACEBOOK_APP_ID=...       # ID d'application Facebook
FACEBOOK_APP_SECRET=...   # Secret d'application Facebook
GOOGLE_CLIENT_ID=...      # ID client Google OAuth
GOOGLE_CLIENT_SECRET=...  # Secret client Google OAuth
PUBLIC_URL=...            # URL publique de l'application (pour Capacitor)
```

### Configuration de la Base de Données

La configuration de la base de données est définie dans `server/db.ts` et `drizzle.config.ts`.

### Paramètres Serveur

Les paramètres serveur sont configurés dans `server/index.ts`.

---

## Base de Données

### Schéma de Base de Données

Le schéma de la base de données est défini dans `shared/schema.ts`. Les principales tables sont :

- **users**: Informations sur les utilisateurs
- **orders**: Commandes des clients
- **products**: Produits disponibles
- **saved_orders**: Commandes enregistrées par les utilisateurs
- **notifications**: Notifications système
- **popup_notifications**: Notifications popup pour les utilisateurs
- **popup_seen**: Suivi des popups vus par les utilisateurs
- **order_statuses**: Statuts possibles des commandes
- **static_pages**: Pages statiques gérées par l'admin
- **app_settings**: Paramètres globaux de l'application (numéro WhatsApp, etc.)
- **contact_messages**: Messages de contact envoyés via le formulaire

### Migrations

Les migrations de base de données sont gérées via Drizzle ORM. Pour appliquer les changements de schéma :

```bash
npm run db:push
```

### Connexion à la Base de Données

La connexion à la base de données est établie dans `server/db.ts` via le pool PostgreSQL et l'ORM Drizzle.

---

## API Backend

### Routes API

Les routes API sont définies dans `server/routes.ts` et suivent la structure RESTful.

#### Routes Utilisateur

- `GET /api/users`: Liste tous les utilisateurs
- `GET /api/users/export/excel`: Exporte les utilisateurs au format Excel
- `GET /api/users/:id`: Récupère un utilisateur par ID
- `PATCH /api/users/:id`: Met à jour un utilisateur
- `DELETE /api/users/:id`: Supprime un utilisateur
- `PATCH /api/users/:id/password`: Change le mot de passe d'un utilisateur

#### Routes d'Authentification

- `POST /api/auth/login`: Authentification par email/mot de passe
- `POST /api/auth/register`: Inscription d'un nouvel utilisateur
- `GET /api/auth/logout`: Déconnexion
- `POST /api/auth/forgot-password`: Demande de réinitialisation de mot de passe
- `POST /api/auth/reset-password`: Réinitialisation de mot de passe
- Diverses routes pour OAuth (Google, Facebook, Apple)

#### Routes de Commande

- `GET /api/orders`: Liste toutes les commandes
- `GET /api/orders/:reference`: Récupère une commande par référence
- `POST /api/orders`: Crée une nouvelle commande
- `PATCH /api/orders/:id`: Met à jour une commande
- `GET /api/orders/user/:userId`: Commandes d'un utilisateur
- `GET /api/orders/saved/:userId`: Commandes sauvegardées par un utilisateur
- `POST /api/orders/save`: Sauvegarde une commande
- `DELETE /api/orders/saved/:orderId`: Supprime une commande sauvegardée

#### Routes de Produit

- `GET /api/products`: Liste tous les produits
- `GET /api/products/:id`: Récupère un produit par ID
- `POST /api/products`: Crée un nouveau produit
- `PATCH /api/products/:id`: Met à jour un produit
- `DELETE /api/products/:id`: Supprime un produit

#### Routes de Notification

- `POST /api/notifications`: Crée une notification
- `GET /api/notifications/user/:userId`: Notifications d'un utilisateur
- `PATCH /api/notifications/:id/read`: Marque une notification comme lue
- Routes pour les notifications popup

#### Autres Routes

- Routes pour les statuts de commande
- Routes pour les pages statiques
- Routes pour les paramètres d'application
- Routes pour les messages de contact

### Middlewares

Plusieurs middlewares sont utilisés dans l'application :

- Middleware d'authentification
- Middleware de validation
- Middleware de gestion d'erreurs
- Middleware de session
- Middleware CORS

---

## Frontend

### Pages Principales

- **Home.tsx**: Page d'accueil avec présentation du service et fonctionnalités principales
- **ProfilePage.tsx**: Profil utilisateur avec gestion des informations personnelles
- **OrderTrackingPage.tsx**: Suivi des commandes en temps réel
- **SavedOrdersPage.tsx**: Liste des commandes sauvegardées par l'utilisateur
- **ProductsPage.tsx**: Catalogue des produits disponibles
- **ContactPage.tsx**: Formulaire de contact
- **StaticPage.tsx**: Pages statiques (À propos, Conditions d'utilisation, etc.)
- **NotificationsPage.tsx**: Centre de notifications

### Pages d'Authentification

- **AuthPage.tsx**: Page d'authentification avec onglets pour connexion et inscription
- **ForgotPasswordPage.tsx**: Demande de réinitialisation de mot de passe
- **ResetPasswordPage.tsx**: Réinitialisation du mot de passe
- **EmailVerificationPage.tsx**: Vérification d'adresse email

### Pages d'Administration

- **Dashboard.tsx**: Tableau de bord général
- **AdminUsersPage.tsx**: Gestion des utilisateurs
- **AdminOrdersPage.tsx**: Gestion des commandes
- **AdminProductsPage.tsx**: Gestion des produits
- **AdminNotificationsPage.tsx**: Gestion des notifications
- **AdminStaticPagesPage.tsx**: Édition des pages statiques
- **AdminSettingsPage.tsx**: Paramètres généraux de l'application
- **AdminContactMessagesPage.tsx**: Gestion des messages de contact

### Composants Principaux

- **Layout.tsx**: Disposition générale de l'application
- **Navbar.tsx**: Barre de navigation
- **Footer.tsx**: Pied de page
- **Sidebar.tsx**: Barre latérale (notamment pour l'administration)
- **Logo.tsx**: Logo Waabo avec variantes
- **OrderCard.tsx**: Carte de commande
- **ProductCard.tsx**: Carte de produit
- **NotificationItem.tsx**: Élément de notification
- **OrderStatusTimeline.tsx**: Chronologie des statuts de commande
- **SaveOrderButton.tsx**: Bouton pour sauvegarder une commande
- Divers composants UI basés sur shadcn/ui

### Hooks Personnalisés

- **useAuth.tsx**: Gestion de l'authentification
- **useUser.tsx**: Accès aux données utilisateur
- **useOrders.tsx**: Récupération et manipulation des commandes
- **useNotifications.tsx**: Gestion des notifications
- **useWebSocket.tsx**: Communication WebSocket en temps réel
- **useToast.tsx**: Affichage de messages toast

### Styles et Thème

- Le projet utilise Tailwind CSS pour le styling
- La configuration du thème est dans `theme.json` et `tailwind.config.ts`
- Les couleurs principales sont:
  - Vert (`#004d25`) pour la marque principale "waabo"
  - Or pour le suffixe "express"
  - Diverses nuances de gris pour les éléments d'interface

---

## Authentification

### Méthodes d'Authentification

Waabo supporte plusieurs méthodes d'authentification :

1. **Email/Mot de passe**: Authentification classique
2. **Google OAuth**: Connexion via compte Google
3. **Facebook OAuth**: Connexion via compte Facebook
4. **Apple Sign-In**: Connexion via compte Apple

### Configuration de l'Authentification

La configuration de l'authentification est définie dans plusieurs fichiers :

- `server/auth.ts`: Configuration générale de l'authentification
- `server/google-auth.ts`: Configuration de l'authentification Google
- `server/apple-auth.ts`: Configuration de l'authentification Apple

### Sessions et Sécurité

- Les sessions utilisateur sont gérées via `express-session`
- Les mots de passe sont hashés avant stockage en base de données
- Les tokens de réinitialisation de mot de passe ont une durée de validité limitée
- La vérification d'email est supportée pour renforcer la sécurité

---

## Fonctionnalités Principales

### Suivi de Commande

- Les utilisateurs peuvent suivre leurs commandes par numéro de référence
- Affichage de la chronologie des statuts avec timestamps
- Possibilité d'enregistrer une commande pour un suivi facile
- Option de contact direct via WhatsApp

### Gestion des Commandes Sauvegardées

- Interface dédiée pour afficher toutes les commandes sauvegardées
- Suppression facile de commandes de la liste
- Vue détaillée des informations de commande

### Profil Utilisateur

- Modification des informations personnelles (nom, email, etc.)
- Gestion de la vérification d'email
- Changement de mot de passe
- Historique des commandes liées au compte

### Catalogue de Produits

- Affichage des produits disponibles
- Filtrage et recherche
- Détails des produits

### Contact et Support

- Formulaire de contact pour envoyer des messages à l'administration
- Accès direct au support via WhatsApp
- Pages d'information statiques (FAQ, À propos, etc.)

---

## Gestion des Notifications

### Types de Notifications

1. **Notifications système**: Apparaissent dans le centre de notifications
2. **Notifications popup**: S'affichent à l'écran lors de la connexion ou d'événements importants

### Fonctionnement des Notifications

- Les notifications sont envoyées en temps réel via WebSocket
- Les utilisateurs peuvent voir toutes leurs notifications dans la page dédiée
- Un badge indique le nombre de notifications non lues
- Les administrateurs peuvent envoyer des notifications globales ou ciblées

### WebSockets

- La configuration WebSocket est dans `server/routes.ts`
- Le hook `useWebSocket` dans le frontend gère la connexion et la réception des notifications
- Un système de reconnexion automatique est mis en place en cas de déconnexion

---

## Administration

### Tableau de Bord

Le tableau de bord d'administration offre une vue d'ensemble avec des statistiques clés:
- Nombre d'utilisateurs
- Nombre de commandes
- Répartition des statuts de commande
- Activité récente

### Gestion des Utilisateurs

- Liste complète des utilisateurs avec filtrage et recherche
- Modification des informations utilisateur
- Désactivation/suppression de comptes
- Export Excel des données utilisateurs

### Gestion des Commandes

- Vue détaillée de toutes les commandes
- Mise à jour des statuts de commande
- Filtrage par statut, date, etc.
- Recherche par référence ou utilisateur

### Gestion des Produits

- Ajout, modification et suppression de produits
- Gestion des images et descriptions

### Gestion des Notifications

- Création de notifications globales ou ciblées
- Configuration des notifications popup

### Pages Statiques

- Éditeur pour les pages statiques (À propos, CGV, etc.)
- Prévisualisation avant publication

### Paramètres Généraux

- Configuration du numéro WhatsApp
- Autres paramètres globaux de l'application

---

## Conversion Mobile

### Structure Capacitor

Le projet est configuré pour être converti en applications mobiles via Capacitor:

- **capacitor.config.ts**: Configuration principale de Capacitor
- **package-for-ios.js**: Script pour préparer l'application iOS
- **package-for-android.js**: Script pour préparer l'application Android
- **prepare-mobile-app.js**: Script combiné pour iOS et Android

### Configuration iOS

La configuration iOS dans `capacitor.config.ts` inclut:
- ID de bundle: `com.waabo.app`
- Nom de l'application: `Waabo`
- Configuration d'écran de démarrage
- Schémas d'URL et domaines autorisés

### Configuration Android

La configuration Android dans `capacitor.config.ts` inclut:
- Couleur d'arrière-plan
- Configuration de débogage
- Paramètres de capture d'entrée

### Processus de Conversion

Le processus de conversion est documenté dans `MOBILE_APP_GUIDE.md` avec ces étapes principales:
1. Construction de l'application web
2. Ajout et configuration des plateformes iOS/Android
3. Synchronisation des fichiers
4. Personnalisation spécifique à la plateforme
5. Construction des paquets natifs

### Publication sur les Stores

Des guides détaillés pour la publication sont fournis:
- `IOS_APP_STORE_GUIDE.md` pour l'App Store
- `ANDROID_APP_STORE_GUIDE.md` pour le Play Store

---

## Déploiement

### Déploiement Web

L'application est déployée sur Replit avec l'URL de production `waabo-app.com`.

### Build et Optimisation

Le processus de build est défini dans `build-app.js` et inclut:
1. Construction de l'application frontend (Vite)
2. Compilation du backend (ESBuild)
3. Optimisations pour la production
4. Configuration des variables d'environnement

### Script de Déploiement

Le déploiement peut être lancé avec:
```bash
npm run build && npm run start
```

Ou via le script `deploy-app.sh` qui automatise les étapes nécessaires.

---

## Maintenance et Dépannage

### Logs et Monitoring

- Les logs serveur sont disponibles dans la console Replit
- Les erreurs frontend sont capturées et affichées via un modal de Vite

### Problèmes Courants et Solutions

1. **Problèmes de connexion à la base de données**
   - Vérifier les identifiants dans les variables d'environnement
   - S'assurer que le service PostgreSQL est actif

2. **Erreurs d'authentification OAuth**
   - Vérifier les clés et secrets dans les variables d'environnement
   - Vérifier que les URLs de callback sont correctement configurées

3. **Problèmes de WebSocket**
   - S'assurer que le port est disponible
   - Vérifier les politiques CORS

4. **Erreurs de build Capacitor**
   - Vérifier que toutes les dépendances sont installées
   - S'assurer que le webDir est correctement configuré

---

## Guides Associés

Plusieurs guides sont disponibles pour des aspects spécifiques du projet:

- **IOS_APP_STORE_GUIDE.md**: Guide complet pour la publication sur l'App Store iOS
- **ANDROID_APP_STORE_GUIDE.md**: Guide pour la publication sur le Google Play Store
- **PWA_IOS_README.md**: Instructions spécifiques pour l'utilisation en tant que PWA sur iOS
- **MOBILE_APP_GUIDE.md**: Guide général pour la conversion en applications mobiles
- **DEPLOY_GUIDE.md**: Instructions pour le déploiement

---

## Conclusion

Waabo est une application complète et sophistiquée qui répond aux besoins logistiques e-commerce entre la Chine et le Burkina Faso. Avec son interface intuitive, ses fonctionnalités multiples et sa capacité à être déployée sur différentes plateformes, elle offre une solution robuste pour le suivi et la gestion des expéditions internationales.

Ce document fournit une vue d'ensemble exhaustive de l'architecture, des fonctionnalités et des processus techniques du projet. Pour des questions spécifiques ou des problèmes non couverts, veuillez contacter l'équipe de développement.

---

**Version de la documentation**: 1.0  
**Date de dernière mise à jour**: 8 mai 2025  
**Auteur**: Équipe de développement Waabo