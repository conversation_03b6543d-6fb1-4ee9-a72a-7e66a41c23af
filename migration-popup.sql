-- Création de la table popup_notifications
CREATE TABLE IF NOT EXISTS popup_notifications (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    width INTEGER DEFAULT 400,
    height INTEGER DEFAULT 300,
    background_color TEXT DEFAULT '#ffffff',
    font_family TEXT DEFAULT 'Inter, sans-serif',
    font_size TEXT DEFAULT '16px',
    font_weight TEXT DEFAULT 'normal',
    image_url TEXT,
    image_position TEXT DEFAULT 'top',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Création de la table user_seen_popups
CREATE TABLE IF NOT EXISTS user_seen_popups (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    popup_id INTEGER NOT NULL REFERENCES popup_notifications(id) ON DELETE CASCADE,
    seen_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Création d'un index sur l'association user_id et popup_id pour des recherches rapides
CREATE INDEX IF NOT EXISTS idx_user_seen_popups ON user_seen_popups(user_id, popup_id);