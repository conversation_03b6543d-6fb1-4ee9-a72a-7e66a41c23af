# Hướng dẫn Build Android Release APK

## 🔧 C<PERSON>u hình ban đầu

### 1. <PERSON><PERSON>u hình Keystore

Bạn cần cập nhật file `android/keystore.properties` với thông tin keystore thực tế:

```properties
storeFile=key.jks
storePassword=your_actual_store_password
keyAlias=your_actual_key_alias
keyPassword=your_actual_key_password
```

**Lưu ý:** File này đã được thêm vào `.gitignore` để bảo mật.

### 2. Tạo Keystore mới (nếu chưa có)

Nếu bạn chưa có keystore, tạo mới bằng lệnh:

```bash
cd android
keytool -genkey -v -keystore key.jks -keyalg RSA -keysize 2048 -validity 10000 -alias waabo-key
```

## 🚀 Cách build Release APK

### Phương pháp 1: Sử dụng script tự động

```bash
./build-android-release.sh
```

### Phương pháp 2: Build thủ công

```bash
# 1. Build web app
npm run build

# 2. Sync Capacitor
npx cap sync android

# 3. Build release APK
cd android
./gradlew assembleRelease
```

## 📍 Vị trí file APK

Sau khi build thành công, file APK sẽ được tạo tại:
```
android/app/build/outputs/apk/release/app-release.apk
```

## 🔍 Kiểm tra APK

Để kiểm tra thông tin APK:

```bash
# Kiểm tra signature
jarsigner -verify -verbose -certs android/app/build/outputs/apk/release/app-release.apk

# Kiểm tra thông tin APK
aapt dump badging android/app/build/outputs/apk/release/app-release.apk
```

## ⚠️ Lưu ý quan trọng

1. **Bảo mật keystore**: Luôn backup keystore và password ở nơi an toàn
2. **Version code**: Tăng `versionCode` trong `android/app/build.gradle` cho mỗi release
3. **Testing**: Test APK trên thiết bị thật trước khi phát hành
4. **ProGuard**: Có thể bật `minifyEnabled true` để giảm kích thước APK

## 🐛 Troubleshooting

### Lỗi keystore không tìm thấy
- Kiểm tra đường dẫn trong `keystore.properties`
- Đảm bảo file `key.jks` tồn tại trong thư mục `android/`

### Lỗi build failed
- Chạy `./gradlew clean` trước khi build lại
- Kiểm tra Android SDK và build tools đã cài đặt đúng

### APK không cài được
- Kiểm tra signature của APK
- Đảm bảo đã enable "Unknown sources" trên thiết bị
