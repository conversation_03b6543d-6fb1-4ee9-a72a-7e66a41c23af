/**
 * Fichier temporaire pour tester les modifications des emails
 */

// Version originale de l'en-tête avec image
const originalHeader = `
<!-- Header avec logo -->
<tr>
  <td style="padding: 30px 0; text-align: center; background-color: #004d25; border-top-left-radius: 8px; border-top-right-radius: 8px;">
    <img src="cid:waabo-logo" alt="Waabo Logo" style="width: 180px; height: auto;" />
  </td>
</tr>
`;

// Nouvelle version avec HTML du logo (basé sur waabo.v1.html)
const newHeader = `
<!-- Header avec logo Waabo Express -->
<tr>
  <td style="padding: 30px 0; text-align: center; background-color: #004d25; border-top-left-radius: 8px; border-top-right-radius: 8px;">
    <div style="display: flex; flex-direction: column; align-items: center; font-family: 'Roboto', sans-serif; font-weight: bold; line-height: 1;">
      <div style="color: #ffffff; font-size: 48px;">
        <span style="display: inline-block;">w</span><span style="display: inline-block;">a</span><span style="display: inline-block;">a</span><span style="display: inline-block;">b</span><span style="display: inline-block;">o</span>
      </div>
      <div style="color: #e5a100; font-size: 24px; margin-top: -5px;">
        <span style="display: inline-block;">e</span><span style="display: inline-block;">x</span><span style="display: inline-block;">p</span><span style="display: inline-block;">r</span><span style="display: inline-block;">e</span><span style="display: inline-block;">s</span><span style="display: inline-block;">s</span>
      </div>
    </div>
  </td>
</tr>
`;

// Version originale des pièces jointes
const originalAttachments = `
// Ajouter les logos intégrés pour que le logo s'affiche correctement
const attachments = [
  {
    content: 'iVBORw0KGgoAAAANSUhEUgAAA3QAAAHVCAMAAAClN3VmAAAAGXRFWHRTb2Z0d2...',
    filename: 'waabo-logo-transparent.png',
    type: 'image/png',
    disposition: 'inline',
    content_id: 'waabo-logo'
  }
];
`;

// Nouvelle version sans pièces jointes
const newAttachments = `
// Plus besoin de pièces jointes car le logo est en HTML
// Remplacer le code suivant dans les deux fonctions (email de réinitialisation et email de vérification)
const emailSent = await sendEmail(emailParams);
return emailSent;
`;