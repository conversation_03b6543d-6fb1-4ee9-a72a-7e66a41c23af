import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import waaboPng from '@assets/Trans.png';

interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onFinish }) => {
  const [showLogo, setShowLogo] = useState(false);

  useEffect(() => {
    // Start the animation sequence after component is mounted
    const animationTimeout = setTimeout(() => {
      setShowLogo(true);
    }, 300);

    // After animation completes, call onFinish
    const finishTimeout = setTimeout(() => {
      onFinish();
    }, 3000);

    return () => {
      clearTimeout(animationTimeout);
      clearTimeout(finishTimeout);
    };
  }, [onFinish]);

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black z-50">
      <div className="relative flex flex-col items-center justify-center">
        {/* Logo animation */}
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ 
            opacity: showLogo ? 1 : 0, 
            scale: showLogo ? 1 : 0.5,
            y: showLogo ? 0 : 20
          }}
          transition={{ 
            duration: 0.8, 
            ease: "easeOut"
          }}
          className="relative"
        >
          <img 
            src={waaboPng} 
            alt="Waabo" 
            className="w-64 h-auto"
          />
        </motion.div>
        
        {/* Loading indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: showLogo ? 1 : 0 }}
          transition={{ delay: 0.5, duration: 0.3 }}
          className="mt-8"
        >
          <div className="flex space-x-2 justify-center items-center">
            <div className="h-2 w-2 bg-green-600 rounded-full animate-bounce"></div>
            <div className="h-2 w-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
            <div className="h-2 w-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default SplashScreen;