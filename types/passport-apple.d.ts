declare module 'passport-apple' {
  import { Request } from 'express';
  import { Strategy as PassportStrategy } from 'passport';

  export interface AppleStrategyOptions {
    clientID: string;
    teamID: string;
    keyID: string;
    privateKeyString?: string;
    privateKeyPath?: string;
    callbackURL: string;
    passReqToCallback?: boolean;
    scope?: string[];
    authorizationURL?: string;
    tokenURL?: string;
    state?: boolean | string;
  }

  export interface VerifyCallback {
    (
      req: Request, 
      accessToken: string, 
      refreshToken: string, 
      idToken: string, 
      profile: any, 
      done: (error: any, user?: any, info?: any) => void
    ): void;
  }

  export class Strategy extends PassportStrategy {
    constructor(options: AppleStrategyOptions, verify: VerifyCallback);
    name: string;
  }
}