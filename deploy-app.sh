#!/bin/bash

# Script pour déployer l'application Waabo sur Replit

# Couleurs pour les messages console
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher des messages
log() {
  echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[$(date '+%H:%M:%S')] ✓${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠${NC} $1"
}

log_error() {
  echo -e "${RED}[$(date '+%H:%M:%S')] ✗${NC} $1"
}

# Vérifier si l'environnement Replit est disponible
if [ -z "$REPL_ID" ]; then
  log_warning "Ce script est conçu pour être exécuté sur Replit."
fi

# Début du processus de déploiement
log "Démarrage du processus de déploiement de l'application Waabo"

# Étape 1: Construction de l'application
log "Étape 1: Construction de l'application web"
npm run build
if [ $? -ne 0 ]; then
  log_error "La construction de l'application a échoué."
  exit 1
fi
log_success "Application web construite avec succès"

# Étape 2: Mise à jour de la configuration Capacitor
log "Étape 2: Préparation de la configuration pour Replit"

# Ajout de la variable d'environnement PUBLIC_URL dans .env
if [ ! -f .env ]; then
  touch .env
fi

# Vérifier si PUBLIC_URL est déjà défini
if ! grep -q "PUBLIC_URL" .env; then
  echo "PUBLIC_URL=https://${REPL_SLUG}.${REPL_OWNER}.repl.co" >> .env
  log_success "Variable PUBLIC_URL ajoutée au fichier .env"
else
  log "Variable PUBLIC_URL déjà présente dans le fichier .env"
fi

# Étape 3: Synchronisation avec Capacitor
log "Étape 3: Synchronisation avec Capacitor"
npx cap sync
if [ $? -ne 0 ]; then
  log_error "La synchronisation avec Capacitor a échoué."
  exit 1
fi
log_success "Synchronisation avec Capacitor terminée avec succès"

# Finaliser
log_success "Processus de déploiement terminé avec succès!"
log "Pour déployer l'application sur Replit, cliquez sur le bouton 'Run' puis sur 'Deploy'"
log "Pour créer une version iOS, utilisez la commande 'npx cap add ios && npx cap sync ios'"