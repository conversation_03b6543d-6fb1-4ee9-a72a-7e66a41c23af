/**
 * Script pour construire et optimiser l'application Waabo
 * pour le déploiement sur Replit et la préparation pour iOS
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Couleurs pour les messages console
const colors = {
  reset: "\x1b[0m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  red: "\x1b[31m"
};

/**
 * Affiche un message dans la console
 */
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  
  switch (type) {
    case 'success':
      console.log(`${colors.green}[${timestamp}] ✓${colors.reset} ${message}`);
      break;
    case 'warning':
      console.log(`${colors.yellow}[${timestamp}] ⚠${colors.reset} ${message}`);
      break;
    case 'error':
      console.log(`${colors.red}[${timestamp}] ✗${colors.reset} ${message}`);
      break;
    default:
      console.log(`${colors.blue}[${timestamp}]${colors.reset} ${message}`);
  }
}

/**
 * Exécute une commande et affiche le résultat
 */
function execute(command, silent = false) {
  try {
    if (!silent) log(`Exécution: ${command}`);
    const output = execSync(command, { encoding: 'utf8' });
    if (!silent && output.trim()) {
      console.log(output.trim());
    }
    return { success: true, output };
  } catch (error) {
    log(`Erreur lors de l'exécution de: ${command}`, 'error');
    if (error.stdout) console.log(error.stdout.toString());
    if (error.stderr) console.error(error.stderr.toString());
    return { success: false, error };
  }
}

/**
 * Vérifie si un dossier existe, le crée sinon
 */
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    log(`Dossier créé: ${dir}`, 'success');
    return true;
  }
  return false;
}

/**
 * Processus principal de build
 */
async function build() {
  log("Démarrage du processus de construction de l'application Waabo");
  
  // Étape 1: Vérification de l'environnement
  log("Étape 1: Vérification de l'environnement");
  
  // Vérifier que nous sommes dans le bon répertoire
  const isReplitEnv = process.env.REPL_ID !== undefined;
  if (isReplitEnv) {
    log("Environnement Replit détecté", 'success');
  } else {
    log("Environnement local détecté", 'info');
  }
  
  // Étape 2: Construire l'application (frontend + backend)
  log("Étape 2: Construction de l'application web");
  const buildResult = execute('npm run build');
  if (!buildResult.success) {
    log("La construction de l'application a échoué.", 'error');
    return;
  }
  log("Application web construite avec succès", 'success');
  
  // Étape 3: Mise à jour du fichier .env
  log("Étape 3: Configuration des variables d'environnement");
  
  if (!fs.existsSync('.env')) {
    fs.writeFileSync('.env', '');
  }
  
  let envContent = fs.readFileSync('.env', 'utf8');
  
  // Ajouter PUBLIC_URL si nécessaire
  if (isReplitEnv && !envContent.includes('PUBLIC_URL')) {
    const publicUrl = `${process.env.REPL_SLUG}.${process.env.REPL_OWNER}.repl.co`;
    envContent += `\nPUBLIC_URL=${publicUrl}\n`;
    fs.writeFileSync('.env', envContent);
    log(`Variable PUBLIC_URL ajoutée: ${publicUrl}`, 'success');
  }
  
  // Étape 4: Préparation pour le déploiement mobile
  log("Étape 4: Préparation pour le déploiement mobile");
  
  // Synchroniser avec Capacitor
  log("Synchronisation avec Capacitor");
  const syncResult = execute('npx cap sync');
  if (!syncResult.success) {
    log("La synchronisation avec Capacitor a échoué.", 'error');
    log("Continuez malgré l'erreur car l'application web devrait fonctionner correctement.", 'warning');
  } else {
    log("Synchronisation avec Capacitor terminée avec succès", 'success');
  }
  
  // Finalisation
  log("Processus de construction terminé avec succès!", 'success');
  log("Pour lancer l'application, exécutez: npm run start");
  log("Pour déployer l'application sur Replit, cliquez sur le bouton 'Run' puis sur 'Deploy'");
}

// Lancer le processus de build
build().catch(error => {
  log(`Une erreur inattendue s'est produite: ${error.message}`, 'error');
  console.error(error);
});