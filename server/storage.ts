import { 
  users, type User, type InsertUser,
  orders, type Order, type InsertOrder, type OrderStatus,
  orderStatuses, type OrderStatusEntry, type InsertOrderStatus,
  products, type Product, type InsertProduct,
  notifications, type Notification, type InsertNotification,
  staticPages, type StaticPage, type InsertStaticPage,
  popupNotifications, type PopupNotification, type InsertPopupNotification,
  userSeenPopups,
  passwordResetTokens, type PasswordResetToken, type InsertPasswordResetToken,
  emailVerificationTokens, type EmailVerificationToken, type InsertEmailVerificationToken,
  appSettings, type AppSetting, type InsertAppSetting,
  savedOrders, type SavedOrder, type InsertSavedOrder,
  contactMessages, type ContactMessage, type InsertContactMessage
} from "@shared/schema";
import { db } from './db';
import { and, eq, or, gte, lte, isNull, inArray, desc } from 'drizzle-orm';
import { randomBytes } from 'crypto';

import session from "express-session";
import createMemoryStore from "memorystore";

const MemoryStore = createMemoryStore(session);

export interface IStorage {
  // Session store
  sessionStore: session.Store;
  
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByProvider(provider: string, providerId: string): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updateData: Partial<User>): Promise<User | undefined>;
  deleteUser(id: number): Promise<boolean>;
  
  // Order operations
  getOrder(id: number): Promise<Order | undefined>;
  getOrderByReference(reference: string | null): Promise<Order | undefined>;
  getOrdersByUserId(userId: number | null): Promise<Order[]>;
  getAllOrders(): Promise<Order[]>;
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrderStatus(id: number, status: OrderStatus, latestUpdate?: string): Promise<Order | undefined>;
  
  // Saved orders operations
  saveOrder(userId: number, orderId: number): Promise<SavedOrder>;
  getSavedOrdersForUser(userId: number): Promise<Order[]>;
  removeSavedOrder(userId: number, orderId: number): Promise<boolean>;
  isSavedOrder(userId: number, orderId: number): Promise<boolean>;
  
  // Order status operations
  getAllOrderStatuses(): Promise<OrderStatusEntry[]>;
  getActiveOrderStatuses(): Promise<OrderStatusEntry[]>;
  getOrderStatus(id: number): Promise<OrderStatusEntry | undefined>;
  getOrderStatusByCode(code: string): Promise<OrderStatusEntry | undefined>;
  createOrderStatus(status: InsertOrderStatus): Promise<OrderStatusEntry>;
  updateOrderStatusEntry(id: number, status: Partial<InsertOrderStatus>): Promise<OrderStatusEntry | undefined>;
  deleteOrderStatus(id: number): Promise<boolean>;
  
  // Product operations
  getProduct(id: number): Promise<Product | undefined>;
  getAllProducts(): Promise<Product[]>;
  createProduct(product: InsertProduct): Promise<Product>;
  updateProduct(id: number, product: Partial<InsertProduct>): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<boolean>;
  
  // Notification operations
  createNotification(notification: InsertNotification): Promise<Notification>;
  getNotificationsForUser(userId: number): Promise<Notification[]>;
  getAllGlobalNotifications(): Promise<Notification[]>;
  markNotificationAsRead(id: number): Promise<Notification | undefined>;
  
  // Popup notification operations
  getPopupNotification(id: number): Promise<PopupNotification | undefined>;
  getAllPopupNotifications(): Promise<PopupNotification[]>;
  getActivePopupNotifications(): Promise<PopupNotification[]>;
  createPopupNotification(popup: InsertPopupNotification): Promise<PopupNotification>;
  updatePopupNotification(id: number, popup: Partial<InsertPopupNotification>): Promise<PopupNotification | undefined>;
  deletePopupNotification(id: number): Promise<boolean>;
  markPopupAsSeen(userId: number, popupId: number): Promise<void>;
  hasUserSeenPopup(userId: number, popupId: number): Promise<boolean>;
  getUnseenPopupsForUser(userId: number): Promise<PopupNotification[]>;
  
  // Static page operations
  getStaticPage(slug: string): Promise<StaticPage | undefined>;
  getAllStaticPages(): Promise<StaticPage[]>;
  createStaticPage(page: InsertStaticPage): Promise<StaticPage>;
  updateStaticPage(slug: string, page: Partial<InsertStaticPage>): Promise<StaticPage | undefined>;
  
  // Password reset operations
  createPasswordResetToken(userId: number): Promise<PasswordResetToken>;
  getPasswordResetTokenByToken(token: string): Promise<PasswordResetToken | undefined>;
  validatePasswordResetToken(token: string): Promise<boolean>;
  markPasswordResetTokenAsUsed(token: string): Promise<boolean>;
  
  // Email verification operations
  createEmailVerificationToken(userId: number): Promise<EmailVerificationToken>;
  getEmailVerificationTokenByToken(token: string): Promise<EmailVerificationToken | undefined>;
  validateEmailVerificationToken(token: string): Promise<boolean>;
  markEmailVerificationTokenAsUsed(token: string): Promise<boolean>;
  markUserEmailAsVerified(userId: number): Promise<User | undefined>;
  
  // Application settings operations
  getAppSetting(key: string): Promise<AppSetting | undefined>;
  getAllAppSettings(): Promise<AppSetting[]>;
  createOrUpdateAppSetting(key: string, value: string, description?: string): Promise<AppSetting>;
  
  // Contact messages operations
  createContactMessage(message: InsertContactMessage): Promise<ContactMessage>;
  getAllContactMessages(): Promise<ContactMessage[]>;
  getUnreadContactMessages(): Promise<ContactMessage[]>;
  getContactMessage(id: number): Promise<ContactMessage | undefined>;
  markContactMessageAsRead(id: number): Promise<ContactMessage | undefined>;
  deleteContactMessage(id: number): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  sessionStore: session.Store;
  
  constructor() {
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // 24 heures
    });
  }
  

  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    if (!email) return undefined;
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async getUserByProvider(provider: string, providerId: string): Promise<User | undefined> {
    if (!provider || !providerId) return undefined;
    const [user] = await db.select().from(users).where(
      and(
        eq(users.provider, provider),
        eq(users.providerId, providerId)
      )
    );
    return user;
  }

  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    // Make sure required fields are not null
    const userToInsert: InsertUser = {
      ...insertUser,
      email: insertUser.email ?? null,
      password: insertUser.password ?? null,
      provider: insertUser.provider ?? null,
      providerId: insertUser.providerId ?? null
    };
    
    const [user] = await db.insert(users).values(userToInsert).returning();
    return user;
  }
  
  async updateUser(id: number, updateData: Partial<User>): Promise<User | undefined> {
    try {
      // Vérifier si l'utilisateur existe
      const user = await this.getUser(id);
      if (!user) {
        return undefined;
      }
      
      console.log('Updating user with data:', updateData);
      
      const [updatedUser] = await db
        .update(users)
        .set({
          ...updateData,
          updatedAt: new Date()
        })
        .where(eq(users.id, id))
        .returning();
      
      console.log('User updated successfully:', updatedUser);
      return updatedUser;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }
  
  async deleteUser(id: number): Promise<boolean> {
    try {
      // Vérifier si l'utilisateur existe
      const user = await this.getUser(id);
      if (!user) {
        return false;
      }
      
      // Transaction pour supprimer toutes les données associées à l'utilisateur
      await db.transaction(async (tx) => {
        // 1. Supprimer les tokens de réinitialisation de mot de passe
        await tx.delete(passwordResetTokens).where(eq(passwordResetTokens.userId, id));
        
        // 2. Supprimer les associations utilisateur-popup vues
        await tx.delete(userSeenPopups).where(eq(userSeenPopups.userId, id));
        
        // 3. Supprimer les notifications ciblées pour cet utilisateur
        await tx.delete(notifications).where(eq(notifications.targetUserId, id));
        
        // 4. Supprimer les commandes de l'utilisateur
        await tx.delete(orders).where(eq(orders.userId, id));
        
        // 5. Enfin, supprimer l'utilisateur lui-même
        await tx.delete(users).where(eq(users.id, id));
      });
      
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }

  async getOrder(id: number): Promise<Order | undefined> {
    const [order] = await db.select().from(orders).where(eq(orders.id, id));
    return order;
  }

  async getOrderByReference(reference: string | null): Promise<Order | undefined> {
    if (!reference) return undefined;
    const [order] = await db.select().from(orders).where(eq(orders.reference, reference));
    return order;
  }

  async getOrdersByUserId(userId: number | null): Promise<Order[]> {
    if (userId === null || userId === undefined) {
      // Si userId est null, récupérer toutes les commandes sans userId
      return await db.select().from(orders).where(eq(orders.userId, null as unknown as number));
    }
    return await db.select().from(orders).where(eq(orders.userId, userId));
  }
  
  async getAllOrders(): Promise<Order[]> {
    return await db.select().from(orders);
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    // Ensure required fields are not undefined
    const orderToInsert: InsertOrder = {
      ...insertOrder,
      status: insertOrder.status ?? 'ORDER_CONFIRMED', // Provide default if missing
      userId: insertOrder.userId ?? null,
      estimatedDelivery: insertOrder.estimatedDelivery ?? null,
      latestUpdate: insertOrder.latestUpdate ?? null,
      latestUpdateTime: insertOrder.latestUpdateTime ?? null
    };
    
    const [order] = await db.insert(orders).values(orderToInsert).returning();
    return order;
  }

  async updateOrderStatus(id: number, status: OrderStatus, latestUpdate?: string): Promise<Order | undefined> {
    const now = new Date();
    const updateData: Record<string, any> = {
      status,
      updatedAt: now
    };

    if (latestUpdate) {
      updateData.latestUpdate = latestUpdate;
      updateData.latestUpdateTime = now;
    }

    const [updatedOrder] = await db
      .update(orders)
      .set(updateData)
      .where(eq(orders.id, id))
      .returning();

    return updatedOrder;
  }
  
  async saveOrder(userId: number, orderId: number): Promise<SavedOrder> {
    // Vérifier si la commande est déjà sauvegardée
    const alreadySaved = await this.isSavedOrder(userId, orderId);
    if (alreadySaved) {
      const [existingSaved] = await db
        .select()
        .from(savedOrders)
        .where(
          and(
            eq(savedOrders.userId, userId),
            eq(savedOrders.orderId, orderId)
          )
        );
      return existingSaved;
    }
    
    // Sauvegarder la commande
    const [savedOrder] = await db
      .insert(savedOrders)
      .values({
        userId: userId,
        orderId: orderId
      })
      .returning();
      
    return savedOrder;
  }
  
  async getSavedOrdersForUser(userId: number): Promise<Order[]> {
    // Récupérer tous les ordres sauvegardés pour l'utilisateur
    const savedOrderRecords = await db
      .select()
      .from(savedOrders)
      .where(eq(savedOrders.userId, userId));
    
    if (!savedOrderRecords.length) {
      return [];
    }
    
    // Récupérer les détails complets des commandes
    const orderIds = savedOrderRecords.map(record => record.orderId);
    const savedOrderDetails = await db
      .select()
      .from(orders)
      .where(inArray(orders.id, orderIds));
      
    return savedOrderDetails;
  }
  
  async removeSavedOrder(userId: number, orderId: number): Promise<boolean> {
    try {
      const result = await db
        .delete(savedOrders)
        .where(
          and(
            eq(savedOrders.userId, userId),
            eq(savedOrders.orderId, orderId)
          )
        );
      
      return true;
    } catch (error) {
      console.error('Error removing saved order:', error);
      return false;
    }
  }
  
  async isSavedOrder(userId: number, orderId: number): Promise<boolean> {
    const [savedOrder] = await db
      .select()
      .from(savedOrders)
      .where(
        and(
          eq(savedOrders.userId, userId),
          eq(savedOrders.orderId, orderId)
        )
      );
      
    return !!savedOrder;
  }

  async getProduct(id: number): Promise<Product | undefined> {
    const [product] = await db.select().from(products).where(eq(products.id, id));
    return product;
  }

  async getAllProducts(): Promise<Product[]> {
    return await db.select().from(products);
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    // Ensure required fields are not undefined
    const productToInsert: InsertProduct = {
      ...insertProduct,
      hasFreeshipping: insertProduct.hasFreeshipping ?? false,
      features: insertProduct.features ?? [],
      specifications: insertProduct.specifications ?? {}
    };
    
    const [product] = await db.insert(products).values(productToInsert).returning();
    return product;
  }

  async updateProduct(id: number, productUpdate: Partial<InsertProduct>): Promise<Product | undefined> {
    const [updatedProduct] = await db
      .update(products)
      .set({
        ...productUpdate,
        updatedAt: new Date()
      })
      .where(eq(products.id, id))
      .returning();

    return updatedProduct;
  }

  async deleteProduct(id: number): Promise<boolean> {
    try {
      await db.delete(products).where(eq(products.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting product:", error);
      return false;
    }
  }

  async createNotification(insertNotification: InsertNotification): Promise<Notification> {
    // Ensure required fields are not undefined
    const notificationToInsert: InsertNotification = {
      ...insertNotification,
      targetUserId: insertNotification.targetUserId ?? null,
      isGlobal: insertNotification.isGlobal ?? false
    };
    
    const [notification] = await db.insert(notifications).values(notificationToInsert).returning();
    return notification;
  }

  async getNotificationsForUser(userId: number): Promise<Notification[]> {
    try {
      // Récupérer d'abord la date d'inscription de l'utilisateur
      const user = await this.getUser(userId);
      if (!user || !user.createdAt) {
        return [];
      }
      
      // Récupérer toutes les notifications ciblées pour cet utilisateur ou globales
      // mais seulement celles créées après l'inscription de l'utilisateur
      return await db.select()
        .from(notifications)
        .where(
          and(
            or(
              eq(notifications.targetUserId, userId),
              eq(notifications.isGlobal, true)
            ),
            // S'assurer que la notification a été envoyée après l'inscription de l'utilisateur
            gte(notifications.sentAt, user.createdAt)
          )
        );
    } catch (error) {
      console.error("Error getting notifications for user:", error);
      return [];
    }
  }

  async getAllGlobalNotifications(): Promise<Notification[]> {
    return await db.select().from(notifications).where(eq(notifications.isGlobal, true));
  }
  
  async markNotificationAsRead(id: number): Promise<Notification | undefined> {
    try {
      const [notification] = await db
        .update(notifications)
        .set({ isRead: true })
        .where(eq(notifications.id, id))
        .returning();
      
      return notification;
    } catch (error) {
      console.error("Error marking notification as read:", error);
      return undefined;
    }
  }
  
  async deleteNotificationsByTitleAndUser(titles: string[], userId: number): Promise<boolean> {
    try {
      await db
        .delete(notifications)
        .where(
          and(
            inArray(notifications.title, titles),
            eq(notifications.targetUserId, userId)
          )
        );
      
      console.log(`Suppression des notifications avec les titres [${titles.join(', ')}] pour l'utilisateur ${userId}`);
      return true;
    } catch (error) {
      console.error("Error deleting notifications by title and user:", error);
      return false;
    }
  }

  // Static page operations
  async getStaticPage(slug: string): Promise<StaticPage | undefined> {
    try {
      const [page] = await db.select().from(staticPages).where(eq(staticPages.slug, slug));
      return page;
    } catch (error) {
      console.error(`Error getting static page with slug ${slug}:`, error);
      return undefined;
    }
  }

  async getAllStaticPages(): Promise<StaticPage[]> {
    try {
      return await db.select().from(staticPages);
    } catch (error) {
      console.error("Error getting all static pages:", error);
      return [];
    }
  }

  async createStaticPage(page: InsertStaticPage): Promise<StaticPage> {
    try {
      const [createdPage] = await db.insert(staticPages).values(page).returning();
      return createdPage;
    } catch (error) {
      console.error("Error creating static page:", error);
      throw error;
    }
  }

  async updateStaticPage(slug: string, page: Partial<InsertStaticPage>): Promise<StaticPage | undefined> {
    try {
      const [updatedPage] = await db
        .update(staticPages)
        .set({
          ...page,
          lastUpdated: new Date()
        })
        .where(eq(staticPages.slug, slug))
        .returning();
      
      return updatedPage;
    } catch (error) {
      console.error(`Error updating static page with slug ${slug}:`, error);
      return undefined;
    }
  }

  // Order Status Operations
  async getAllOrderStatuses(): Promise<OrderStatusEntry[]> {
    return await db.select().from(orderStatuses).orderBy(orderStatuses.displayOrder);
  }

  async getActiveOrderStatuses(): Promise<OrderStatusEntry[]> {
    return await db.select().from(orderStatuses)
      .where(eq(orderStatuses.isActive, true))
      .orderBy(orderStatuses.displayOrder);
  }

  async getOrderStatus(id: number): Promise<OrderStatusEntry | undefined> {
    const [status] = await db.select().from(orderStatuses).where(eq(orderStatuses.id, id));
    return status;
  }

  async getOrderStatusByCode(code: string): Promise<OrderStatusEntry | undefined> {
    const [status] = await db.select().from(orderStatuses).where(eq(orderStatuses.code, code));
    return status;
  }

  async createOrderStatus(insertStatus: InsertOrderStatus): Promise<OrderStatusEntry> {
    const [status] = await db.insert(orderStatuses).values(insertStatus).returning();
    return status;
  }

  async updateOrderStatusEntry(id: number, statusUpdate: Partial<InsertOrderStatus>): Promise<OrderStatusEntry | undefined> {
    const [updatedStatus] = await db
      .update(orderStatuses)
      .set({
        ...statusUpdate,
        updatedAt: new Date()
      })
      .where(eq(orderStatuses.id, id))
      .returning();
    
    return updatedStatus;
  }

  async deleteOrderStatus(id: number): Promise<boolean> {
    try {
      await db.delete(orderStatuses).where(eq(orderStatuses.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting order status:", error);
      return false;
    }
  }

  // Popup Notification Operations
  async getPopupNotification(id: number): Promise<PopupNotification | undefined> {
    try {
      const [popup] = await db.select().from(popupNotifications).where(eq(popupNotifications.id, id));
      return popup;
    } catch (error) {
      console.error("Error getting popup notification:", error);
      return undefined;
    }
  }

  async getAllPopupNotifications(): Promise<PopupNotification[]> {
    try {
      return await db.select().from(popupNotifications);
    } catch (error) {
      console.error("Error getting all popup notifications:", error);
      return [];
    }
  }

  async getActivePopupNotifications(): Promise<PopupNotification[]> {
    try {
      const now = new Date();
      
      // Récupérer les popups qui sont actifs
      // ET qui n'ont pas de date de début OU dont la date de début est passée
      // ET qui n'ont pas de date de fin OU dont la date de fin n'est pas encore passée
      const activePopups = await db.select()
        .from(popupNotifications)
        .where(
          and(
            eq(popupNotifications.isActive, true),
            or(
              isNull(popupNotifications.startDate),
              lte(popupNotifications.startDate, now)
            ),
            or(
              isNull(popupNotifications.endDate),
              gte(popupNotifications.endDate, now)
            )
          )
        );
      
      console.log(`Trouvé ${activePopups.length} popups actives`);
      return activePopups;
    } catch (error) {
      console.error("Error getting active popup notifications:", error);
      return [];
    }
  }

  async createPopupNotification(popup: InsertPopupNotification): Promise<PopupNotification> {
    try {
      const [createdPopup] = await db.insert(popupNotifications).values(popup).returning();
      return createdPopup;
    } catch (error) {
      console.error("Error creating popup notification:", error);
      throw error;
    }
  }

  async updatePopupNotification(id: number, popup: Partial<InsertPopupNotification>): Promise<PopupNotification | undefined> {
    try {
      const [updatedPopup] = await db
        .update(popupNotifications)
        .set({
          ...popup,
          updatedAt: new Date()
        })
        .where(eq(popupNotifications.id, id))
        .returning();
      return updatedPopup;
    } catch (error) {
      console.error("Error updating popup notification:", error);
      return undefined;
    }
  }

  async deletePopupNotification(id: number): Promise<boolean> {
    try {
      await db.delete(popupNotifications).where(eq(popupNotifications.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting popup notification:", error);
      return false;
    }
  }

  async markPopupAsSeen(userId: number, popupId: number): Promise<void> {
    try {
      await db.insert(userSeenPopups).values({
        userId,
        popupId,
        seenAt: new Date()
      });
    } catch (error) {
      console.error("Error marking popup as seen:", error);
    }
  }

  async hasUserSeenPopup(userId: number, popupId: number): Promise<boolean> {
    try {
      const [seen] = await db.select()
        .from(userSeenPopups)
        .where(
          and(
            eq(userSeenPopups.userId, userId),
            eq(userSeenPopups.popupId, popupId)
          )
        );
      return !!seen;
    } catch (error) {
      console.error("Error checking if user has seen popup:", error);
      return false;
    }
  }

  async getUnseenPopupsForUser(userId: number): Promise<PopupNotification[]> {
    try {
      // Get all active popups
      const activePopups = await this.getActivePopupNotifications();
      
      // Get IDs of popups seen by this user
      const seenPopupRows = await db.select()
        .from(userSeenPopups)
        .where(eq(userSeenPopups.userId, userId));
      
      const seenPopupIds = new Set(seenPopupRows.map(row => row.popupId));
      
      // Filter out popups that the user has already seen
      return activePopups.filter(popup => !seenPopupIds.has(popup.id));
    } catch (error) {
      console.error("Error getting unseen popups for user:", error);
      return [];
    }
  }

  // Password reset token operations
  async createPasswordResetToken(userId: number): Promise<PasswordResetToken> {
    try {
      // Générer un token aléatoire
      const token = randomBytes(32).toString('hex');
      
      // Définir la date d'expiration (1 heure)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);
      
      // Insérer le token en base de données
      const [resetToken] = await db.insert(passwordResetTokens).values({
        userId,
        token,
        expiresAt,
      }).returning();
      
      return resetToken;
    } catch (error) {
      console.error(`Error creating password reset token for user ${userId}:`, error);
      throw error;
    }
  }
  
  async getPasswordResetTokenByToken(token: string): Promise<PasswordResetToken | undefined> {
    try {
      const [resetToken] = await db
        .select()
        .from(passwordResetTokens)
        .where(eq(passwordResetTokens.token, token));
        
      return resetToken;
    } catch (error) {
      console.error(`Error getting password reset token:`, error);
      return undefined;
    }
  }
  
  async validatePasswordResetToken(token: string): Promise<boolean> {
    try {
      const resetToken = await this.getPasswordResetTokenByToken(token);
      
      if (!resetToken) {
        return false;
      }
      
      // Vérifier si le token a déjà été utilisé
      if (resetToken.used) {
        return false;
      }
      
      // Vérifier si le token n'a pas expiré
      const now = new Date();
      if (resetToken.expiresAt < now) {
        return false;
      }
      
      return true;
    } catch (error) {
      console.error(`Error validating password reset token:`, error);
      return false;
    }
  }
  
  async markPasswordResetTokenAsUsed(token: string): Promise<boolean> {
    try {
      const [updatedToken] = await db
        .update(passwordResetTokens)
        .set({ used: true })
        .where(eq(passwordResetTokens.token, token))
        .returning();
      
      return !!updatedToken;
    } catch (error) {
      console.error(`Error marking password reset token as used:`, error);
      return false;
    }
  }
  
  // Email verification methods
  async createEmailVerificationToken(userId: number): Promise<EmailVerificationToken> {
    try {
      // Générer un token aléatoire
      const token = randomBytes(32).toString('hex');
      
      // Définir la date d'expiration (48 heures)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 48);
      
      // Insérer le token en base de données
      const [verificationToken] = await db.insert(emailVerificationTokens).values({
        userId,
        token,
        expiresAt,
      }).returning();
      
      return verificationToken;
    } catch (error) {
      console.error(`Error creating email verification token for user ${userId}:`, error);
      throw error;
    }
  }
  
  async getEmailVerificationTokenByToken(token: string): Promise<EmailVerificationToken | undefined> {
    try {
      const [verificationToken] = await db
        .select()
        .from(emailVerificationTokens)
        .where(eq(emailVerificationTokens.token, token));
        
      return verificationToken;
    } catch (error) {
      console.error(`Error getting email verification token:`, error);
      return undefined;
    }
  }
  
  async validateEmailVerificationToken(token: string): Promise<boolean> {
    try {
      const verificationToken = await this.getEmailVerificationTokenByToken(token);
      
      if (!verificationToken) {
        return false;
      }
      
      // Vérifier si le token a déjà été utilisé
      if (verificationToken.used) {
        return false;
      }
      
      // Vérifier si le token n'a pas expiré
      const now = new Date();
      if (verificationToken.expiresAt < now) {
        return false;
      }
      
      return true;
    } catch (error) {
      console.error(`Error validating email verification token:`, error);
      return false;
    }
  }
  
  async markEmailVerificationTokenAsUsed(token: string): Promise<boolean> {
    try {
      const [updatedToken] = await db
        .update(emailVerificationTokens)
        .set({ used: true })
        .where(eq(emailVerificationTokens.token, token))
        .returning();
      
      return !!updatedToken;
    } catch (error) {
      console.error(`Error marking email verification token as used:`, error);
      return false;
    }
  }
  
  async markUserEmailAsVerified(userId: number): Promise<User | undefined> {
    try {
      const [updatedUser] = await db
        .update(users)
        .set({ 
          emailVerified: true,
          updatedAt: new Date() 
        })
        .where(eq(users.id, userId))
        .returning();
      
      return updatedUser;
    } catch (error) {
      console.error(`Error marking user email as verified:`, error);
      return undefined;
    }
  }

  // Application Settings Operations
  async getAppSetting(key: string): Promise<AppSetting | undefined> {
    try {
      const [setting] = await db.select().from(appSettings).where(eq(appSettings.key, key));
      return setting;
    } catch (error) {
      console.error(`Error getting app setting with key ${key}:`, error);
      return undefined;
    }
  }

  async getAllAppSettings(): Promise<AppSetting[]> {
    try {
      return await db.select().from(appSettings);
    } catch (error) {
      console.error("Error getting all app settings:", error);
      return [];
    }
  }

  async createOrUpdateAppSetting(key: string, value: string, description?: string): Promise<AppSetting> {
    try {
      // Vérifier si le paramètre existe déjà
      const existingSetting = await this.getAppSetting(key);
      
      if (existingSetting) {
        // Mise à jour du paramètre existant
        const [updatedSetting] = await db
          .update(appSettings)
          .set({
            value,
            description: description ?? existingSetting.description,
            updatedAt: new Date()
          })
          .where(eq(appSettings.key, key))
          .returning();
        
        return updatedSetting;
      } else {
        // Création d'un nouveau paramètre
        const [newSetting] = await db
          .insert(appSettings)
          .values({
            key,
            value,
            description: description ?? null
          })
          .returning();
        
        return newSetting;
      }
    } catch (error) {
      console.error(`Error creating/updating app setting with key ${key}:`, error);
      throw error;
    }
  }

  // Implémentation des méthodes pour les messages de contact
  async createContactMessage(message: InsertContactMessage): Promise<ContactMessage> {
    try {
      const [newMessage] = await db
        .insert(contactMessages)
        .values(message)
        .returning();
        
      return newMessage;
    } catch (error) {
      console.error('Error creating contact message:', error);
      throw error;
    }
  }
  
  async getAllContactMessages(): Promise<ContactMessage[]> {
    try {
      // Récupérer tous les messages triés par date (plus récents en premier)
      return await db
        .select()
        .from(contactMessages)
        .orderBy(desc(contactMessages.createdAt));
    } catch (error) {
      console.error('Error fetching all contact messages:', error);
      return [];
    }
  }
  
  async getUnreadContactMessages(): Promise<ContactMessage[]> {
    try {
      // Récupérer uniquement les messages non lus
      return await db
        .select()
        .from(contactMessages)
        .where(eq(contactMessages.isRead, false))
        .orderBy(desc(contactMessages.createdAt));
    } catch (error) {
      console.error('Error fetching unread contact messages:', error);
      return [];
    }
  }
  
  async getContactMessage(id: number): Promise<ContactMessage | undefined> {
    try {
      const [message] = await db
        .select()
        .from(contactMessages)
        .where(eq(contactMessages.id, id));
        
      return message;
    } catch (error) {
      console.error(`Error fetching contact message with id ${id}:`, error);
      return undefined;
    }
  }
  
  async markContactMessageAsRead(id: number): Promise<ContactMessage | undefined> {
    try {
      const [updated] = await db
        .update(contactMessages)
        .set({ isRead: true })
        .where(eq(contactMessages.id, id))
        .returning();
        
      return updated;
    } catch (error) {
      console.error(`Error marking contact message ${id} as read:`, error);
      return undefined;
    }
  }
  
  async deleteContactMessage(id: number): Promise<boolean> {
    try {
      await db
        .delete(contactMessages)
        .where(eq(contactMessages.id, id));
        
      return true;
    } catch (error) {
      console.error(`Error deleting contact message ${id}:`, error);
      return false;
    }
  }
}

// Initialize database with demo data
async function initDemoData(storage: IStorage) {
  // Initialize admin user if it doesn't exist
  try {
    const adminUser = await storage.getUserByEmail("<EMAIL>");
    if (!adminUser) {
      const { hashPassword } = await import("./auth");
      const hashedPassword = await hashPassword("Admin123!");
      await storage.createUser({
        username: "admin",
        email: "<EMAIL>",
        password: hashedPassword,
      });
      console.log("Admin user created successfully");
    }
  } catch (error) {
    console.error("Failed to initialize admin user:", error);
  }

  // Initialize demo order statuses
  try {
    const existingStatuses = await storage.getAllOrderStatuses();
    
    // Only add demo statuses if none exist
    if (existingStatuses.length === 0) {
      const demoOrderStatuses: InsertOrderStatus[] = [
        { code: "ORDER_CONFIRMED", label: "Commande Confirmée", displayOrder: 1, isActive: true },
        { code: "PROCESSING", label: "En Traitement", displayOrder: 2, isActive: true },
        { code: "IN_TRANSIT", label: "En Transit", displayOrder: 3, isActive: true },
        { code: "ARRIVED", label: "Arrivée au Burkina", displayOrder: 4, isActive: true },
        { code: "DELIVERED", label: "Livrée", displayOrder: 5, isActive: true },
      ];
      
      for (const status of demoOrderStatuses) {
        await storage.createOrderStatus(status);
      }
      
      console.log("Demo order statuses initialized successfully");
    }
  } catch (error) {
    console.error("Failed to initialize demo order statuses:", error);
  }

  // Initialize default application settings
  try {
    // Ajouter le numéro WhatsApp par défaut
    const whatsappSetting = await storage.getAppSetting("whatsapp_number");
    if (!whatsappSetting) {
      await storage.createOrUpdateAppSetting(
        "whatsapp_number", 
        "+22670000000", 
        "Numéro WhatsApp affiché sur les pages de détail des produits"
      );
      console.log("WhatsApp number setting initialized successfully");
    }
  } catch (error) {
    console.error("Failed to initialize default app settings:", error);
  }

  // Initialize static pages
  try {
    const existingPages = await storage.getAllStaticPages();
    
    // Only add default static pages if none exist
    if (existingPages.length === 0) {
      const defaultStaticPages: InsertStaticPage[] = [
        {
          slug: "about",
          title: "À propos de Waabo",
          content: "Contenu à propos de Waabo - À remplir par l'administrateur"
        },
        {
          slug: "support",
          title: "Support",
          content: "Contenu de la page de support - À remplir par l'administrateur"
        },
        {
          slug: "contact",
          title: "Nous contacter",
          content: "Contenu de la page de contact - À remplir par l'administrateur"
        },
        {
          slug: "privacy",
          title: "Confidentialité",
          content: "Politique de confidentialité - À remplir par l'administrateur"
        }
      ];
      
      for (const page of defaultStaticPages) {
        await storage.createStaticPage(page);
      }
      
      console.log("Default static pages initialized successfully");
    }
  } catch (error) {
    console.error("Failed to initialize default static pages:", error);
  }

  // Initialize demo products
  const existingProducts = await storage.getAllProducts();
  
  // Only add demo products if none exist
  if (existingProducts.length === 0) {
    const demoProducts: InsertProduct[] = [
      {
        name: "Smart Watch Pro",
        description: "Fitness tracker with heart rate monitoring",
        price: "$89.99",
        imageUrl: "https://images.unsplash.com/photo-1523275335684-37898b6baf30",
        hasFreeshipping: true,
        features: ["1.3\" AMOLED HD touchscreen display", "24/7 heart rate & blood oxygen monitoring",
          "Sleep tracking with detailed analysis", "20+ sport modes with GPS tracking",
          "Up to 14 days battery life"],
        specifications: {
          Display: "1.3\" AMOLED",
          Battery: "420mAh",
          "Water Resistance": "5ATM (50m)",
          Compatibility: "Android 5.0+, iOS 10.0+"
        }
      },
      {
        name: "Wireless Earbuds",
        description: "Noise cancelling with 24h battery life",
        price: "$59.99",
        imageUrl: "https://images.unsplash.com/photo-**********-7041f2a55e12",
        hasFreeshipping: true,
        features: [],
        specifications: {}
      },
      {
        name: "20000mAh Power Bank",
        description: "Fast charging for multiple devices",
        price: "$45.99",
        imageUrl: "https://images.unsplash.com/photo-*************-7690c70c4eac",
        hasFreeshipping: true,
        features: [],
        specifications: {}
      },
      {
        name: "Portable Bluetooth Speaker",
        description: "Waterproof with 360° sound",
        price: "$79.99",
        imageUrl: "https://images.unsplash.com/photo-**********-9ebf69173e03",
        hasFreeshipping: true,
        features: [],
        specifications: {}
      },
      {
        name: "Premium Phone Case",
        description: "Shockproof protection with card holder",
        price: "$24.99",
        imageUrl: "https://images.unsplash.com/photo-*************-acd977736f90",
        hasFreeshipping: true,
        features: [],
        specifications: {}
      },
      {
        name: "Wireless Charging Pad",
        description: "Fast 15W charging for all devices",
        price: "$34.99",
        imageUrl: "https://images.unsplash.com/photo-1625772452859-1c03d5bf1137",
        hasFreeshipping: true,
        features: [],
        specifications: {}
      }
    ];

    for (const product of demoProducts) {
      await storage.createProduct(product);
    }
  }
}

// Create an instance of the DatabaseStorage
export const storage = new DatabaseStorage();

// Initialize demo data
initDemoData(storage).catch(err => {
  console.error("Failed to initialize demo data:", err);
});
