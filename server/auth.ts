import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Strategy as FacebookStrategy } from "passport-facebook";
import { Express } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { storage } from "./storage";
import { User as SchemaUser } from "@shared/schema";
import { pool } from './db';
import connectPgSimple from 'connect-pg-simple';
import { log } from "./vite";
import { setupAppleAuth } from "./apple-auth";
import { verifyJWTOrSession, getCurrentUser } from "./jwt-middleware";

const PostgresSessionStore = connectPgSimple(session);

declare global {
  namespace Express {
    interface User extends SchemaUser {}
  }
}

const scryptAsync = promisify(scrypt);

export async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export async function comparePasswords(supplied: string, stored: string | null) {
  if (!stored) return false;
  
  try {
    const [hashed, salt] = stored.split(".");
    const hashedBuf = Buffer.from(hashed, "hex");
    const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
    return timingSafeEqual(hashedBuf, suppliedBuf);
  } catch (error) {
    console.error('Error comparing passwords:', error);
    return false;
  }
}

/**
 * Génère un nom d'utilisateur unique basé sur le nom fourni
 * Ajoute un suffixe aléatoire et vérifie la disponibilité dans la base de données
 * 
 * @param baseName Le nom de base à utiliser pour générer le nom d'utilisateur
 * @returns Un nom d'utilisateur unique
 */
export async function generateUsername(baseName: string): Promise<string> {
  // Nettoyer le nom de base (retirer les espaces, caractères spéciaux, etc.)
  let baseUsername = baseName
    .toLowerCase()
    .replace(/[^\w\s]/gi, '') // Retirer les caractères spéciaux
    .replace(/\s+/g, '') // Retirer les espaces
    .slice(0, 15); // Limiter la longueur
  
  // Si le nom de base est vide après nettoyage, utiliser un fallback
  if (!baseUsername) {
    baseUsername = 'user';
  }
  
  // Génération d'un nombre aléatoire à 4 chiffres pour rendre le nom d'utilisateur unique
  const randomSuffix = Math.floor(1000 + Math.random() * 9000);
  let username = `${baseUsername}${randomSuffix}`;
  
  // Vérifier si le nom d'utilisateur existe déjà dans la base de données
  const existingUser = await storage.getUserByUsername(username);
  
  // Si le nom d'utilisateur existe déjà, générer un nouveau suffixe
  if (existingUser) {
    const newRandomSuffix = Math.floor(1000 + Math.random() * 9000);
    username = `${baseUsername}${newRandomSuffix}`;
  }
  
  return username;
}

export function setupAuth(app: Express) {
  // Créer la table de session si elle n'existe pas
  // La table sera automatiquement créée si createTableIfMissing est à true
  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET || "waabo-session-secret",
    resave: false,
    saveUninitialized: false,
    store: new PostgresSessionStore({
      pool,
      tableName: 'session',
      createTableIfMissing: true
    }),
    cookie: {
      maxAge: 1000 * 60 * 60 * 24 * 30, // 30 jours
      httpOnly: false, // Allow JavaScript access for mobile apps
      secure: false, // Disable for development/mobile
      sameSite: 'none', // Allow cross-origin cookies for mobile
      path: '/'
    },
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  // Configuration de l'authentification Apple
  if (process.env.APPLE_TEAM_ID && 
      process.env.APPLE_SERVICE_ID && 
      process.env.APPLE_KEY_ID && 
      process.env.APPLE_PRIVATE_KEY) {
    console.log('Configuration de l\'authentification Apple');
    setupAppleAuth(app, storage);
  } else {
    console.log('Authentification Apple désactivée (variables d\'environnement manquantes)');
  }

  // Facebook Strategy - seulement si les variables d'environnement sont définies
  if (process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
    console.log('Configuration de l\'authentification Facebook');
    // Détecter l'URL de base pour le callback
    const baseUrl = process.env.BASE_URL || 'https://waabo-app.com';
    const callbackURL = `${baseUrl}/api/auth/facebook/callback`;
    console.log(`Configuration Facebook avec URL de callback: ${callbackURL}`);
    console.log(`Facebook App ID: ${process.env.FACEBOOK_APP_ID?.substring(0, 5)}... (masqué pour sécurité)`);
    
    passport.use(new FacebookStrategy({
      clientID: process.env.FACEBOOK_APP_ID,
      clientSecret: process.env.FACEBOOK_APP_SECRET,
      callbackURL: callbackURL,
      profileFields: ['id', 'emails', 'name'],
      enableProof: true, // Activer la vérification de preuve pour plus de sécurité
      state: true // Activer la protection CSRF
    }, async (accessToken, refreshToken, profile, done) => {
      try {
        const email = profile.emails?.[0]?.value;
        if (!email) {
          return done(null, false, { message: "Email not provided by Facebook" });
        }

        let user = await storage.getUserByEmail(email);
        
        if (!user) {
          user = await storage.createUser({
            email,
            username: email.split('@')[0],
            firstName: profile.name?.givenName,
            lastName: profile.name?.familyName,
            provider: 'facebook',
            providerId: profile.id
          });
        }

        return done(null, user);
      } catch (error) {
        return done(error);
      }
    }));
  } else {
    console.log('Authentification Facebook désactivée (variables d\'environnement manquantes)');
  }

  passport.use(
    new LocalStrategy(
      {
        usernameField: "email", // Conservons ce nom pour compatibilité, même s'il contient email ou username
      },
      async (identifier, password, done) => {
        try {
          log(`Tentative de connexion pour: ${identifier}`, "auth");
          
          // Rechercher d'abord par email
          let user = await storage.getUserByEmail(identifier);
          
          // Si l'utilisateur n'est pas trouvé par email, essayer par nom d'utilisateur
          if (!user) {
            user = await storage.getUserByUsername(identifier);
          }
          
          // Si toujours pas trouvé, retourner une erreur
          if (!user) {
            log(`Aucun utilisateur trouvé avec l'identifiant: ${identifier}`, "auth");
            return done(null, false, { message: "Identifiant ou mot de passe incorrect" });
          }
          
          // Vérification que le mot de passe est défini
          if (!user.password) {
            log(`Utilisateur sans mot de passe: ${identifier}`, "auth-error");
            return done(null, false, { message: "Compte invalide. Veuillez réinitialiser votre mot de passe." });
          }
          
          const isPasswordValid = await comparePasswords(password, user.password);
          
          if (!isPasswordValid) {
            log(`Mot de passe invalide pour: ${identifier}`, "auth");
            return done(null, false, { message: "Identifiant ou mot de passe incorrect" });
          }
          
          log(`Connexion réussie pour: ${identifier}`, "auth");
          return done(null, user);
        } catch (error) {
          log(`Erreur d'authentification: ${error}`, "auth-error");
          return done(error);
        }
      }
    )
  );

  passport.serializeUser((user, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (error) {
      done(error);
    }
  });

  // Route d'inscription
  app.post("/api/auth/register", async (req, res) => {
    try {
      // Vérification si l'utilisateur existe déjà
      const existingUserByEmail = await storage.getUserByEmail(req.body.email);
      if (existingUserByEmail) {
        return res.status(400).json({ message: "Cet email est déjà utilisé" });
      }

      // Génération automatique du nom d'utilisateur basé sur le prénom, le nom et un suffixe aléatoire
      let baseUsername = '';
      if (req.body.firstName && req.body.lastName) {
        // Prendre la première lettre du prénom et le nom complet sans espaces
        baseUsername = (req.body.firstName.charAt(0) + req.body.lastName).toLowerCase().replace(/\s+/g, '');
      } else {
        // Utiliser la partie avant @ de l'email comme solution de repli
        baseUsername = req.body.email.split('@')[0].toLowerCase();
      }
      
      // Génération d'un nombre aléatoire à 4 chiffres pour rendre le nom d'utilisateur unique
      const randomSuffix = Math.floor(1000 + Math.random() * 9000);
      const username = `${baseUsername}${randomSuffix}`;
      
      // Création du nouvel utilisateur
      const hashedPassword = await hashPassword(req.body.password);
      const user = await storage.createUser({
        ...req.body,
        username,
        password: hashedPassword,
      });

      // Connexion automatique après inscription
      req.login(user, (err) => {
        if (err) {
          log(`Erreur après inscription: ${err}`, "auth-error");
          return res.status(500).json({ message: "Erreur lors de la connexion automatique" });
        }
        
        // Ne pas renvoyer le mot de passe au client
        const { password, ...userWithoutPassword } = user;
        res.status(201).json(userWithoutPassword);
      });
    } catch (error) {
      log(`Erreur d'inscription: ${error}`, "auth-error");
      res.status(500).json({ message: "Erreur lors de l'inscription" });
    }
  });

  // Route de connexion
  app.post("/api/auth/login", (req, res, next) => {
    passport.authenticate("local", (err: Error | null, user: Express.User | false, info: { message?: string } = {}) => {
      if (err) {
        log(`Erreur lors de l'authentification: ${err}`, "auth-error");
        return next(err);
      }
      
      if (!user) {
        return res.status(401).json({ message: info.message || "Identifiant ou mot de passe incorrect" });
      }
      
      req.login(user, (err) => {
        if (err) {
          log(`Erreur lors de la connexion: ${err}`, "auth-error");
          return next(err);
        }
        
        // Ne pas renvoyer le mot de passe au client
        const { password, ...userWithoutPassword } = user;
        res.json(userWithoutPassword);
      });
    })(req, res, next);
  });

  // Route de déconnexion
  app.post("/api/auth/logout", (req, res) => {
    req.logout((err) => {
      if (err) {
        log(`Erreur lors de la déconnexion: ${err}`, "auth-error");
        return res.status(500).json({ message: "Erreur lors de la déconnexion" });
      }
      res.status(200).json({ message: "Déconnexion réussie" });
    });
  });
  
  // Route pour demander une réinitialisation de mot de passe
  app.post("/api/auth/forgot-password", async (req, res) => {
    try {
      const { email } = req.body;
      
      if (!email) {
        return res.status(400).json({ message: "L'email est requis" });
      }
      
      // Recherche de l'utilisateur par email
      const user = await storage.getUserByEmail(email);
      
      // Si l'utilisateur n'existe pas, on renvoie quand même une réponse de succès
      // pour des raisons de sécurité (pas divulguer l'existence d'un compte)
      if (!user) {
        log(`Tentative de réinitialisation pour un email inexistant: ${email}`, "auth-info");
        return res.status(200).json({ message: "Si cet email existe, un lien de réinitialisation a été envoyé." });
      }
      
      // Génération du token de réinitialisation
      const resetToken = await storage.createPasswordResetToken(user.id);
      
      // URL de base pour la réinitialisation
      const baseUrl = 'https://waabo-app.com';
      const resetLink = `${baseUrl}/reset-password?token=${resetToken.token}`;
      
      // Import du service d'email
      const { sendPasswordResetEmail } = await import('./email-service');
      
      // Envoi de l'email
      const emailSent = await sendPasswordResetEmail(email, resetToken.token, resetLink);
      
      if (emailSent) {
        log(`Email de réinitialisation envoyé à ${email}`, "auth-info");
        return res.status(200).json({ message: "Un email de réinitialisation a été envoyé." });
      } else {
        log(`Échec d'envoi d'email de réinitialisation à ${email}`, "auth-error");
        return res.status(500).json({ message: "Erreur lors de l'envoi de l'email de réinitialisation." });
      }
    } catch (error) {
      log(`Erreur lors de la demande de réinitialisation: ${error}`, "auth-error");
      res.status(500).json({ message: "Une erreur est survenue, veuillez réessayer." });
    }
  });
  
  // Route pour vérifier la validité d'un token de réinitialisation
  app.get("/api/auth/verify-reset-token", async (req, res) => {
    try {
      const { token } = req.query;
      
      if (!token || typeof token !== 'string') {
        return res.status(400).json({ valid: false, message: "Token invalide" });
      }
      
      const isValid = await storage.validatePasswordResetToken(token);
      
      return res.status(200).json({ valid: isValid });
    } catch (error) {
      log(`Erreur lors de la vérification du token: ${error}`, "auth-error");
      res.status(500).json({ valid: false, message: "Une erreur est survenue" });
    }
  });
  
  // Route pour réinitialiser le mot de passe avec un token
  app.post("/api/auth/reset-password", async (req, res) => {
    try {
      const { token, password } = req.body;
      
      if (!token || !password) {
        return res.status(400).json({ message: "Token et mot de passe requis" });
      }
      
      // Vérification du token
      const isValid = await storage.validatePasswordResetToken(token);
      
      if (!isValid) {
        return res.status(400).json({ message: "Token invalide ou expiré" });
      }
      
      // Récupération du token
      const resetToken = await storage.getPasswordResetTokenByToken(token);
      
      if (!resetToken) {
        return res.status(400).json({ message: "Token introuvable" });
      }
      
      // Récupération de l'utilisateur
      const user = await storage.getUser(resetToken.userId);
      
      if (!user) {
        return res.status(400).json({ message: "Utilisateur introuvable" });
      }
      
      // Hachage du nouveau mot de passe
      const hashedPassword = await hashPassword(password);
      
      // Mise à jour du mot de passe
      await storage.updateUser(user.id, { password: hashedPassword });
      
      // Marquage du token comme utilisé
      await storage.markPasswordResetTokenAsUsed(token);
      
      log(`Mot de passe réinitialisé pour l'utilisateur ${user.id}`, "auth-info");
      res.status(200).json({ message: "Mot de passe réinitialisé avec succès" });
    } catch (error) {
      log(`Erreur lors de la réinitialisation du mot de passe: ${error}`, "auth-error");
      res.status(500).json({ message: "Une erreur est survenue lors de la réinitialisation du mot de passe" });
    }
  });

  // Route pour récupérer l'utilisateur actuel (support JWT + Session)
  app.get("/api/user", verifyJWTOrSession, (req, res) => {
    const user = getCurrentUser(req);

    if (!user) {
      return res.status(401).json({ message: "Non authentifié" });
    }

    // Ne pas renvoyer le mot de passe au client
    const { password, ...userWithoutPassword } = user;
    res.json(userWithoutPassword);
  });
}