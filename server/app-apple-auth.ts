import { Request, Response } from 'express';
import { db } from './db';
import { users } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { generateUsername } from './auth';
import jwt from 'jsonwebtoken';
import * as appleSignin from 'apple-signin-auth';

/**
 * Endpoint pour authentifier un utilisateur via Apple dans l'application mobile
 * 
 * Cette route reçoit le token ID Apple et les informations utilisateur
 * puis crée ou met à jour l'utilisateur dans la base de données
 */
export async function handleAppAppleAuth(req: Request, res: Response) {
  const { idToken, userInfo } = req.body;

  if (!idToken || !userInfo || !userInfo.uid) {
    return res.status(400).json({
      success: false,
      message: 'Token ID ou informations utilisateur manquants'
    });
  }

  try {
    // Vérifier le token ID avec Apple
    const appleIdTokenClaims = await appleSignin.verifyIdToken(
      idToken,
      {
        audience: process.env.APPLE_SERVICE_ID || process.env.APPLE_CLIENT_ID,
        ignoreExpiration: true, // Pour la démo, à remplacer en production
      }
    );

    if (!appleIdTokenClaims.sub) {
      return res.status(401).json({
        success: false,
        message: 'Token ID Apple invalide'
      });
    }

    // Vérifier si l'utilisateur existe déjà avec cet ID Apple
    const providerId = appleIdTokenClaims.sub;
    let [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.providerId, providerId));

    // Si l'utilisateur existe, mettre à jour ses informations si nécessaire
    if (existingUser) {
      console.log(`Utilisateur Apple existant trouvé: ${existingUser.id}`);

      // Mise à jour de la date pour indiquer une connexion récente
      const [updatedUser] = await db
        .update(users)
        .set({
          updatedAt: new Date()
        })
        .where(eq(users.id, existingUser.id))
        .returning();

      // Créer un token JWT pour l'utilisateur
      const token = createUserToken(updatedUser);

      return res.status(200).json({
        success: true,
        user: updatedUser,
        token
      });
    }

    // Si l'utilisateur n'existe pas, le créer
    console.log(`Création d'un nouvel utilisateur Apple: ${providerId}`);

    // Génération d'un nom d'utilisateur unique
    // Utiliser l'email si disponible, sinon un identifiant générique basé sur Apple ID
    const usernameBase = userInfo.email
      ? userInfo.email.split('@')[0]
      : `apple_user_${providerId.substring(0, 6)}`;

    const username = await generateUsername(usernameBase);

    // Créer le nouvel utilisateur avec les champs définis dans le schéma
    const userData = {
      username,
      email: userInfo.email || null,
      firstName: userInfo.firstName || null,
      lastName: userInfo.lastName || null,
      provider: 'apple',
      providerId,
      emailVerified: true, // Les emails Apple sont vérifiés
    };

    // Utiliser l'insert standard pour les utilisateurs
    const [newUser] = await db
      .insert(users)
      .values(userData)
      .returning();

    // Créer un token JWT pour l'utilisateur
    const token = createUserToken(newUser);

    return res.status(201).json({
      success: true,
      user: newUser,
      token,
      isNewUser: true
    });

  } catch (error) {
    console.error('Erreur lors de l\'authentification Apple:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'authentification'
    });
  }
}

/**
 * Crée un token JWT pour l'utilisateur
 */
function createUserToken(user: any): string {
  // Vérifier que la variable d'environnement JWT_SECRET est définie
  const jwtSecret = process.env.JWT_SECRET || 'waabo-secret-key-for-app-auth';

  // Créer le payload du token
  const payload = {
    id: user.id,
    username: user.username,
    provider: user.provider,
    // Ne pas inclure d'informations sensibles dans le token
  };

  // Générer le token avec une expiration de 7 jours
  return jwt.sign(payload, jwtSecret, { expiresIn: '7d' });
}