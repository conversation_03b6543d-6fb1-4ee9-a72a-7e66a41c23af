// Apple Authentication Strategy
// @ts-ignore - passport-apple doesn't have TypeScript definitions
import { Strategy as AppleStrategy } from 'passport-apple';
import appleSignin from 'apple-signin-auth';
import { Express } from 'express';
import passport from 'passport';
import { IStorage } from './storage';

export function setupAppleAuth(app: Express, storage: IStorage) {
  // URL de base pour les callbacks
  const baseUrl = 'https://waabo-app.com';

  const callbackURL = `${baseUrl}/api/auth/apple/callback`;
  console.log('Configuration Apple avec URL de callback:', callbackURL);

  if (!process.env.APPLE_TEAM_ID || !process.env.APPLE_SERVICE_ID || !process.env.APPLE_KEY_ID || !process.env.APPLE_PRIVATE_KEY) {
    console.error('Les variables d\'environnement pour Apple Sign In ne sont pas définies');
    return;
  }

  // Configuration de la stratégie Apple
  // Formater correctement la clé privée Apple si elle n'est pas au bon format
  let privateKey = process.env.APPLE_PRIVATE_KEY || '';

  // Remplacer les \n littéraux par des sauts de ligne réels
  privateKey = privateKey.replace(/\\n/g, '\n');

  // Vérifier si la clé est déjà au format PEM
  if (!privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
    // Si la clé est en base64 sans les délimiteurs PEM, ajoutons-les
    privateKey = `-----BEGIN PRIVATE KEY-----\n${privateKey}\n-----END PRIVATE KEY-----`;
  }

  // Assurer que la clé a le bon format avec les sauts de ligne corrects
  // Les clés PEM ont des lignes de 64 caractères (sauf la dernière qui peut être plus courte)
  if (!privateKey.includes('\n-----END')) {
    // Formater la clé PEM correctement si elle n'a pas de sauts de ligne
    const match = privateKey.match(/-----BEGIN PRIVATE KEY-----([\s\S]*?)-----END PRIVATE KEY-----/);
    if (match && match[1]) {
      const keyContent = match[1].replace(/\s/g, '');
      // Formater le contenu en lignes de 64 caractères
      let formattedContent = '';
      for (let i = 0; i < keyContent.length; i += 64) {
        formattedContent += keyContent.substring(i, i + 64) + '\n';
      }
      privateKey = `-----BEGIN PRIVATE KEY-----\n${formattedContent}-----END PRIVATE KEY-----`;
    }
  }

  console.log('Utilisation de la clé Apple avec ID:', process.env.APPLE_KEY_ID);
  console.log('Type de clé privée:', typeof privateKey);
  console.log('Longueur de la clé privée:', privateKey.length);

  // Vérifier si la clé semble être au format correct
  if (privateKey.length < 500) {
    console.warn('ATTENTION: La clé privée Apple semble trop courte, vérifiez son format');
  }

  passport.use(
    new AppleStrategy(
      {
        clientID: process.env.APPLE_SERVICE_ID,
        teamID: process.env.APPLE_TEAM_ID,
        keyID: process.env.APPLE_KEY_ID,
        privateKeyString: privateKey,
        callbackURL,
        passReqToCallback: true,
        scope: ['name', 'email'],
      },
      async (req: any, accessToken: any, refreshToken: any, idToken: any, profile: any, done: any) => {
        try {
          console.log('Apple auth callback reçu');

          if (!idToken) {
            return done(new Error('Pas de token ID reçu d\'Apple'), null);
          }

          // Vérifier et décoder le token
          let decodedToken;
          try {
            decodedToken = await appleSignin.verifyIdToken(idToken, {
              audience: process.env.APPLE_SERVICE_ID,
            });
          } catch (error) {
            console.error('Erreur de vérification du token Apple:', error);
            return done(new Error('Token Apple invalide'), null);
          }

          const { email, sub: appleId } = decodedToken;

          if (!email) {
            return done(new Error('Email non reçu depuis Apple'), null);
          }

          console.log(`Authentification Apple réussie pour: ${email}`);

          // Vérifie si l'utilisateur existe déjà
          let user = await storage.getUserByProvider('apple', appleId);

          if (!user) {
            // Récupère les informations de profil depuis la requête
            const { name } = req.body || {};
            const firstName = name?.firstName || 'Utilisateur';
            const lastName = name?.lastName || 'Apple';

            // Crée un nouvel utilisateur
            user = await storage.createUser({
              email,
              password: null,
              firstName,
              lastName,
              username: email.split('@')[0],
              provider: 'apple',
              providerId: appleId,
            });

            console.log('Nouvel utilisateur Apple créé:', email);
          }

          // Authentifie l'utilisateur
          return done(null, user);
        } catch (error) {
          console.error('Erreur lors de l\'authentification Apple:', error);
          return done(error, null);
        }
      }
    )
  );

  // Routes pour l'authentification Apple
  app.get('/api/auth/apple', passport.authenticate('apple', {
    scope: ['name', 'email'],
    successRedirect: '/?success=true&provider=apple',
    failureRedirect: '/login?error=apple-auth-failed',
  }));

  app.post('/api/auth/apple/callback',
    function (req: any, res: any, next: any) {
      console.log('Apple callback reçu avec corps de requête:', req.body);
      next();
    },
    passport.authenticate('apple', {
      successRedirect: '/?success=true&provider=apple',
      failureRedirect: '/login?error=apple-auth-failed',
    })
  );
}