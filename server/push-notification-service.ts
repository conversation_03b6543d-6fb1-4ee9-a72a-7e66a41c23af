import { db } from './db';
import { eq } from 'drizzle-orm';
import { pushTokens } from '@shared/schema';

// Interface pour les options de notification push
export interface PushNotificationOptions {
  title: string;
  body: string;
  data?: Record<string, string>;
  imageUrl?: string;
  icon?: string;
  tag?: string;
  userId?: number | null;
}

/**
 * Service pour envoyer des notifications push aux appareils mobiles
 * Utilise Firebase Cloud Messaging (FCM) pour la livraison des notifications
 */
class PushNotificationService {
  // Clé serveur Firebase Cloud Messaging
  private fcmServerKey: string | null = process.env.FIREBASE_SERVER_KEY || null;

  constructor() {
    // Vérifier si la clé FCM est configurée
    if (!this.fcmServerKey) {
      console.warn('FIREBASE_SERVER_KEY n\'est pas configurée. Les notifications push ne fonctionneront pas.');
    }
  }

  /**
   * Enregistre un token de notification push pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param token Token de notification push de l'appareil
   */
  public async registerToken(userId: number, token: string): Promise<boolean> {
    try {
      // Vérifier si ce token existe déjà
      const existingTokens = await db
        .select()
        .from(pushTokens)
        .where(eq(pushTokens.token, token));

      if (existingTokens.length > 0) {
        // Mettre à jour le token existant
        await db
          .update(pushTokens)
          .set({
            userId,
            updatedAt: new Date()
          })
          .where(eq(pushTokens.token, token));
      } else {
        // Insérer un nouveau token
        await db
          .insert(pushTokens)
          .values({
            userId,
            token,
            createdAt: new Date(),
            updatedAt: new Date()
          });
      }

      return true;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du token push:', error);
      return false;
    }
  }

  /**
   * Supprime un token de notification push
   * @param token Token à supprimer
   */
  public async unregisterToken(token: string): Promise<boolean> {
    try {
      await db
        .delete(pushTokens)
        .where(eq(pushTokens.token, token));
      
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression du token push:', error);
      return false;
    }
  }

  /**
   * Envoie une notification push à un utilisateur spécifique
   * @param userId ID de l'utilisateur à qui envoyer la notification
   * @param options Options de la notification
   */
  public async sendToUser(userId: number, options: PushNotificationOptions): Promise<boolean> {
    try {
      // Récupérer tous les tokens associés à cet utilisateur
      const userTokens = await db
        .select()
        .from(pushTokens)
        .where(eq(pushTokens.userId, userId));

      if (userTokens.length === 0) {
        console.log(`Aucun token de notification push trouvé pour l'utilisateur ${userId}`);
        return false;
      }

      // Envoyer la notification à tous les appareils de l'utilisateur
      const tokens = userTokens.map(t => t.token);
      return await this.sendToTokens(tokens, options);
    } catch (error) {
      console.error('Erreur lors de l\'envoi de notification push à l\'utilisateur:', error);
      return false;
    }
  }

  /**
   * Envoie une notification push à tous les utilisateurs
   * @param options Options de la notification
   */
  public async sendToAll(options: PushNotificationOptions): Promise<boolean> {
    try {
      // Récupérer tous les tokens
      const allTokens = await db
        .select()
        .from(pushTokens);

      if (allTokens.length === 0) {
        console.log('Aucun token de notification push trouvé');
        return false;
      }

      // Envoyer la notification à tous les appareils
      const tokens = allTokens.map(t => t.token);
      return await this.sendToTokens(tokens, options);
    } catch (error) {
      console.error('Erreur lors de l\'envoi de notification push à tous les utilisateurs:', error);
      return false;
    }
  }

  /**
   * Envoie une notification push à des tokens spécifiques
   * @param tokens Liste des tokens FCM
   * @param options Options de la notification
   */
  private async sendToTokens(tokens: string[], options: PushNotificationOptions): Promise<boolean> {
    if (!this.fcmServerKey) {
      console.error('FIREBASE_SERVER_KEY non configurée, impossible d\'envoyer des notifications push');
      return false;
    }

    try {
      // Préparer le payload FCM
      const message = {
        notification: {
          title: options.title,
          body: options.body,
          image: options.imageUrl,
          icon: options.icon || '/favicon.ico',
          tag: options.tag
        },
        data: options.data || {},
        registration_ids: tokens
      };

      // Envoyer la requête à l'API FCM
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `key=${this.fcmServerKey}`
        },
        body: JSON.stringify(message)
      });

      if (!response.ok) {
        console.error('Erreur lors de l\'envoi de notifications push FCM:', await response.text());
        return false;
      }

      const result = await response.json();
      console.log('Résultat de l\'envoi de notifications push:', result);
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'envoi de notifications push:', error);
      return false;
    }
  }
}

// Exporter une instance singleton du service
export const pushNotificationService = new PushNotificationService();