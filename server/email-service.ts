import { MailService } from '@sendgrid/mail';
import { log } from './vite';

// Vérifier que la clé API SendGrid est disponible
if (!process.env.SENDGRID_API_KEY) {
  log("ATTENTION: La variable d'environnement SENDGRID_API_KEY n'est pas définie. L'envoi d'e-mails ne fonctionnera pas.", "email-service");
}

// Initialiser le service mail
const mailService = new MailService();
if (process.env.SENDGRID_API_KEY) {
  mailService.setApiKey(process.env.SENDGRID_API_KEY);
}

// Interface conforme au type attendu par sendgrid
export interface EmailParams {
  to: string;
  from: string;
  subject: string;
  text: string;
  html: string;
}

/**
 * Envoie un email via SendGrid
 * 
 * @param params Paramètres de l'email
 * @returns Promise<boolean> true si l'envoi a réussi, false sinon
 */
export async function sendEmail(params: EmailParams): Promise<boolean> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      log("Échec de l'envoi d'e-mail: clé API SendGrid manquante", "email-service");
      return false;
    }

    log(`Tentative d'envoi d'email à ${params.to} depuis ${params.from}`, "email-service");
    
    const emailData = {
      to: params.to,
      from: params.from,
      subject: params.subject,
      text: params.text || '',
      html: params.html || '',
    };
    
    log(`Contenu de l'email: ${JSON.stringify({
      to: params.to,
      from: params.from,
      subject: params.subject,
    })}`, "email-service");

    await mailService.send(emailData);
    
    log(`Email envoyé avec succès à ${params.to}`, "email-service");
    return true;
  } catch (error) {
    log(`Erreur lors de l'envoi de l'email: ${error}`, "email-service-error");
    console.error("SendGrid API error details:", error);
    return false;
  }
}

// Logo Waabo Express en HTML avec alignement parfait à droite et centré dans le header
// Basé sur le fichier waaboexpress.html dans le dossier assets avec des ajustements pour email
const waabologoHtml = `
<div style="display: inline-block; text-align: center; padding: 20px 0;">
  <div style="display: inline-block; text-align: right; line-height: 0.9;">
    <div style="font-family: 'Roboto', Arial, sans-serif; font-weight: 700; font-size: 60px; color: #ffffff;">
      waabo
    </div>
    <div style="font-family: 'Roboto', Arial, sans-serif; font-weight: 700; font-size: 24px; color: #e5a100; margin-top: -5px;">
      express
    </div>
  </div>
</div>`;

/**
 * Envoie un email de réinitialisation de mot de passe
 * 
 * @param to Adresse email du destinataire
 * @param resetToken Token de réinitialisation 
 * @param resetLink Lien de réinitialisation avec le token
 * @returns Promise<boolean> true si l'envoi a réussi, false sinon
 */
export async function sendPasswordResetEmail(to: string, resetToken: string, resetLink: string): Promise<boolean> {
  // Utiliser l'adresse d'envoi demandée
  const fromEmail = '<EMAIL>';
  
  const emailParams: EmailParams = {
    to,
    from: fromEmail,
    subject: 'Réinitialisation de votre mot de passe Waabo',
    text: `
      Bonjour,
      
      Vous avez demandé la réinitialisation de votre mot de passe sur Waabo.
      
      Veuillez cliquer sur le lien suivant pour réinitialiser votre mot de passe :
      ${resetLink}
      
      Ce lien est valable pour une durée de 1 heure.
      
      Si vous n'avez pas demandé cette réinitialisation, veuillez ignorer cet email.
      
      Cordialement,
      L'équipe Waabo
    `,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Réinitialisation de votre mot de passe</title>
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f9f9f9; color: #333333;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f9f9f9; padding: 20px;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                <!-- Header avec logo Waabo Express -->
                <tr>
                  <td style="padding: 30px 0; text-align: center; background-color: #004d25; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                    ${waabologoHtml}
                  </td>
                </tr>
                
                <!-- Contenu principal -->
                <tr>
                  <td style="padding: 40px 30px;">
                    <h1 style="margin-top: 0; margin-bottom: 20px; color: #004d25; font-size: 24px; font-weight: 600; text-align: center;">
                      Réinitialisation de votre mot de passe
                    </h1>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">Bonjour,</p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Vous avez demandé la réinitialisation de votre mot de passe sur <strong>Waabo</strong>.
                    </p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Veuillez cliquer sur le bouton ci-dessous pour réinitialiser votre mot de passe :
                    </p>
                    
                    <p style="text-align: center; margin: 30px 0;">
                      <a href="${resetLink}" 
                         style="background-color: #004d25; color: white; padding: 14px 25px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px; transition: background-color 0.3s ease;">
                        Réinitialiser mon mot de passe
                      </a>
                    </p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Si le bouton ne fonctionne pas, vous pouvez copier et coller le lien suivant dans votre navigateur :
                    </p>
                    
                    <p style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin-bottom: 20px; word-break: break-all; font-size: 14px; line-height: 1.4; border-left: 4px solid #004d25;">
                      ${resetLink}
                    </p>
                    
                    <div style="background-color: #FFF9EB; border-left: 4px solid #FFB100; padding: 15px; margin-bottom: 20px; border-radius: 6px;">
                      <p style="margin: 0; font-size: 15px; line-height: 1.5;">
                        <strong>Note importante :</strong> Ce lien est valable pour une durée de <strong>1 heure</strong> seulement. Après ce délai, vous devrez faire une nouvelle demande.
                      </p>
                    </div>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Si vous n'avez pas demandé cette réinitialisation, veuillez ignorer cet email et vérifier la sécurité de votre compte.
                    </p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Cordialement,<br>
                      <strong>L'équipe Waabo</strong>
                    </p>
                  </td>
                </tr>
                
                <!-- Footer -->
                <tr>
                  <td style="background-color: #f5f5f5; padding: 25px 30px; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; text-align: center;">
                    <p style="margin: 0 0 10px 0; font-size: 14px; color: #666666;">
                      Ceci est un email automatique, merci de ne pas y répondre.
                    </p>
                    <p style="margin: 0; font-size: 13px; color: #888888;">
                      &copy; 2025 Waabo. Tous droits réservés.
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `
  };
  
  // Envoi immédiat pour accélérer le traitement
  try {
    // Log supplémentaire pour suivi
    log(`Envoi de l'email de réinitialisation à ${to}`, "email-service-reset");
    
    // Envoi direct de l'email
    if (!process.env.SENDGRID_API_KEY) {
      log("Échec de l'envoi de l'email de réinitialisation: clé API SendGrid manquante", "email-service");
      return false;
    }
    
    await mailService.send({
      to: emailParams.to,
      from: emailParams.from,
      subject: emailParams.subject,
      text: emailParams.text || '',
      html: emailParams.html || '',
    });
    
    log(`Email de réinitialisation envoyé avec succès à ${to}`, "email-service");
    return true;
  } catch (error) {
    log(`Erreur lors de l'envoi de l'email de réinitialisation: ${error}`, "email-service-error");
    console.error("SendGrid API error details:", error);
    return false;
  }
}

/**
 * Envoie un email de vérification d'adresse email
 * 
 * @param to Adresse email du destinataire
 * @param verificationToken Jeton de vérification 
 * @param verificationLink Lien de vérification avec le jeton
 * @returns Promise<boolean> true si l'envoi a réussi, false sinon
 */
export async function sendEmailVerificationEmail(to: string, verificationToken: string, verificationLink: string): Promise<boolean> {
  // Utiliser l'adresse d'envoi demandée
  const fromEmail = '<EMAIL>';
  
  const emailParams: EmailParams = {
    to,
    from: fromEmail,
    subject: 'Vérification de votre adresse email Waabo',
    text: `
      Bonjour,
      
      Merci de vous être inscrit sur Waabo ! Pour activer votre compte, nous avons besoin de vérifier votre adresse email.
      
      Veuillez cliquer sur le lien suivant pour confirmer votre adresse email :
      ${verificationLink}
      
      Ce lien est valable pour une durée de 24 heures.
      
      Si vous n'avez pas créé de compte sur Waabo, veuillez ignorer cet email.
      
      Cordialement,
      L'équipe Waabo
    `,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vérification de votre adresse email</title>
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f9f9f9; color: #333333;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f9f9f9; padding: 20px;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                <!-- Header avec logo Waabo Express -->
                <tr>
                  <td style="padding: 30px 0; text-align: center; background-color: #004d25; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                    ${waabologoHtml}
                  </td>
                </tr>
                
                <!-- Contenu principal -->
                <tr>
                  <td style="padding: 40px 30px;">
                    <h1 style="margin-top: 0; margin-bottom: 20px; color: #004d25; font-size: 24px; font-weight: 600; text-align: center;">
                      Vérification de votre adresse email
                    </h1>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">Bonjour,</p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Merci de vous être inscrit sur <strong>Waabo</strong> ! Pour activer votre compte, nous avons besoin de vérifier votre adresse email.
                    </p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Veuillez cliquer sur le bouton ci-dessous pour confirmer votre adresse email :
                    </p>
                    
                    <p style="text-align: center; margin: 30px 0;">
                      <a href="${verificationLink}" 
                         style="background-color: #004d25; color: white; padding: 14px 25px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px; transition: background-color 0.3s ease;">
                        Vérifier mon adresse email
                      </a>
                    </p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Si le bouton ne fonctionne pas, vous pouvez copier et coller le lien suivant dans votre navigateur :
                    </p>
                    
                    <p style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin-bottom: 20px; word-break: break-all; font-size: 14px; line-height: 1.4; border-left: 4px solid #004d25;">
                      ${verificationLink}
                    </p>
                    
                    <div style="background-color: #FFF9EB; border-left: 4px solid #FFB100; padding: 15px; margin-bottom: 20px; border-radius: 6px;">
                      <p style="margin: 0; font-size: 15px; line-height: 1.5;">
                        <strong>Note importante :</strong> Ce lien est valable pour une durée de <strong>24 heures</strong> seulement. Après ce délai, vous devrez demander un nouveau lien de vérification.
                      </p>
                    </div>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Si vous n'avez pas créé de compte sur Waabo, veuillez ignorer cet email.
                    </p>
                    
                    <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.5;">
                      Cordialement,<br>
                      <strong>L'équipe Waabo</strong>
                    </p>
                  </td>
                </tr>
                
                <!-- Footer -->
                <tr>
                  <td style="background-color: #f5f5f5; padding: 25px 30px; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; text-align: center;">
                    <p style="margin: 0 0 10px 0; font-size: 14px; color: #666666;">
                      Ceci est un email automatique, merci de ne pas y répondre.
                    </p>
                    <p style="margin: 0; font-size: 13px; color: #888888;">
                      &copy; 2025 Waabo. Tous droits réservés.
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `
  };
  
  // Envoi immédiat pour égaliser la vitesse avec l'email de réinitialisation
  try {
    // Log supplémentaire pour suivi
    log(`Envoi de l'email de vérification à ${to}`, "email-service-verify");
    
    // Envoi direct de l'email
    if (!process.env.SENDGRID_API_KEY) {
      log("Échec de l'envoi de l'email de vérification: clé API SendGrid manquante", "email-service");
      return false;
    }
    
    await mailService.send({
      to: emailParams.to,
      from: emailParams.from,
      subject: emailParams.subject,
      text: emailParams.text || '',
      html: emailParams.html || '',
    });
    
    log(`Email de vérification envoyé avec succès à ${to}`, "email-service");
    return true;
  } catch (error) {
    log(`Erreur lors de l'envoi de l'email de vérification: ${error}`, "email-service-error");
    console.error("SendGrid API error details:", error);
    return false;
  }
}