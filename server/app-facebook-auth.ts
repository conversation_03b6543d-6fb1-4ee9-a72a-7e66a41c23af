import { Request, Response } from 'express';
import { db } from './db';
import { users } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { generateUsername } from './auth';
import jwt from 'jsonwebtoken';

/**
 * Endpoint pour authentifier un utilisateur via Facebook dans l'application mobile
 * 
 * Cette route reçoit le token d'accès Facebook et les informations utilisateur
 * puis crée ou met à jour l'utilisateur dans la base de données
 */
export async function handleAppFacebookAuth(req: Request, res: Response) {
  const { accessToken, userInfo } = req.body;
  
  if (!accessToken || !userInfo || !userInfo.id) {
    return res.status(400).json({ 
      success: false, 
      message: 'Token d\'accès ou informations utilisateur manquants' 
    });
  }

  try {
    // Vérifier si l'utilisateur existe déjà avec cet ID Facebook
    const providerId = userInfo.id;
    let [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.providerId, providerId));

    // Si l'utilisateur existe, mettre à jour ses informations si nécessaire
    if (existingUser) {
      console.log(`Utilisateur Facebook existant trouvé: ${existingUser.id}`);
      
      // Mise à jour de la date pour indiquer une connexion récente
      const [updatedUser] = await db
        .update(users)
        .set({
          updatedAt: new Date()
        })
        .where(eq(users.id, existingUser.id))
        .returning();
      
      // Créer un token JWT pour l'utilisateur
      const token = createUserToken(updatedUser);
      
      return res.status(200).json({ 
        success: true, 
        user: updatedUser,
        token
      });
    }

    // Si l'utilisateur n'existe pas, le créer
    console.log(`Création d'un nouvel utilisateur Facebook: ${providerId}`);
    
    // Générer un nom d'utilisateur unique basé sur le nom
    const username = await generateUsername(userInfo.name || 'user');
    
    // Créer le nouvel utilisateur avec les champs définis dans le schéma
    // En utilisant l'objet de stockage pour assurer la compatibilité avec le schéma
    const userData = {
      username,
      email: userInfo.email || null,
      firstName: userInfo.firstName || null,
      lastName: userInfo.lastName || null,
      provider: 'facebook',
      providerId,
      emailVerified: userInfo.email ? true : false, // Les emails Facebook sont vérifiés
    };
    
    // Utiliser l'insert standard pour les utilisateurs
    const [newUser] = await db
      .insert(users)
      .values(userData)
      .returning();
    
    // Créer un token JWT pour l'utilisateur
    const token = createUserToken(newUser);
    
    return res.status(201).json({ 
      success: true, 
      user: newUser,
      token,
      isNewUser: true
    });
    
  } catch (error) {
    console.error('Erreur lors de l\'authentification Facebook:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Erreur serveur lors de l\'authentification' 
    });
  }
}

/**
 * Crée un token JWT pour l'utilisateur
 */
function createUserToken(user: any): string {
  // Vérifier que la variable d'environnement JWT_SECRET est définie
  const jwtSecret = process.env.JWT_SECRET || 'waabo-secret-key-for-app-auth';
  
  // Créer le payload du token
  const payload = {
    id: user.id,
    username: user.username,
    provider: user.provider,
    // Ne pas inclure d'informations sensibles dans le token
  };
  
  // Générer le token avec une expiration de 7 jours
  return jwt.sign(payload, jwtSecret, { expiresIn: '7d' });
}