import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { storage } from './storage';

// Extend Express Request interface to include jwtUser
declare global {
  namespace Express {
    interface Request {
      jwtUser?: any;
    }
  }
}

/**
 * Middleware hybride pour vérifier JWT ou session
 * Essaie JWT d'abord, puis fallback vers session
 */
export const verifyJWTOrSession = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    console.log('🔑 Auth header received:', authHeader ? 'EXISTS' : 'NOT FOUND');

    // Essayer JWT d'abord
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const jwtSecret = process.env.JWT_SECRET || 'waabo-secret-key-for-app-auth';
      console.log('🔑 JWT token extracted:', token.substring(0, 20) + '...');

      try {
        const decoded = jwt.verify(token, jwtSecret) as any;
        console.log('🔑 JWT decoded successfully:', { id: decoded.id, username: decoded.username });

        if (decoded && decoded.id) {
          const user = await storage.getUser(decoded.id);

          if (user) {
            // JWT auth réussi
            console.log('🔑 JWT auth successful for user:', user.username);
            req.jwtUser = user;
            return next();
          } else {
            console.log('🔑 JWT decoded but user not found in DB');
          }
        }
      } catch (jwtError: any) {
        console.log('🔑 JWT verification failed:', jwtError.message);
      }
    }

    // Fallback vers session auth
    console.log('🔑 Checking session auth, isAuthenticated:', req.isAuthenticated ? req.isAuthenticated() : 'NO_FUNCTION');
    if (req.isAuthenticated && req.isAuthenticated()) {
      console.log('🔑 Session auth successful');
      return next();
    }

    // Aucune auth valide
    console.log('🔑 No valid authentication found');
    return res.status(401).json({ message: "Non authentifié" });

  } catch (error) {
    console.error('🔑 Auth verification error:', error);
    return res.status(401).json({ message: "Erreur d'authentification" });
  }
};

/**
 * Middleware strict pour JWT uniquement (pour endpoints mobile-only)
 */
export const requireJWT = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Token JWT manquant' });
    }
    
    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'waabo-secret-key-for-app-auth';
    
    const decoded = jwt.verify(token, jwtSecret) as any;
    
    if (!decoded || !decoded.id) {
      return res.status(401).json({ message: 'Token JWT invalide' });
    }
    
    const user = await storage.getUser(decoded.id);
    
    if (!user) {
      return res.status(401).json({ message: 'Utilisateur non trouvé' });
    }
    
    req.jwtUser = user;
    next();
    
  } catch (error) {
    console.error('JWT verification error:', error);
    return res.status(401).json({ message: 'Token JWT invalide' });
  }
};

/**
 * Helper function pour récupérer l'utilisateur actuel (JWT ou session)
 */
export const getCurrentUser = (req: Request) => {
  // Priorité au JWT user
  if (req.jwtUser) {
    return req.jwtUser;
  }
  
  // Fallback vers session user
  if (req.user) {
    return req.user;
  }
  
  return null;
};
