import { Request, Response } from 'express';
import { db } from './db';
import { users } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { generateUsername } from './auth';
import jwt from 'jsonwebtoken';
import { OAuth2Client } from 'google-auth-library';

// Créer un client OAuth2 pour vérifier les tokens Google
const googleClient = new OAuth2Client();

/**
 * Endpoint pour authentifier un utilisateur via Google dans l'application mobile
 * 
 * Cette route reçoit le token ID Google et les informations utilisateur
 * puis crée ou met à jour l'utilisateur dans la base de données
 */
export async function handleAppGoogleAuth(req: Request, res: Response) {
  const { idToken, userInfo } = req.body;

  if (!idToken || !userInfo || !userInfo.uid || !userInfo.email) {
    return res.status(400).json({
      success: false,
      message: 'Token ID ou informations utilisateur manquants'
    });
  }

  try {
    // Vérifier le token ID avec Google
    const audiences = [
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_ANDROID_CLIENT_ID,
      process.env.GOOGLE_FIREBASE_CLIENT_ID
    ].filter((id): id is string => Boolean(id));

    const ticket = await googleClient.verifyIdToken({
      idToken,
      audience: audiences
    });

    const payload = ticket.getPayload();
    if (!payload || !payload.sub) {
      return res.status(401).json({
        success: false,
        message: 'Token ID Google invalide'
      });
    }

    // Vérifier si l'utilisateur existe déjà avec cet ID Google
    const providerId = payload.sub;
    let [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.providerId, providerId));

    // Si l'utilisateur existe, mettre à jour ses informations si nécessaire
    if (existingUser) {

      // Mise à jour de la date pour indiquer une connexion récente
      const [updatedUser] = await db
        .update(users)
        .set({
          updatedAt: new Date()
        })
        .where(eq(users.id, existingUser.id))
        .returning();

      // Créer un token JWT pour l'utilisateur
      const token = createUserToken(updatedUser);

      // Trả về JWT token cho mobile (không tạo session)
      return res.status(200).json({
        success: true,
        user: updatedUser,
        token
      });
    }

    // Si l'utilisateur n'existe pas, le créer

    // Générer un nom d'utilisateur unique basé sur le nom
    const username = await generateUsername(userInfo.displayName || userInfo.email);

    // Créer le nouvel utilisateur avec les champs définis dans le schéma
    const userData = {
      username,
      email: userInfo.email,
      firstName: userInfo.firstName || null,
      lastName: userInfo.lastName || null,
      provider: 'google',
      providerId,
      emailVerified: true, // Les emails Google sont toujours vérifiés
    };

    // Utiliser l'insert standard pour les utilisateurs
    const [newUser] = await db
      .insert(users)
      .values(userData)
      .returning();

    // Créer un token JWT pour l'utilisateur
    const token = createUserToken(newUser);

    // Trả về JWT token cho mobile (không tạo session)
    return res.status(201).json({
      success: true,
      user: newUser,
      token,
      isNewUser: true
    });

  } catch (error) {
    console.error('Erreur lors de l\'authentification Google:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'authentification'
    });
  }
}

/**
 * Crée un token JWT pour l'utilisateur
 */
function createUserToken(user: any): string {
  // Vérifier que la variable d'environnement JWT_SECRET est définie
  const jwtSecret = process.env.JWT_SECRET || 'waabo-secret-key-for-app-auth';

  // Créer le payload du token
  const payload = {
    id: user.id,
    username: user.username,
    provider: user.provider,
    // Ne pas inclure d'informations sensibles dans le token
  };

  // Générer le token avec une expiration de 7 jours
  return jwt.sign(payload, jwtSecret, { expiresIn: '7d' });
}