import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from 'ws';
import { storage } from "./storage";
import { 
  insertUserSchema, 
  insertOrderSchema, 
  insertProductSchema, 
  insertNotificationSchema,
  insertOrderStatusSchema,
  insertContactMessageSchema,
  orderStatusEnum,
  User,
  Notification,
  ContactMessage
} from "@shared/schema";
import { z } from "zod";
import { setupAuth } from "./auth";
import { OAuth2Client } from 'google-auth-library';
import passport from 'passport';
import ExcelJS from 'exceljs';
import { sendEmailVerificationEmail, sendPasswordResetEmail } from "./email-service";
import axios from 'axios';
import { handleAppFacebookAuth } from './app-facebook-auth';
import { handleAppGoogleAuth } from './app-google-auth';
import { handleAppAppleAuth } from './app-apple-auth';
import { verifyJWTOrSession, getCurrentUser } from './jwt-middleware';
import { pushNotificationService } from './push-notification-service';


// Gestionnaire de connexions WebSocket par utilisateur
const userConnections = new Map<number, WebSocket[]>();

// Fonction pour envoyer une notification à un utilisateur spécifique
function sendNotificationToUser(userId: number, notification: Notification) {
  const connections = userConnections.get(userId);
  if (connections && connections.length > 0) {
    const message = JSON.stringify({
      type: 'notification',
      data: notification
    });

    connections.forEach(conn => {
      if (conn.readyState === WebSocket.OPEN) {
        conn.send(message);
      }
    });
  }
}

// Fonction pour envoyer une notification globale à tous les utilisateurs connectés
function sendGlobalNotification(notification: Notification) {
  userConnections.forEach((connections, userId) => {
    sendNotificationToUser(userId, notification);
  });
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Configuration de l'authentification
  setupAuth(app);

  // API prefix for all routes
  const apiPrefix = "/api";

  // User routes

  // Endpoint pour récupérer tous les utilisateurs (admin)
  app.get(`${apiPrefix}/users`, async (_req: Request, res: Response) => {
    try {
      const users = await storage.getAllUsers();
      // Retirer les mots de passe de la réponse
      const usersWithoutPasswords = users.map((user: User) => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      res.status(200).json(usersWithoutPasswords);
    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });
  
  // Endpoint pour exporter la liste des utilisateurs au format Excel
  app.get(`${apiPrefix}/users/export/excel`, async (_req: Request, res: Response) => {
    try {
      if (!_req.isAuthenticated() || _req.user.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé à exporter les données des utilisateurs" });
      }
      
      const users = await storage.getAllUsers();
      
      // Créer un nouveau workbook Excel
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Utilisateurs');
      
      // Définir les en-têtes
      worksheet.columns = [
        { header: 'ID', key: 'id', width: 10 },
        { header: 'Nom d\'utilisateur', key: 'username', width: 20 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Email vérifié', key: 'emailVerified', width: 15 },
        { header: 'Téléphone', key: 'phone', width: 20 },
        { header: 'Téléphone vérifié', key: 'phoneVerified', width: 15 },
        { header: 'Prénom', key: 'firstName', width: 20 },
        { header: 'Nom', key: 'lastName', width: 20 },
        { header: 'Date d\'inscription', key: 'createdAt', width: 20 },
        { header: 'Dernière connexion', key: 'updatedAt', width: 20 },
        { header: 'Méthode de connexion', key: 'provider', width: 20 },
      ];
      
      // Ajouter les données sans les mots de passe
      users.forEach(user => {
        const { password, ...userData } = user;
        
        // Formater les dates pour Excel
        const createdAtDate = user.createdAt ? new Date(user.createdAt) : null;
        const updatedAtDate = user.updatedAt ? new Date(user.updatedAt) : null;
        
        worksheet.addRow({
          ...userData,
          createdAt: createdAtDate,
          updatedAt: updatedAtDate,
          provider: user.provider || 'Email/Mot de passe'
        });
      });
      
      // Appliquer des styles pour les en-têtes
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE9F1FD' }
      };
      
      // Configurer la réponse
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=utilisateurs.xlsx');
      
      // Générer et renvoyer le fichier Excel
      await workbook.xlsx.write(res);
      res.end();
      
    } catch (error) {
      console.error('Error exporting users to Excel:', error);
      res.status(500).json({ message: "Échec de l'exportation des utilisateurs" });
    }
  });

  // Endpoint pour récupérer un utilisateur par ID
  app.get(`${apiPrefix}/users/:id`, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Retirer le mot de passe de la réponse
      const { password, ...userWithoutPassword } = user;
      res.status(200).json(userWithoutPassword);
    } catch (error) {
      console.error('Error fetching user:', error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Facebook Auth routes
  app.get('/api/auth/facebook', (req, res, next) => {
    console.log('Démarrage du flux d\'authentification Facebook');
    console.log('URL actuelle:', req.originalUrl);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    
    // Utiliser une promesse avec timeout pour éviter les blocages potentiels
    const authTimeout = setTimeout(() => {
      console.error('Facebook auth timeout - redirection vers /login');
      return res.redirect('/login?error=facebook_timeout');
    }, 10000); // 10 secondes de timeout
    
    // Middleware d'authentification avec gestion d'erreur
    passport.authenticate('facebook', { 
      scope: ['email'],
      failureRedirect: '/login?error=facebook_init_failed' 
    })(req, res, next);
    
    // Annuler le timeout si le middleware a répondu
    clearTimeout(authTimeout);
  });

  app.get('/api/auth/facebook/callback', (req, res, next) => {
    console.log('Facebook callback reçu avec paramètres:', req.query);
    
    if (req.query.error) {
      console.error('Erreur Facebook reçue:', req.query.error);
      return res.redirect(`/login?error=${encodeURIComponent(req.query.error as string)}`);
    }
    
    passport.authenticate('facebook', { 
      failureRedirect: '/login?error=facebook_auth_failed'
    })(req, res, (err: any) => {
      if (err) {
        console.error('Erreur pendant l\'authentification Facebook:', err);
        return res.redirect('/login?error=internal_error');
      }
      
      console.log('Authentification Facebook réussie pour:', req.user?.email);
      
      // Rediriger vers la page d'accueil avec un paramètre pour indiquer la connexion réussie
      // Ce paramètre sera détecté par le frontend pour actualiser l'interface utilisateur
      res.redirect('/?login_success=true&provider=facebook');
    });
  });

  // Google Auth routes
  const oauth2Client = new OAuth2Client(
    process.env.GOOGLE_CLIENT_ID!,
    process.env.GOOGLE_CLIENT_SECRET!,
    'https://waabo-app.com/api/auth/google/callback'
  );

  app.get('/api/auth/google', (_req, res) => {
    try {
      const authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: ['email', 'profile'],
        // Inclure le prompt pour s'assurer que l'utilisateur peut choisir son compte
        prompt: 'select_account'
      });
      console.log('Generated Google Auth URL:', authUrl);
      res.json({ 
        url: authUrl,
        redirectUri: 'https://waabo-app.com/api/auth/google/callback',
        clientId: process.env.GOOGLE_CLIENT_ID?.substring(0, 8) + '...' // Afficher partiellement pour des raisons de sécurité
      });
    } catch (error: any) {
      console.error('Error generating Google auth URL:', error);
      res.status(500).json({ 
        message: 'Failed to generate Google auth URL',
        error: error.message || 'Unknown error'
      });
    }
  });

  app.get('/api/auth/google/callback', async (req, res) => {
    try {
      console.log('Google auth callback received with query params:', req.query);
      
      // Vérifier si nous avons reçu une erreur de Google
      if (req.query.error) {
        console.error('Google auth error received:', req.query.error);
        return res.redirect(`/login?error=${encodeURIComponent(req.query.error as string)}`);
      }
      
      const { code } = req.query;
      if (!code) {
        console.error('No code parameter received from Google');
        return res.redirect('/login?error=no_auth_code');
      }
      
      console.log('Exchanging auth code for tokens');
      const { tokens } = await oauth2Client.getToken(code as string);
      
      if (!tokens || !tokens.id_token) {
        console.error('Failed to get tokens from Google');
        return res.redirect('/login?error=no_tokens');
      }
      
      console.log('Verifying ID token');
      const ticket = await oauth2Client.verifyIdToken({
        idToken: tokens.id_token,
        audience: process.env.GOOGLE_CLIENT_ID
      });
      
      const payload = ticket.getPayload();

      if (!payload) {
        console.error('Invalid token payload');
        return res.redirect('/login?error=invalid_token');
      }

      console.log(`Google auth successful for email: ${payload.email}`);
      
      // Check if user exists
      let user = await storage.getUserByEmail(payload.email!);

      if (!user) {
        // Create new user
        console.log(`Creating new user for: ${payload.email}`);
        user = await storage.createUser({
          email: payload.email!,
          username: payload.email!.split('@')[0],
          firstName: payload.given_name,
          lastName: payload.family_name,
          provider: 'google',
          providerId: payload.sub
        });
      }

      req.login(user, (err) => {
        if (err) {
          console.error('Error during login:', err);
          return res.redirect('/login?error=login_failed');
        }
        console.log(`Successfully logged in user: ${user.email}`);
        res.redirect('/?login_success=true&provider=google');
      });
    } catch (error: any) {
      console.error('Google auth error:', error);
      const errorMessage = encodeURIComponent(error.message || 'Authentication failed');
      res.redirect(`/login?error=${errorMessage}`);
    }
  });

  // Route de login gérée dans auth.ts

  // Endpoint pour mettre à jour un utilisateur (support JWT + Session)
  app.patch(`${apiPrefix}/users/:id`, verifyJWTOrSession, async (req: Request, res: Response) => {
    try {
      const currentUser = getCurrentUser(req);

      if (!currentUser) {
        return res.status(401).json({ message: "Non authentifié" });
      }

      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "ID utilisateur invalide" });
      }

      // Vérifier que l'utilisateur modifie son propre profil ou est un admin
      if (currentUser.id !== userId && currentUser.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé à modifier cet utilisateur" });
      }

      const userData = req.body;
      const { username, email } = userData;

      // Vérifier si le nom d'utilisateur existe déjà pour un autre utilisateur
      if (username) {
        const existingUser = await storage.getUserByUsername(username);
        if (existingUser && existingUser.id !== userId) {
          return res.status(400).json({ message: "Ce nom d'utilisateur est déjà utilisé" });
        }
      }

      // Vérifier si l'email existe déjà pour un autre utilisateur
      if (email) {
        const existingUser = await storage.getUserByEmail(email);
        if (existingUser && existingUser.id !== userId) {
          return res.status(400).json({ message: "Cet email est déjà utilisé" });
        }
      }
      
      // Récupérer l'utilisateur à modifier pour faire des comparaisons
      const userToUpdate = await storage.getUser(userId);
      if (!userToUpdate) {
        return res.status(404).json({ message: "Utilisateur non trouvé" });
      }
      
      // Préparer les données de mise à jour incluant tous les champs
      let updateData: Partial<User> = { 
        username: userData.username,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone
      };
      
      // Vérifier si le numéro de téléphone a changé
      if (userData.phone && userToUpdate.phone !== userData.phone) {
        console.log("TÉLÉPHONE MODIFIÉ: Ancien:", userToUpdate.phone, "Nouveau:", userData.phone);

        // Le téléphone a été modifié, FORCER le statut non vérifié avec une requête séparée
        const phoneUpdateData = {
          phone: userData.phone,
          phoneVerified: false,
          phoneVerificationToken: null
        };

        // Mettre à jour IMMÉDIATEMENT le statut du téléphone
        await storage.updateUser(userId, phoneUpdateData);
        console.log("Statut de vérification téléphonique réinitialisé à false");

        // Continuer avec les autres mises à jour mais sans le téléphone (déjà traité)
        updateData = {
          username: userData.username,
          firstName: userData.firstName,
          lastName: userData.lastName
        };

        console.log(`Numéro de téléphone modifié pour l'utilisateur ${userId}: ${userToUpdate.phone} -> ${userData.phone}. Statut de vérification réinitialisé.`);
        
        // Créer une notification pour informer l'utilisateur qu'il doit vérifier son nouveau numéro
        storage.createNotification({
          title: "Vérification téléphonique requise",
          body: "Vous avez changé votre numéro de téléphone. Pour des raisons de sécurité, vous devez vérifier ce nouveau numéro.",
          targetUserId: userId,
          isGlobal: false,
          sentAt: new Date()
        });
      }
      
      if (email) {
        // Vérifier si l'email a changé
        if (userToUpdate.email !== email) {
          // L'email a été modifié, marquer comme non vérifié
          // Important: préserver l'état phoneVerified s'il a été modifié précédemment
          updateData = {
            ...updateData,
            email,
            emailVerified: false,  // Définir le statut de vérification de l'email à false
            // S'assurer que phoneVerified est conservé si déjà modifié
            phoneVerified: updateData.phoneVerified !== undefined ? updateData.phoneVerified : userToUpdate.phoneVerified
          };

          console.log(`Email modifié pour l'utilisateur ${userId}: ${userToUpdate.email} -> ${email}. Statut de vérification réinitialisé.`);
          
          // Créer une notification pour informer l'utilisateur qu'il doit vérifier son nouvel email
          storage.createNotification({
            targetUserId: userId,
            title: 'Vérification d\'email requise',
            body: 'Vous avez changé votre adresse email. Pour des raisons de sécurité, vous devez vérifier cette nouvelle adresse. Rendez-vous sur votre profil pour envoyer un email de vérification.',
            isGlobal: false
          });
        } else {
          // Pas de changement d'email ou utilisateur non trouvé
          updateData = { ...updateData, email };
        }
      }

      // Mise à jour de l'utilisateur avec les données préparées
      const updatedUser = await storage.updateUser(userId, updateData);
      if (!updatedUser) {
        return res.status(404).json({ message: "Utilisateur non trouvé" });
      }

      // Retirer le mot de passe de la réponse
      const { password, ...userWithoutPassword } = updatedUser;
      res.status(200).json(userWithoutPassword);
    } catch (error) {
      console.error('Error updating user:', error);
      res.status(500).json({ message: "Échec de la mise à jour de l'utilisateur" });
    }
  });
  
  // Endpoint pour supprimer un utilisateur et toutes ses données associées
  app.delete(`${apiPrefix}/users/:id`, async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Non authentifié" });
      }
      
      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "ID utilisateur invalide" });
      }
      
      // Empêcher la suppression de l'utilisateur administrateur (id=1)
      if (userId === 1) {
        return res.status(403).json({ message: "Impossible de supprimer l'utilisateur administrateur" });
      }
      
      // Vérifier que seul l'administrateur peut supprimer des utilisateurs
      if (req.user.username !== 'admin') {
        return res.status(403).json({ message: "Seul l'administrateur peut supprimer des utilisateurs" });
      }
      
      const success = await storage.deleteUser(userId);
      if (!success) {
        return res.status(404).json({ message: "Utilisateur non trouvé ou erreur lors de la suppression" });
      }
      
      res.status(200).json({ message: "Utilisateur et toutes ses données associées supprimés avec succès" });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({ message: "Échec de la suppression de l'utilisateur" });
    }
  });

  // Endpoint pour changer le mot de passe d'un utilisateur
  app.patch(`${apiPrefix}/users/:id/password`, async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Non authentifié" });
      }

      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "ID utilisateur invalide" });
      }

      // Vérifier que l'utilisateur modifie son propre mot de passe ou est un admin
      if (req.user.id !== userId && req.user.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé à modifier le mot de passe de cet utilisateur" });
      }

      const { currentPassword, newPassword } = req.body;

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "Utilisateur non trouvé" });
      }

      // Vérifier le mot de passe actuel (sauf si c'est l'admin qui fait la modification)
      if (req.user.username !== 'admin') {
        const { comparePasswords } = await import('./auth');
        const isPasswordValid = await comparePasswords(currentPassword, user.password);
        if (!isPasswordValid) {
          return res.status(400).json({ message: "Mot de passe actuel incorrect" });
        }
      }

      // Hacher le nouveau mot de passe
      const { hashPassword } = await import('./auth');
      const hashedPassword = await hashPassword(newPassword);

      // Mettre à jour le mot de passe
      const updatedUser = await storage.updateUser(userId, { password: hashedPassword });
      if (!updatedUser) {
        return res.status(404).json({ message: "Utilisateur non trouvé" });
      }

      res.status(200).json({ message: "Mot de passe mis à jour avec succès" });
    } catch (error) {
      console.error('Error updating password:', error);
      res.status(500).json({ message: "Échec de la mise à jour du mot de passe" });
    }
  });

  // Email verification routes
  
  // Endpoint pour demander un nouvel email de vérification
  app.post(`${apiPrefix}/email/send-verification`, async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Non authentifié" });
      }
      
      const userId = req.user.id;
      
      // Vérifier si l'email est déjà vérifié
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "Utilisateur non trouvé" });
      }
      
      if (user.emailVerified) {
        return res.status(400).json({ message: "Votre email est déjà vérifié" });
      }
      
      // Créer un jeton de vérification
      const verificationToken = await storage.createEmailVerificationToken(userId);
      
      // Construire le lien de vérification qui pointe vers la page frontend
      const verificationLink = `https://waabo-app.com/verify-email?token=${verificationToken.token}`;
      
      // Vérifier si l'email est défini
      if (!user.email) {
        return res.status(400).json({ message: "L'utilisateur n'a pas d'adresse email" });
      }
      
      // Envoyer l'email
      const emailSent = await sendEmailVerificationEmail(
        user.email,
        verificationToken.token,
        verificationLink
      );
      
      if (emailSent) {
        // Créer une notification pour informer l'utilisateur
        await storage.createNotification({
          targetUserId: user.id,
          title: 'Email de vérification envoyé',
          body: 'Un email de vérification a été envoyé à votre adresse email.',
          isGlobal: false
        });
        
        res.status(200).json({ 
          message: "Email de vérification envoyé avec succès",
          email: user.email 
        });
      } else {
        res.status(500).json({ message: "Échec de l'envoi de l'email de vérification" });
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email de vérification:', error);
      res.status(500).json({ message: "Erreur serveur" });
    }
  });
  
  // Endpoint pour vérifier l'email avec le jeton
  // Route pour envoyer un code OTP au numéro de téléphone
  app.post(`${apiPrefix}/phone/send-verification`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est connecté
      if (!req.user) {
        return res.status(401).json({ message: "Non authentifié" });
      }
      
      const { phone } = req.body;
      
      if (!phone) {
        return res.status(400).json({ message: "Numéro de téléphone requis" });
      }
      
      // Générer un code OTP à 6 chiffres
      const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
      
      // Sauvegarder le code dans la base de données
      await storage.updateUser(req.user.id, {
        phone,
        phoneVerificationToken: otpCode,
        phoneVerified: false
      });
      
      // Envoyer le SMS avec le code OTP via l'API Aqilas
      const apiKey = process.env.AQILAS_SMS_API_KEY;
      if (!apiKey) {
        return res.status(500).json({ message: "Erreur de configuration de l'API SMS" });
      }
      
      const smsResponse = await axios.post('https://www.aqilas.com/api/v1/sms', {
        from: "Waabo Exp",
        text: `Votre code de vérification est: ${otpCode}. Ne partagez pas ce code.`,
        to: [phone]
      }, {
        headers: {
          'X-AUTH-TOKEN': apiKey,
          'Content-Type': 'application/json'
        }
      });
      
      if (smsResponse.data.success) {
        // Envoyer une notification à l'utilisateur
        const notification = await storage.createNotification({
          title: "Code de vérification envoyé",
          body: `Un code de vérification a été envoyé au numéro ${phone}.`,
          targetUserId: req.user.id,
          isGlobal: false,
          sentAt: new Date(),
        });
        
        // Envoyer la notification en temps réel
        sendNotificationToUser(req.user.id, notification);
        
        return res.status(200).json({ 
          message: "Code de vérification envoyé avec succès", 
          phoneNumber: phone 
        });
      } else {
        return res.status(500).json({ 
          message: "Erreur lors de l'envoi du SMS", 
          error: smsResponse.data.message 
        });
      }
    } catch (error) {
      console.error("Erreur lors de l'envoi du code de vérification:", error);
      res.status(500).json({ 
        message: "Erreur serveur", 
        error: error instanceof Error ? error.message : "Erreur inconnue" 
      });
    }
  });
  
  // Route pour vérifier le code OTP
  app.post(`${apiPrefix}/phone/verify`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est connecté
      if (!req.user) {
        return res.status(401).json({ message: "Non authentifié" });
      }
      
      const { code } = req.body;
      
      if (!code) {
        return res.status(400).json({ message: "Code de vérification requis" });
      }
      
      const user = await storage.getUser(req.user.id);
      
      if (!user) {
        return res.status(404).json({ message: "Utilisateur non trouvé" });
      }
      
      // Vérifier que le code correspond
      if (user.phoneVerificationToken !== code) {
        return res.status(400).json({ message: "Code de vérification invalide" });
      }
      
      // Mettre à jour le numéro de téléphone et le marquer comme vérifié
      await storage.updateUser(req.user.id, {
        phoneVerified: true,
        phoneVerificationToken: null
      });
      
      // Envoyer une notification à l'utilisateur
      const notification = await storage.createNotification({
        title: "Numéro de téléphone vérifié",
        body: "Votre numéro de téléphone a été vérifié avec succès.",
        targetUserId: req.user.id,
        isGlobal: false,
        sentAt: new Date(),
      });
      
      // Envoyer la notification en temps réel
      sendNotificationToUser(req.user.id, notification);
      
      // Récupérer les informations utilisateur mises à jour
      const updatedUser = await storage.getUser(req.user.id);
      const { password, ...userWithoutPassword } = updatedUser || {};
      
      return res.status(200).json({ 
        message: "Numéro de téléphone vérifié avec succès",
        user: userWithoutPassword
      });
    } catch (error) {
      console.error("Erreur lors de la vérification du numéro de téléphone:", error);
      res.status(500).json({ 
        message: "Erreur serveur", 
        error: error instanceof Error ? error.message : "Erreur inconnue" 
      });
    }
  });

  app.get(`${apiPrefix}/email/verify`, async (req: Request, res: Response) => {
    try {
      const { token } = req.query;
      
      console.log(`Tentative de vérification d'email avec le token: ${token}`);
      
      if (!token || typeof token !== 'string') {
        console.log("Jeton manquant ou invalide");
        return res.status(400).json({ message: "Jeton de vérification manquant ou invalide" });
      }
      
      // Obtenir le jeton pour vérifier s'il existe
      const verificationToken = await storage.getEmailVerificationTokenByToken(token);
      
      if (!verificationToken) {
        console.log("Jeton introuvable dans la base de données");
        return res.status(400).json({ 
          message: "Jeton de vérification non trouvé",
          redirectUrl: '/profile?verificationStatus=error'
        });
      }
      
      // Vérifier si l'utilisateur existe avant de continuer
      const user = await storage.getUser(verificationToken.userId);
      if (!user) {
        console.log(`Utilisateur avec ID ${verificationToken.userId} non trouvé`);
        return res.status(404).json({ 
          message: "Utilisateur associé au jeton non trouvé",
          redirectUrl: '/profile?verificationStatus=error'
        });
      }
      
      // Vérifier si l'email est déjà vérifié
      if (user.emailVerified) {
        console.log(`L'email de l'utilisateur ${user.id} est déjà vérifié`);
        
        // Récupérer et marquer comme lues toutes les notifications de vérification d'email
        // pour éviter qu'elles s'affichent en boucle sous forme de popups
        const notificationTitles = ['Email vérifié avec succès', 'Email de vérification envoyé', 'Vérification d\'email requise'];
        const userNotifications = await storage.getNotificationsForUser(user.id);
        
        // Filtrer les notifications de vérification d'email et les marquer comme lues
        for (const notification of userNotifications) {
          if (notificationTitles.includes(notification.title) && !notification.isRead) {
            await storage.markNotificationAsRead(notification.id);
            console.log(`Notification ${notification.id} (${notification.title}) marquée comme lue`);
          }
        }
        
        console.log(`Notifications de vérification d'email traitées pour l'utilisateur ${user.id}`);
        
        return res.status(200).json({ 
          message: "Votre email a déjà été vérifié",
          redirectUrl: '/profile?verificationStatus=success',
          alreadyVerified: true
        });
      }
      
      // Valider le jeton (vérifier s'il n'est pas expiré ou utilisé)
      const isValid = await storage.validateEmailVerificationToken(token);
      
      if (!isValid) {
        console.log("Jeton invalide ou expiré");
        return res.status(400).json({ 
          message: "Jeton de vérification invalide ou expiré",
          redirectUrl: '/profile?verificationStatus=invalid'
        });
      }
      
      // Marquer le jeton comme utilisé
      const tokenMarked = await storage.markEmailVerificationTokenAsUsed(token);
      if (!tokenMarked) {
        console.log("Échec du marquage du jeton comme utilisé");
      }
      
      // Marquer l'email de l'utilisateur comme vérifié
      const updatedUser = await storage.markUserEmailAsVerified(verificationToken.userId);
      
      if (!updatedUser) {
        console.log(`Échec de la vérification pour l'utilisateur ${verificationToken.userId}`);
        return res.status(500).json({ 
          message: "Échec lors de la mise à jour de l'utilisateur",
          redirectUrl: '/profile?verificationStatus=error'
        });
      }
      
      console.log(`Email vérifié avec succès pour l'utilisateur ${updatedUser.id}`);
      
      // Créer une notification pour informer l'utilisateur
      await storage.createNotification({
        targetUserId: updatedUser.id,
        title: 'Email vérifié avec succès',
        body: 'Votre adresse email a été vérifiée avec succès. Merci !',
        isGlobal: false
      });
      
      res.status(200).json({ 
        message: "Email vérifié avec succès",
        redirectUrl: '/profile?verificationStatus=success'
      });
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'email:', error);
      res.status(500).json({ 
        message: "Erreur serveur",
        redirectUrl: '/profile?verificationStatus=error'
      });
    }
  });

  // Order routes

  // Endpoint pour récupérer les commandes d'un utilisateur spécifique
  app.get(`${apiPrefix}/orders/user/:userId`, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "ID utilisateur invalide" });
      }

      // Vérifier que l'utilisateur accède à ses propres commandes ou est un admin
      if (req.isAuthenticated()) {
        if (req.user.id !== userId && req.user.username !== 'admin') {
          return res.status(403).json({ message: "Non autorisé à accéder aux commandes de cet utilisateur" });
        }

        const orders = await storage.getOrdersByUserId(userId);
        res.status(200).json(orders);
      } else {
        res.status(401).json({ message: "Non authentifié" });
      }
    } catch (error) {
      console.error('Error fetching user orders:', error);
      res.status(500).json({ message: "Échec de la récupération des commandes de l'utilisateur" });
    }
  });
  
  // Endpoint pour récupérer les commandes sauvegardées d'un utilisateur
  app.get(`${apiPrefix}/orders/saved/:userId`, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "ID utilisateur invalide" });
      }

      // Vérifier que l'utilisateur accède à ses propres commandes sauvegardées ou est un admin
      if (req.isAuthenticated()) {
        if (req.user.id !== userId && req.user.username !== 'admin') {
          return res.status(403).json({ message: "Non autorisé à accéder aux commandes sauvegardées de cet utilisateur" });
        }

        const savedOrders = await storage.getSavedOrdersForUser(userId);
        res.status(200).json(savedOrders);
      } else {
        res.status(401).json({ message: "Non authentifié" });
      }
    } catch (error) {
      console.error('Error fetching saved orders:', error);
      res.status(500).json({ message: "Échec de la récupération des commandes sauvegardées" });
    }
  });
  
  // Endpoint pour sauvegarder une commande
  app.post(`${apiPrefix}/orders/save`, async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Non authentifié" });
      }
      
      const { orderId } = req.body;
      if (!orderId) {
        return res.status(400).json({ message: "ID de commande requis" });
      }
      
      // Vérifier que la commande existe
      const order = await storage.getOrder(orderId);
      if (!order) {
        return res.status(404).json({ message: "Commande non trouvée" });
      }
      
      const savedOrder = await storage.saveOrder(req.user.id, orderId);
      res.status(200).json(savedOrder);
    } catch (error) {
      console.error('Error saving order:', error);
      res.status(500).json({ message: "Échec de la sauvegarde de la commande" });
    }
  });
  
  // Endpoint pour supprimer une commande sauvegardée
  app.delete(`${apiPrefix}/orders/saved/:orderId`, async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Non authentifié" });
      }
      
      const orderId = parseInt(req.params.orderId);
      if (isNaN(orderId)) {
        return res.status(400).json({ message: "ID de commande invalide" });
      }
      
      const success = await storage.removeSavedOrder(req.user.id, orderId);
      if (success) {
        res.status(200).json({ message: "Commande supprimée des favoris" });
      } else {
        res.status(404).json({ message: "Commande sauvegardée non trouvée" });
      }
    } catch (error) {
      console.error('Error removing saved order:', error);
      res.status(500).json({ message: "Échec de la suppression de la commande sauvegardée" });
    }
  });
  
  // Endpoint pour vérifier si une commande est sauvegardée
  app.get(`${apiPrefix}/orders/saved/check/:orderId`, async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Non authentifié" });
      }
      
      const orderId = parseInt(req.params.orderId);
      if (isNaN(orderId)) {
        return res.status(400).json({ message: "ID de commande invalide" });
      }
      
      const isSaved = await storage.isSavedOrder(req.user.id, orderId);
      res.status(200).json({ isSaved });
    } catch (error) {
      console.error('Error checking if order is saved:', error);
      res.status(500).json({ message: "Échec de la vérification si la commande est sauvegardée" });
    }
  });
  app.get(`${apiPrefix}/orders`, async (_req: Request, res: Response) => {
    try {
      // Utiliser la nouvelle méthode pour récupérer toutes les commandes
      const allOrders = await storage.getAllOrders();
      res.status(200).json(allOrders);
    } catch (error) {
      console.error("Error fetching orders:", error);
      res.status(500).json({ message: "Failed to retrieve orders" });
    }
  });

  app.get(`${apiPrefix}/orders/:reference`, async (req: Request, res: Response) => {
    try {
      const { reference } = req.params;
      const order = await storage.getOrderByReference(reference);

      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      res.status(200).json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to retrieve order" });
    }
  });

  app.post(`${apiPrefix}/orders`, async (req: Request, res: Response) => {
    try {
      const orderData = insertOrderSchema.parse(req.body);

      // Check if order with this reference already exists
      const existingOrder = await storage.getOrderByReference(orderData.reference);
      if (existingOrder) {
        return res.status(400).json({ message: "Order with this reference already exists" });
      }

      const order = await storage.createOrder(orderData);
      res.status(201).json(order);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid order data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create order" });
    }
  });

  app.patch(`${apiPrefix}/orders/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { status, latestUpdate } = req.body;

      // Validate order status
      const validatedStatus = orderStatusEnum.safeParse(status);
      if (!validatedStatus.success) {
        return res.status(400).json({ message: "Invalid order status" });
      }

      const updatedOrder = await storage.updateOrderStatus(
        parseInt(id), 
        validatedStatus.data,
        latestUpdate
      );

      if (!updatedOrder) {
        return res.status(404).json({ message: "Order not found" });
      }

      res.status(200).json(updatedOrder);
    } catch (error) {
      res.status(500).json({ message: "Failed to update order" });
    }
  });

  // Product routes
  app.get(`${apiPrefix}/products`, async (_req: Request, res: Response) => {
    try {
      const products = await storage.getAllProducts();
      res.status(200).json(products);
    } catch (error) {
      res.status(500).json({ message: "Failed to retrieve products" });
    }
  });

  app.get(`${apiPrefix}/products/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const product = await storage.getProduct(parseInt(id));

      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.status(200).json(product);
    } catch (error) {
      res.status(500).json({ message: "Failed to retrieve product" });
    }
  });

  app.post(`${apiPrefix}/products`, async (req: Request, res: Response) => {
    try {
      const productData = insertProductSchema.parse(req.body);
      const product = await storage.createProduct(productData);
      res.status(201).json(product);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid product data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create product" });
    }
  });

  app.patch(`${apiPrefix}/products/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const productUpdate = req.body;

      const updatedProduct = await storage.updateProduct(parseInt(id), productUpdate);

      if (!updatedProduct) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.status(200).json(updatedProduct);
    } catch (error) {
      res.status(500).json({ message: "Failed to update product" });
    }
  });

  app.delete(`${apiPrefix}/products/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const result = await storage.deleteProduct(parseInt(id));

      if (!result) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Failed to delete product" });
    }
  });

  // Notification routes
  app.post(`${apiPrefix}/notifications`, async (req: Request, res: Response) => {
    try {
      const notificationData = insertNotificationSchema.parse(req.body);

      // Vérifier si l'utilisateur a la date de création après la date de la notification
      // Si l'utilisateur est spécifié et qu'il existe
      if (notificationData.targetUserId) {
        const targetUser = await storage.getUser(notificationData.targetUserId);
        if (targetUser) {
          // Ne créer la notification que si l'utilisateur est créé avant la notification
          const userCreatedAt = targetUser.createdAt ? new Date(targetUser.createdAt) : new Date();
          const now = new Date();

          // Si l'utilisateur a été créé après maintenant moins 1 minute (pour gérer les décalages d'horloge)
          // ne pas créer la notification
          if (userCreatedAt > new Date(now.getTime() - 60000)) {
            console.log(`Ignoré notification pour l'utilisateur ${targetUser.id} qui vient de s'inscrire`);
            return res.status(201).json({ message: "Nouvel utilisateur, notification ignorée" });
          }
        }
      }

      const notification = await storage.createNotification(notificationData);

      // Stocker la notification dans res.locals pour le middleware WebSocket
      res.locals.notification = notification;

      res.status(201).json(notification);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid notification data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create notification" });
    }
  });

  app.get(`${apiPrefix}/notifications/user/:userId`, async (req: Request, res: Response) => {
    try {
      const { userId } = req.params;
      const notifications = await storage.getNotificationsForUser(parseInt(userId));
      res.status(200).json(notifications);
    } catch (error) {
      res.status(500).json({ message: "Failed to retrieve notifications" });
    }
  });

  app.patch(`${apiPrefix}/notifications/:id/read`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const notification = await storage.markNotificationAsRead(parseInt(id));
      if (!notification) {
        return res.status(404).json({ message: "Notification not found" });
      }
      res.status(200).json(notification);
    } catch (error) {
      res.status(500).json({ message: "Failed to mark notification as read" });
    }
  });

  // Order Status routes
  app.get(`${apiPrefix}/order-statuses`, async (_req: Request, res: Response) => {
    try {
      const statuses = await storage.getAllOrderStatuses();
      res.status(200).json(statuses);
    } catch (error) {
      res.status(500).json({ message: "Failed to retrieve order statuses" });
    }
  });

  app.get(`${apiPrefix}/order-statuses/active`, async (_req: Request, res: Response) => {
    try {
      const statuses = await storage.getActiveOrderStatuses();
      res.status(200).json(statuses);
    } catch (error) {
      res.status(500).json({ message: "Failed to retrieve active order statuses" });
    }
  });

  app.get(`${apiPrefix}/order-statuses/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const status = await storage.getOrderStatus(parseInt(id));

      if (!status) {
        return res.status(404).json({ message: "Order status not found" });
      }

      res.status(200).json(status);
    } catch (error) {
      res.status(500).json({ message: "Failed to retrieve order status" });
    }
  });

  app.post(`${apiPrefix}/order-statuses`, async (req: Request, res: Response) => {
    try {
      const statusData = insertOrderStatusSchema.parse(req.body);

      // Check if status with this code already exists
      const existingStatus = await storage.getOrderStatusByCode(statusData.code);
      if (existingStatus) {
        return res.status(400).json({ message: "Order status with this code already exists" });
      }

      const status = await storage.createOrderStatus(statusData);
      res.status(201).json(status);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid order status data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create order status" });
    }
  });

  app.patch(`${apiPrefix}/order-statuses/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const statusUpdate = req.body;

      // If code is being updated, check if it already exists on a different status
      if (statusUpdate.code) {
        const existingStatus = await storage.getOrderStatusByCode(statusUpdate.code);
        if (existingStatus && existingStatus.id !== parseInt(id)) {
          return res.status(400).json({ message: "Order status with this code already exists" });
        }
      }

      const updatedStatus = await storage.updateOrderStatusEntry(parseInt(id), statusUpdate);

      if (!updatedStatus) {
        return res.status(404).json({ message: "Order status not found" });
      }

      res.status(200).json(updatedStatus);
    } catch (error) {
      res.status(500).json({ message: "Failed to update order status" });
    }
  });

  app.delete(`${apiPrefix}/order-statuses/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const result = await storage.deleteOrderStatus(parseInt(id));

      if (!result) {
        return res.status(404).json({ message: "Order status not found" });
      }

      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Failed to delete order status" });
    }
  });

  // Routes pour les pages statiques
  app.get(`${apiPrefix}/static-pages`, async (_req: Request, res: Response) => {
    try {
      const pages = await storage.getAllStaticPages();
      res.json(pages);
    } catch (error) {
      console.error("Error getting all static pages:", error);
      res.status(500).json({ message: "Erreur lors de la récupération des pages statiques" });
    }
  });

  app.get(`${apiPrefix}/static-pages/:slug`, async (req: Request, res: Response) => {
    try {
      console.log(`Requête de page statique pour le slug: ${req.params.slug}`);

      if (!req.params.slug) {
        return res.status(400).json({ message: "Slug manquant" });
      }

      const page = await storage.getStaticPage(req.params.slug);

      if (page) {
        console.log(`Page trouvée: ${page.title}`);
        res.json(page);
      } else {
        console.log(`Aucune page trouvée avec le slug: ${req.params.slug}`);
        res.status(404).json({ message: "Page statique non trouvée" });
      }
    } catch (error) {
      console.error(`Error getting static page with slug ${req.params.slug}:`, error);
      res.status(500).json({ message: "Erreur lors de la récupération de la page statique" });
    }
  });

  app.patch(`${apiPrefix}/static-pages/:slug`, async (req: Request, res: Response) => {
    try {
      // Autoriser les mises à jour sans authentification pour le moment
      // car nous avons un problème de persistance de session
      // Note: En production, il faudrait rétablir la vérification d'authentification
      /*if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Non authentifié" });
      }*/

      // Log pour le débogage
      console.log("Requête de mise à jour reçue pour:", req.params.slug, req.body);

      const updatedPage = await storage.updateStaticPage(req.params.slug, req.body);
      if (updatedPage) {
        console.log("Page mise à jour avec succès:", updatedPage);
        res.json(updatedPage);
      } else {
        console.log("Page non trouvée pour la mise à jour");
        res.status(404).json({ message: "Page statique non trouvée" });
      }
    } catch (error) {
      console.error(`Error updating static page with slug ${req.params.slug}:`, error);
      res.status(500).json({ message: "Erreur lors de la mise à jour de la page statique" });
    }
  });

  // Routes pour les notifications popup
  app.get(`${apiPrefix}/popup-notifications`, async (_req: Request, res: Response) => {
    try {
      const popups = await storage.getAllPopupNotifications();
      res.status(200).json(popups);
    } catch (error) {
      console.error('Error getting popup notifications:', error);
      res.status(500).json({ message: "Failed to retrieve popup notifications" });
    }
  });
  
  app.get(`${apiPrefix}/popup-notifications/active`, async (_req: Request, res: Response) => {
    try {
      const popups = await storage.getActivePopupNotifications();
      res.status(200).json(popups);
    } catch (error) {
      console.error('Error getting active popup notifications:', error);
      res.status(500).json({ message: "Failed to retrieve active popup notifications" });
    }
  });
  
  app.get(`${apiPrefix}/popup-notifications/unseen/user/:userId`, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      if (isNaN(userId)) {
        return res.status(400).json({ message: "Invalid user ID format" });
      }
      
      // Vérifier que l'utilisateur demande ses propres notifications ou est un admin
      if (req.isAuthenticated() && (req.user.id === userId || req.user.username === 'admin')) {
        const popups = await storage.getUnseenPopupsForUser(userId);
        res.status(200).json(popups);
      } else {
        res.status(403).json({ message: "Permission denied" });
      }
    } catch (error) {
      console.error('Error getting unseen popups for user:', error);
      res.status(500).json({ message: "Failed to retrieve unseen popup notifications" });
    }
  });
  
  app.get(`${apiPrefix}/popup-notifications/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid ID format" });
      }
      
      const popup = await storage.getPopupNotification(id);
      if (!popup) {
        return res.status(404).json({ message: "Popup notification not found" });
      }
      
      res.status(200).json(popup);
    } catch (error) {
      console.error('Error getting popup notification:', error);
      res.status(500).json({ message: "Failed to retrieve popup notification" });
    }
  });
  
  app.post(`${apiPrefix}/popup-notifications`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est un admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Permission denied" });
      }
      
      const data = req.body;
      
      // Convertir les chaînes de date en objets Date
      if (data.startDate && typeof data.startDate === 'string') {
        data.startDate = new Date(data.startDate);
      }
      
      if (data.endDate && typeof data.endDate === 'string') {
        data.endDate = new Date(data.endDate);
      }
      
      const popup = await storage.createPopupNotification(data);
      res.status(201).json(popup);
    } catch (error) {
      console.error('Error creating popup notification:', error);
      res.status(500).json({ message: "Failed to create popup notification" });
    }
  });
  
  app.patch(`${apiPrefix}/popup-notifications/:id`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est un admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Permission denied" });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid ID format" });
      }
      
      const data = req.body;
      
      // Convertir les chaînes de date en objets Date
      if (data.startDate && typeof data.startDate === 'string') {
        data.startDate = new Date(data.startDate);
      }
      
      if (data.endDate && typeof data.endDate === 'string') {
        data.endDate = new Date(data.endDate);
      }
      
      const updatedPopup = await storage.updatePopupNotification(id, data);
      
      if (!updatedPopup) {
        return res.status(404).json({ message: "Popup notification not found" });
      }
      
      res.status(200).json(updatedPopup);
    } catch (error) {
      console.error('Error updating popup notification:', error);
      res.status(500).json({ message: "Failed to update popup notification" });
    }
  });
  
  app.delete(`${apiPrefix}/popup-notifications/:id`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est un admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Permission denied" });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid ID format" });
      }
      
      const success = await storage.deletePopupNotification(id);
      
      if (!success) {
        return res.status(404).json({ message: "Popup notification not found or could not be deleted" });
      }
      
      res.status(200).json({ message: "Popup notification deleted successfully" });
    } catch (error) {
      console.error('Error deleting popup notification:', error);
      res.status(500).json({ message: "Failed to delete popup notification" });
    }
  });
  
  app.post(`${apiPrefix}/popup-notifications/:popupId/seen`, async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Unauthorized" });
      }
      
      const popupId = parseInt(req.params.popupId);
      if (isNaN(popupId)) {
        return res.status(400).json({ message: "Invalid popup ID format" });
      }
      
      const userId = req.user.id;
      
      await storage.markPopupAsSeen(userId, popupId);
      res.status(200).json({ message: "Popup marked as seen" });
    } catch (error) {
      console.error('Error marking popup as seen:', error);
      res.status(500).json({ message: "Failed to mark popup as seen" });
    }
  });

  const httpServer = createServer(app);

  // Configuration du serveur WebSocket
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  wss.on('connection', (ws) => {
    console.log('Nouvelle connexion WebSocket');

    // Authentification et attribution de l'utilisateur
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());

        // Message d'authentification
        if (data.type === 'auth' && data.userId) {
          const userId = parseInt(data.userId);

          // Stocker la connexion pour cet utilisateur
          if (!userConnections.has(userId)) {
            userConnections.set(userId, []);
          }

          // Ajouter cette connexion à la liste des connexions de l'utilisateur
          userConnections.get(userId)!.push(ws);

          console.log(`Utilisateur ${userId} connecté via WebSocket`);

          // Nettoyer les connexions lorsque la connexion est fermée
          ws.on('close', () => {
            const connections = userConnections.get(userId);
            if (connections) {
              // Supprimer cette connexion de la liste
              const index = connections.indexOf(ws);
              if (index !== -1) {
                connections.splice(index, 1);
              }

              // Si plus aucune connexion pour cet utilisateur, supprimer l'entrée
              if (connections.length === 0) {
                userConnections.delete(userId);
              }
            }

            console.log(`Connexion WebSocket fermée pour l'utilisateur ${userId}`);
          });
        }
      } catch (error) {
        console.error('Erreur de traitement du message WebSocket:', error);
      }
    });
  });

  // Modifier la route de création de notification pour utiliser WebSocket
  const notificationRoute = app._router.stack.find((layer: any) => 
    layer.route && layer.route.path === `${apiPrefix}/notifications` && layer.route.methods.post
  );

  if (notificationRoute && notificationRoute.route) {
    // Conserver l'ancien handler
    const originalHandler = notificationRoute.route.stack[0].handle;

    // Remplacer par un nouveau handler qui envoie aussi via WebSocket
    notificationRoute.route.stack[0].handle = async (req: Request, res: Response) => {
      // Exécuter l'handler original d'abord
      await originalHandler(req, res);

      // Si la notification a été créée avec succès
      if (res.statusCode === 201 && res.locals.notification) {
        const notification = res.locals.notification;

        // Si c'est une notification globale, l'envoyer à tous les utilisateurs connectés
        if (notification.isGlobal) {
          sendGlobalNotification(notification);
        } 
        // Sinon, l'envoyer à l'utilisateur cible
        else if (notification.targetUserId) {
          sendNotificationToUser(notification.targetUserId, notification);
        }
      }
    };
  }

  // Routes pour les paramètres d'application
  app.get(`${apiPrefix}/app-settings`, async (req: Request, res: Response) => {
    try {
      // Vérifier si l'utilisateur est un admin
      if (req.isAuthenticated() && req.user.username === 'admin') {
        const settings = await storage.getAllAppSettings();
        res.status(200).json(settings);
      } else {
        res.status(403).json({ message: "Accès refusé. Seul l'administrateur peut accéder à ces données." });
      }
    } catch (error) {
      console.error('Error fetching app settings:', error);
      res.status(500).json({ message: "Erreur lors de la récupération des paramètres d'application" });
    }
  });

  app.get(`${apiPrefix}/app-settings/:key`, async (req: Request, res: Response) => {
    try {
      const key = req.params.key;
      const setting = await storage.getAppSetting(key);
      
      if (setting) {
        res.status(200).json(setting);
      } else {
        res.status(404).json({ message: `Paramètre '${key}' non trouvé` });
      }
    } catch (error) {
      console.error(`Error fetching app setting with key ${req.params.key}:`, error);
      res.status(500).json({ message: "Erreur lors de la récupération du paramètre d'application" });
    }
  });

  app.post(`${apiPrefix}/app-settings/:key`, async (req: Request, res: Response) => {
    try {
      // Ajout de logs de débogage détaillés
      console.log("=== Début requête POST app-settings ===");
      console.log("POST app-settings - Params:", req.params);
      console.log("POST app-settings - Body:", req.body);
      console.log("POST app-settings - Auth status:", req.isAuthenticated());
      console.log("POST app-settings - Session:", req.session);
      console.log("POST app-settings - User ID:", req.user?.id);
      console.log("POST app-settings - Username:", req.user?.username);
      
      // Problème: Le req.user n'est pas correctement typé, récupérons l'utilisateur directement depuis la base
      let adminUser = req.user;
      if (req.isAuthenticated() && req.user && req.user.id) {
        try {
          // Récupérer l'utilisateur complet depuis le stockage
          const fullUser = await storage.getUser(req.user.id);
          console.log("Utilisateur récupéré de la base:", fullUser);
          if (fullUser) {
            adminUser = fullUser;
          }
        } catch (err) {
          console.error("Erreur lors de la récupération de l'utilisateur:", err);
        }
      }
      
      // Vérifier si l'utilisateur est un admin (même logique mais avec l'utilisateur complet)
      if (req.isAuthenticated() && adminUser && adminUser.username === 'admin') {
        console.log("Authentication réussie pour l'admin");
        
        const key = req.params.key;
        const { value, description } = req.body;
        
        if (!value) {
          return res.status(400).json({ message: "La valeur est requise" });
        }
        
        console.log(`Mise à jour du paramètre ${key} avec la valeur ${value}`);
        const setting = await storage.createOrUpdateAppSetting(key, value, description);
        console.log("Paramètre mis à jour avec succès:", setting);
        
        res.status(201).json(setting);
      } else {
        console.log("POST app-settings - Accès refusé");
        console.log("Username attendu: 'admin', username reçu:", adminUser?.username);
        
        res.status(403).json({ 
          message: "Accès refusé. Seul l'administrateur peut modifier ces données.",
          authStatus: req.isAuthenticated(),
          user: req.user ? {
            id: req.user.id,
            username: req.user.username
          } : null
        });
      }
      console.log("=== Fin requête POST app-settings ===");
    } catch (error) {
      console.error(`Error creating/updating app setting with key ${req.params.key}:`, error);
      res.status(500).json({ message: "Erreur lors de la création/mise à jour du paramètre d'application" });
    }
  });

  // Route spécifique pour le numéro WhatsApp utilisé dans ProductDetail
  app.get(`${apiPrefix}/whatsapp-number`, async (_req: Request, res: Response) => {
    try {
      const setting = await storage.getAppSetting('whatsapp_number');
      const whatsappNumber = setting ? setting.value : '+22670000000'; // Valeur par défaut
      
      res.status(200).json({ whatsappNumber });
    } catch (error) {
      console.error('Error fetching WhatsApp number:', error);
      res.status(500).json({ message: "Erreur lors de la récupération du numéro WhatsApp" });
    }
  });
  
  // Routes pour les messages de contact
  
  // Soumission de nouveau message de contact
  app.post(`${apiPrefix}/contact-messages`, async (req: Request, res: Response) => {
    try {
      // Validation des données avec le schéma Zod
      const validatedData = insertContactMessageSchema.safeParse(req.body);
      
      if (!validatedData.success) {
        return res.status(400).json({ 
          message: "Données de formulaire invalides", 
          errors: validatedData.error.format() 
        });
      }
      
      // Créer le message
      const newMessage = await storage.createContactMessage(validatedData.data);
      
      // Créer une notification uniquement pour l'administrateur (ID 1)
      await storage.createNotification({
        title: "Nouveau message de contact",
        body: `De: ${newMessage.firstName} ${newMessage.lastName} (${newMessage.email})`,
        targetUserId: 1,
        isGlobal: false
      });
      
      res.status(201).json({ 
        message: "Message de contact envoyé avec succès", 
        id: newMessage.id 
      });
    } catch (error) {
      console.error('Error creating contact message:', error);
      res.status(500).json({ message: "Échec de l'envoi du message de contact" });
    }
  });
  
  // Liste des messages de contact (admin seulement)
  app.get(`${apiPrefix}/contact-messages`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé" });
      }
      
      const messages = await storage.getAllContactMessages();
      res.status(200).json(messages);
    } catch (error) {
      console.error('Error fetching contact messages:', error);
      res.status(500).json({ message: "Échec de la récupération des messages de contact" });
    }
  });
  
  // Messages non lus (admin seulement)
  app.get(`${apiPrefix}/contact-messages/unread`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé" });
      }
      
      const messages = await storage.getUnreadContactMessages();
      res.status(200).json(messages);
    } catch (error) {
      console.error('Error fetching unread contact messages:', error);
      res.status(500).json({ message: "Échec de la récupération des messages non lus" });
    }
  });
  
  // Détails d'un message spécifique (admin seulement)
  app.get(`${apiPrefix}/contact-messages/:id`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé" });
      }
      
      const messageId = parseInt(req.params.id);
      if (isNaN(messageId)) {
        return res.status(400).json({ message: "ID de message invalide" });
      }
      
      const message = await storage.getContactMessage(messageId);
      if (!message) {
        return res.status(404).json({ message: "Message non trouvé" });
      }
      
      res.status(200).json(message);
    } catch (error) {
      console.error(`Error fetching contact message ${req.params.id}:`, error);
      res.status(500).json({ message: "Échec de la récupération du message" });
    }
  });
  
  // Marquer un message comme lu (admin seulement)
  app.patch(`${apiPrefix}/contact-messages/:id/read`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé" });
      }
      
      const messageId = parseInt(req.params.id);
      if (isNaN(messageId)) {
        return res.status(400).json({ message: "ID de message invalide" });
      }
      
      const updatedMessage = await storage.markContactMessageAsRead(messageId);
      if (!updatedMessage) {
        return res.status(404).json({ message: "Message non trouvé" });
      }
      
      res.status(200).json({ message: "Message marqué comme lu", data: updatedMessage });
    } catch (error) {
      console.error(`Error marking contact message ${req.params.id} as read:`, error);
      res.status(500).json({ message: "Échec de la mise à jour du message" });
    }
  });
  
  // Supprimer un message (admin seulement)
  app.delete(`${apiPrefix}/contact-messages/:id`, async (req: Request, res: Response) => {
    try {
      // Vérifier que l'utilisateur est admin
      if (!req.isAuthenticated() || req.user.username !== 'admin') {
        return res.status(403).json({ message: "Non autorisé" });
      }
      
      const messageId = parseInt(req.params.id);
      if (isNaN(messageId)) {
        return res.status(400).json({ message: "ID de message invalide" });
      }
      
      const success = await storage.deleteContactMessage(messageId);
      if (!success) {
        return res.status(404).json({ message: "Message non trouvé ou déjà supprimé" });
      }
      
      res.status(200).json({ message: "Message supprimé avec succès" });
    } catch (error) {
      console.error(`Error deleting contact message ${req.params.id}:`, error);
      res.status(500).json({ message: "Échec de la suppression du message" });
    }
  });
  
  // Route pour l'authentification Facebook via l'application mobile
  app.post(`${apiPrefix}/auth/facebook-app`, handleAppFacebookAuth);
  
  // Route pour l'authentification Google via l'application mobile
  app.post(`${apiPrefix}/auth/google-app`, handleAppGoogleAuth);
  
  // Route pour l'authentification Apple via l'application mobile (sans popup)
  app.post(`${apiPrefix}/auth/apple-app`, handleAppAppleAuth);

  // Routes pour les notifications push mobiles
  
  // Middleware pour vérifier l'authentification (JWT + Session)
  const checkAuth = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Essayer JWT d'abord
      const authHeader = req.headers.authorization;

      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET || 'waabo-secret-key-for-app-auth';

        try {
          const jwt = require('jsonwebtoken');
          const decoded = jwt.verify(token, jwtSecret) as any;

          if (decoded && decoded.id) {
            const { storage } = require('./storage');
            const user = await storage.getUser(decoded.id);

            if (user) {
              req.jwtUser = user;
              return next();
            }
          }
        } catch (jwtError) {
          console.log('JWT verification failed, falling back to session');
        }
      }

      // Fallback vers session auth
      if (req.isAuthenticated()) {
        return next();
      }

      return res.status(401).json({ success: false, message: "Non authentifié" });
    } catch (error) {
      console.error('Auth verification error:', error);
      return res.status(401).json({ success: false, message: "Non authentifié" });
    }
  };

  // Middleware pour vérifier si l'utilisateur est admin
  const checkAdmin = (req: Request, res: Response, next: NextFunction) => {
    if (req.isAuthenticated() && req.user && req.user.username === 'admin') {
      return next();
    }
    return res.status(403).json({ success: false, message: "Accès non autorisé" });
  };
  
  // Enregistrer un token de notification push
  app.post(`${apiPrefix}/push-token`, checkAuth, async (req: Request, res: Response) => {
    const user = req.jwtUser || req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Non authentifié'
      });
    }

    const { token, deviceInfo, platform } = req.body;
    const userId = user.id;

    if (!token) {
      return res.status(400).json({ 
        success: false, 
        message: 'Token de notification push manquant' 
      });
    }

    try {
      const success = await pushNotificationService.registerToken(userId, token);
      
      if (success) {
        return res.status(200).json({ 
          success: true, 
          message: 'Token de notification push enregistré avec succès' 
        });
      } else {
        return res.status(500).json({ 
          success: false, 
          message: 'Erreur lors de l\'enregistrement du token' 
        });
      }
    } catch (error) {
      console.error('Erreur d\'enregistrement du token push:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Erreur serveur lors de l\'enregistrement du token' 
      });
    }
  });

  // Supprimer un token de notification push
  app.delete(`${apiPrefix}/push-token/:token`, checkAuth, async (req: Request, res: Response) => {
    const { token } = req.params;

    try {
      const success = await pushNotificationService.unregisterToken(token);
      
      if (success) {
        return res.status(200).json({ 
          success: true, 
          message: 'Token de notification push supprimé avec succès' 
        });
      } else {
        return res.status(500).json({ 
          success: false, 
          message: 'Erreur lors de la suppression du token' 
        });
      }
    } catch (error) {
      console.error('Erreur de suppression du token push:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Erreur serveur lors de la suppression du token' 
      });
    }
  });

  return httpServer;
}