# Guide Technique: Système d'Authentification Multi-Méthodes de Waabo

## Introduction

Ce document détaille l'architecture et l'implémentation du système d'authentification multi-méthodes de Waabo. L'application propose plusieurs méthodes d'authentification pour faciliter l'accès aux utilisateurs tout en maintenant un haut niveau de sécurité.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Méthodes d'authentification](#méthodes-dauthentification)
3. [Architecture technique](#architecture-technique)
4. [Implémentation backend](#implémentation-backend)
5. [Implémentation frontend](#implémentation-frontend)
6. [Sécurité](#sécurité)
7. [Flux d'authentification](#flux-dauthentification)
8. [Gestion des sessions](#gestion-des-sessions)
9. [Récupération et réinitialisation de mot de passe](#récupération-et-réinitialisation-de-mot-de-passe)
10. [Vérification d'email](#vérification-demail)
11. [Tests et débogage](#tests-et-débogage)
12. [Bonnes pratiques](#bonnes-pratiques)

---

## Vue d'ensemble

Le système d'authentification de Waabo est conçu pour être:

- **Flexible**: Supporte multiples méthodes d'authentification
- **Sécurisé**: Utilise des protocoles d'authentification modernes
- **Intégré**: Fonctionne de manière transparente avec le reste de l'application
- **Extensible**: Facilement extensible pour ajouter de nouvelles méthodes

L'architecture utilise Passport.js comme middleware d'authentification, avec des stratégies personnalisées pour chaque méthode d'authentification.

## Méthodes d'authentification

Waabo prend en charge quatre méthodes d'authentification principales:

1. **Locale (Email/Mot de passe)**:
   - Inscription avec email et mot de passe
   - Authentification via identifiants stockés en base de données
   - Hachage sécurisé des mots de passe

2. **Google OAuth 2.0**:
   - Authentification via compte Google
   - Accès au nom, email et avatar de l'utilisateur
   - Pas de stockage de mot de passe

3. **Facebook OAuth**:
   - Authentification via compte Facebook
   - Accès au profil de base de l'utilisateur
   - Intégration avec l'API Facebook

4. **Apple Sign In**:
   - Authentification via compte Apple
   - Support du "Sign in with Apple"
   - Conformité avec les exigences d'Apple pour les applications iOS

## Architecture technique

L'architecture d'authentification suit ce schéma général:

```
+----------------+         HTTP/HTTPS         +----------------+
|                | <----------------------->  |                |
|  Client React  |     Requêtes Auth          |  Serveur Node  |
|                | <----------------------->  |                |
+----------------+                            +----------------+
                                                     |
                                                     | Passport.js
                                                     v
                                            +----------------+
                                            |  Stratégies    |
                                            |  d'Auth        |
                                            +----------------+
                                                     |
                                                     v
                                            +----------------+
                                            |   Base de      |
                                            |   Données      |
                                            |   Utilisateurs |
                                            +----------------+
```

## Implémentation backend

### Configuration de Passport.js

Le système d'authentification est configuré dans `server/auth.ts`:

```typescript
import passport from 'passport';
import { Strategy as LocalStrategy } from 'passport-local';
import { Strategy as FacebookStrategy } from 'passport-facebook';
import { OAuth2Strategy as GoogleStrategy } from 'passport-google-oauth';
import { Strategy as AppleStrategy } from 'passport-apple';
import { Express } from 'express';
import bcrypt from 'bcrypt';

import { db } from './db';
import { schema } from '../shared/schema';
import { eq } from 'drizzle-orm';

// Hash du mot de passe
export async function hashPassword(password: string) {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

// Comparaison des mots de passe
export async function comparePasswords(supplied: string, stored: string | null) {
  if (!stored) return false;
  return bcrypt.compare(supplied, stored);
}

export function setupAuth(app: Express) {
  // Configuration de Passport pour sérialiser/désérialiser les utilisateurs
  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: number, done) => {
    try {
      const users = await db
        .select()
        .from(schema.users)
        .where(eq(schema.users.id, id));
      
      if (users.length === 0) {
        return done(null, false);
      }
      
      const user = users[0];
      
      // Ne pas exposer le mot de passe
      const { password, ...userWithoutPassword } = user;
      
      done(null, userWithoutPassword);
    } catch (error) {
      done(error, false);
    }
  });

  // Stratégie locale (email/mot de passe)
  passport.use(
    new LocalStrategy(
      {
        usernameField: 'email',
        passwordField: 'password',
      },
      async (email, password, done) => {
        try {
          const users = await db
            .select()
            .from(schema.users)
            .where(eq(schema.users.email, email));
          
          if (users.length === 0) {
            return done(null, false, { message: 'Email ou mot de passe incorrect' });
          }
          
          const user = users[0];
          const isMatch = await comparePasswords(password, user.password);
          
          if (!isMatch) {
            return done(null, false, { message: 'Email ou mot de passe incorrect' });
          }
          
          // Ne pas exposer le mot de passe
          const { password: _, ...userWithoutPassword } = user;
          
          return done(null, userWithoutPassword);
        } catch (error) {
          return done(error);
        }
      }
    )
  );

  // Configuration des autres stratégies (Google, Facebook, Apple)...
}
```

### Configuration des stratégies OAuth

#### Google OAuth

```typescript
// Dans server/google-auth.ts
import { OAuth2Client } from 'google-auth-library';

export const oauth2Client = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  'https://waabo-app.com/api/auth/google/callback'
);

// Dans server/auth.ts
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: 'https://waabo-app.com/api/auth/google/callback',
      scope: ['profile', 'email'],
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Vérifier si l'utilisateur existe déjà
        const users = await db
          .select()
          .from(schema.users)
          .where(eq(schema.users.provider, 'google'))
          .where(eq(schema.users.providerId, profile.id));
        
        if (users.length > 0) {
          // Utilisateur existant
          return done(null, users[0]);
        }
        
        // Créer un nouvel utilisateur
        const [newUser] = await db
          .insert(schema.users)
          .values({
            email: profile.emails?.[0]?.value,
            firstName: profile.name?.givenName,
            lastName: profile.name?.familyName,
            provider: 'google',
            providerId: profile.id,
            emailVerified: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .returning();
        
        return done(null, newUser);
      } catch (error) {
        return done(error as Error);
      }
    }
  )
);
```

#### Facebook OAuth

```typescript
passport.use(
  new FacebookStrategy(
    {
      clientID: process.env.FACEBOOK_APP_ID!,
      clientSecret: process.env.FACEBOOK_APP_SECRET!,
      callbackURL: 'https://waabo-app.com/api/auth/facebook/callback',
      profileFields: ['id', 'emails', 'name'],
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Code similaire à Google OAuth pour vérifier/créer l'utilisateur
      } catch (error) {
        return done(error as Error);
      }
    }
  )
);
```

#### Apple Sign In

```typescript
// Dans server/apple-auth.ts
export function setupAppleAuth(app: Express, storage: IStorage) {
  console.log("Configuration de l'authentification Apple");
  
  const callbackUrl = "https://waabo-app.com/api/auth/apple/callback";
  console.log(`Configuration Apple avec URL de callback: ${callbackUrl}`);
  
  const teamId = process.env.APPLE_TEAM_ID;
  const keyId = process.env.APPLE_KEY_ID;
  const privateKey = process.env.APPLE_PRIVATE_KEY;
  
  console.log(`Utilisation de la clé Apple avec ID: ${keyId}`);
  console.log(`Type de clé privée: ${typeof privateKey}`);
  
  if (privateKey) {
    console.log(`Longueur de la clé privée: ${privateKey.length}`);
    if (privateKey.length < 500) {
      console.log("ATTENTION: La clé privée Apple semble trop courte, vérifiez son format");
    }
  }
  
  passport.use(
    new AppleStrategy(
      {
        clientID: "com.waabo.app",
        teamID: teamId!,
        keyID: keyId!,
        privateKey: privateKey!,
        callbackURL: callbackUrl,
        passReqToCallback: true,
      },
      async (req, accessToken, refreshToken, idToken, profile, done) => {
        try {
          // Logique pour traiter l'authentification Apple
        } catch (error) {
          return done(error as Error);
        }
      }
    )
  );
}
```

### Routes d'authentification

```typescript
// Routes d'authentification locale
app.post('/api/auth/login', 
  passport.authenticate('local'),
  (req, res) => res.json(req.user)
);

app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, firstName, lastName } = req.body;
    
    // Vérification si l'email existe déjà
    const existingUsers = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.email, email));
    
    if (existingUsers.length > 0) {
      return res.status(400).json({ message: "Cet email est déjà utilisé" });
    }
    
    // Hashage du mot de passe
    const hashedPassword = await hashPassword(password);
    
    // Création du nouvel utilisateur
    const [newUser] = await db
      .insert(schema.users)
      .values({
        email,
        password: hashedPassword,
        firstName,
        lastName,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();
    
    // Ne pas exposer le mot de passe
    const { password: _, ...userWithoutPassword } = newUser;
    
    // Connecter automatiquement l'utilisateur
    req.login(userWithoutPassword, (err) => {
      if (err) {
        return res.status(500).json({ message: "Erreur lors de la connexion automatique" });
      }
      return res.status(201).json(userWithoutPassword);
    });
  } catch (error) {
    console.error("Erreur lors de l'inscription:", error);
    return res.status(500).json({ message: "Erreur serveur" });
  }
});

// Routes OAuth pour Google
app.get('/api/auth/google',
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

app.get('/api/auth/google/callback',
  passport.authenticate('google', { failureRedirect: '/auth' }),
  (req, res) => {
    res.redirect('/');
  }
);

// Routes similaires pour Facebook et Apple...

// Déconnexion
app.get('/api/auth/logout', (req, res) => {
  req.logout((err) => {
    if (err) {
      return res.status(500).json({ message: "Erreur lors de la déconnexion" });
    }
    res.status(200).json({ message: "Déconnecté avec succès" });
  });
});
```

## Implémentation frontend

### Contexte d'authentification

```tsx
// src/contexts/AuthContext.tsx
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: number;
  email: string;
  firstName: string | null;
  lastName: string | null;
  emailVerified: boolean;
  // autres propriétés...
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<User>;
  register: (userData: RegisterData) => Promise<User>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Vérifier si l'utilisateur est déjà connecté
    async function checkAuth() {
      try {
        const response = await fetch('/api/user');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        }
      } catch (error) {
        console.error("Erreur lors de la vérification de l'authentification:", error);
      } finally {
        setLoading(false);
      }
    }
    
    checkAuth();
  }, []);
  
  const login = async (email: string, password: string): Promise<User> => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Échec de la connexion");
      }
      
      const userData = await response.json();
      setUser(userData);
      return userData;
    } finally {
      setLoading(false);
    }
  };
  
  const register = async (userData: RegisterData): Promise<User> => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Échec de l'inscription");
      }
      
      const newUser = await response.json();
      setUser(newUser);
      return newUser;
    } finally {
      setLoading(false);
    }
  };
  
  const logout = async (): Promise<void> => {
    try {
      await fetch('/api/auth/logout');
      setUser(null);
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
    }
  };
  
  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        register,
        logout,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth doit être utilisé à l'intérieur d'un AuthProvider");
  }
  return context;
}
```

### Page d'authentification

```tsx
// src/pages/auth-page.tsx
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../hooks/use-toast';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { RiGoogleFill, RiFacebookFill, RiAppleFill } from 'react-icons/ri';

// Schémas de validation
const loginSchema = z.object({
  email: z.string().email("Email invalide"),
  password: z.string().min(6, "Le mot de passe doit contenir au moins 6 caractères"),
});

const registerSchema = z.object({
  firstName: z.string().min(1, "Le prénom est requis"),
  lastName: z.string().min(1, "Le nom est requis"),
  email: z.string().email("Email invalide"),
  password: z.string().min(6, "Le mot de passe doit contenir au moins 6 caractères"),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: "Les mots de passe ne correspondent pas",
  path: ["confirmPassword"],
});

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<'login' | 'register'>('login');
  const { login, register: registerUser } = useAuth();
  const { toast } = useToast();
  
  // Formulaire de connexion
  const loginForm = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });
  
  // Formulaire d'inscription
  const registerForm = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });
  
  const handleLogin = async (values: z.infer<typeof loginSchema>) => {
    try {
      await login(values.email, values.password);
      toast({
        title: "Connexion réussie",
        description: "Bienvenue sur Waabo",
        variant: "success",
      });
    } catch (error) {
      toast({
        title: "Échec de la connexion",
        description: error instanceof Error ? error.message : "Une erreur est survenue",
        variant: "destructive",
      });
    }
  };
  
  const handleRegister = async (values: z.infer<typeof registerSchema>) => {
    try {
      await registerUser({
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        password: values.password,
      });
      toast({
        title: "Inscription réussie",
        description: "Votre compte a été créé avec succès",
        variant: "success",
      });
    } catch (error) {
      toast({
        title: "Échec de l'inscription",
        description: error instanceof Error ? error.message : "Une erreur est survenue",
        variant: "destructive",
      });
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <Logo />
          <h1 className="mt-6 text-2xl font-bold text-gray-900">
            {activeTab === 'login' ? 'Connectez-vous à votre compte' : 'Créez votre compte'}
          </h1>
        </div>
        
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <Tabs 
            defaultValue="login" 
            value={activeTab} 
            onValueChange={(value) => setActiveTab(value as 'login' | 'register')}
          >
            <TabsList className="grid grid-cols-2 mb-8">
              <TabsTrigger value="login">Connexion</TabsTrigger>
              <TabsTrigger value="register">Inscription</TabsTrigger>
            </TabsList>
            
            <TabsContent value="login">
              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(handleLogin)} className="space-y-6">
                  <FormField
                    control={loginForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mot de passe</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="••••••••" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button type="submit" className="w-full bg-[#004d25]">
                    Se connecter
                  </Button>
                </form>
              </Form>
            </TabsContent>
            
            <TabsContent value="register">
              <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(handleRegister)} className="space-y-6">
                  {/* Champs d'inscription... */}
                </form>
              </Form>
            </TabsContent>
          </Tabs>
          
          <div className="mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Ou continuer avec</span>
              </div>
            </div>
            
            <div className="mt-6 grid grid-cols-3 gap-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => window.location.href = '/api/auth/google'}
              >
                <RiGoogleFill className="h-5 w-5 text-red-500" />
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => window.location.href = '/api/auth/facebook'}
              >
                <RiFacebookFill className="h-5 w-5 text-blue-600" />
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => window.location.href = '/api/auth/apple'}
              >
                <RiAppleFill className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## Sécurité

### Hachage des mots de passe

Les mots de passe sont hachés à l'aide de bcrypt avant d'être stockés en base de données:

```typescript
export async function hashPassword(password: string) {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

export async function comparePasswords(supplied: string, stored: string | null) {
  if (!stored) return false;
  return bcrypt.compare(supplied, stored);
}
```

### Protection CSRF

Protection contre les attaques CSRF (Cross-Site Request Forgery):

```typescript
import csrf from 'csurf';

const csrfProtection = csrf({ cookie: true });

// Application du middleware sur les routes sensibles
app.post('/api/auth/login', csrfProtection, passport.authenticate('local'), ...);
app.post('/api/auth/register', csrfProtection, ...);
```

### Limitation de tentatives

Limitation du nombre de tentatives de connexion pour prévenir les attaques par force brute:

```typescript
import rateLimit from 'express-rate-limit';

const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 tentatives max
  message: "Trop de tentatives de connexion, veuillez réessayer plus tard",
});

app.post('/api/auth/login', loginLimiter, ...);
```

## Flux d'authentification

### Connexion locale

```
┌─────────────┐         ┌─────────────┐          ┌─────────────┐
│  Client     │         │   Server    │          │  Database   │
└──────┬──────┘         └──────┬──────┘          └──────┬──────┘
       │                        │                        │
       │  POST /api/auth/login  │                        │
       │───────────────────────>│                        │
       │                        │                        │
       │                        │  Query User by Email   │
       │                        │───────────────────────>│
       │                        │                        │
       │                        │  Return User           │
       │                        │<───────────────────────│
       │                        │                        │
       │                        │  Compare Password      │
       │                        │  with Bcrypt           │
       │                        │                        │
       │                        │  Create Session        │
       │                        │                        │
       │  Return User + Cookie  │                        │
       │<───────────────────────│                        │
       │                        │                        │
```

### Connexion OAuth (ex: Google)

```
┌─────────────┐         ┌─────────────┐          ┌─────────────┐        ┌─────────────┐
│  Client     │         │   Server    │          │  Database   │        │  Google     │
└──────┬──────┘         └──────┬──────┘          └──────┬──────┘        └──────┬──────┘
       │                        │                        │                       │
       │  GET /api/auth/google  │                        │                       │
       │───────────────────────>│                        │                       │
       │                        │                        │                       │
       │                        │                        │                       │
       │  Redirect to Google    │                        │                       │
       │<───────────────────────│                        │                       │
       │                        │                        │                       │
       │  Google Login Page     │                        │                       │
       │──────────────────────────────────────────────────────────────────────> │
       │                        │                        │                       │
       │  User Authenticates    │                        │                       │
       │──────────────────────────────────────────────────────────────────────> │
       │                        │                        │                       │
       │  Redirect with Code    │                        │                       │
       │<───────────────────────────────────────────────────────────────────────│
       │                        │                        │                       │
       │  GET /api/auth/google/callback?code=...         │                       │
       │───────────────────────>│                        │                       │
       │                        │                        │                       │
       │                        │  Exchange Code for Token                       │
       │                        │──────────────────────────────────────────────>│
       │                        │                        │                       │
       │                        │  Return Access Token   │                       │
       │                        │<──────────────────────────────────────────────│
       │                        │                        │                       │
       │                        │  Get User Profile      │                       │
       │                        │──────────────────────────────────────────────>│
       │                        │                        │                       │
       │                        │  Return User Profile   │                       │
       │                        │<──────────────────────────────────────────────│
       │                        │                        │                       │
       │                        │  Find/Create User      │                       │
       │                        │───────────────────────>│                       │
       │                        │                        │                       │
       │                        │  Return User           │                       │
       │                        │<───────────────────────│                       │
       │                        │                        │                       │
       │                        │  Create Session        │                       │
       │                        │                        │                       │
       │  Redirect to Home      │                        │                       │
       │<───────────────────────│                        │                       │
       │                        │                        │                       │
```

## Gestion des sessions

La gestion des sessions est configurée dans `server/index.ts`:

```typescript
import session from 'express-session';

// Configuration pour PostgreSQL
import pg from 'pg';
import connectPgSimple from 'connect-pg-simple';

const PgSession = connectPgSimple(session);
const pgPool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
});

app.use(
  session({
    store: new PgSession({
      pool: pgPool,
      tableName: 'session', // Nom de la table de sessions
      createTableIfMissing: true,
    }),
    secret: process.env.SESSION_SECRET!,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 jours
    },
  })
);

app.use(passport.initialize());
app.use(passport.session());
```

## Récupération et réinitialisation de mot de passe

```typescript
// Demande de réinitialisation de mot de passe
app.post('/api/auth/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;
    
    // Vérifier si l'utilisateur existe
    const users = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.email, email));
    
    if (users.length === 0) {
      // Ne pas révéler si l'email existe ou non pour des raisons de sécurité
      return res.status(200).json({ message: "Si cet email existe, un lien de réinitialisation a été envoyé" });
    }
    
    const user = users[0];
    
    // Générer un token de réinitialisation (valable 1 heure)
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 heure
    
    // Mettre à jour l'utilisateur avec le token
    await db
      .update(schema.users)
      .set({
        resetToken,
        resetTokenExpiry,
        updatedAt: new Date(),
      })
      .where(eq(schema.users.id, user.id));
    
    // Construire le lien de réinitialisation
    const resetLink = `https://waabo-app.com/reset-password?token=${resetToken}`;
    
    // Envoyer l'email de réinitialisation
    await sendPasswordResetEmail(email, resetToken, resetLink);
    
    return res.status(200).json({ message: "Un lien de réinitialisation a été envoyé à votre adresse email" });
  } catch (error) {
    console.error("Erreur lors de la demande de réinitialisation:", error);
    return res.status(500).json({ message: "Erreur serveur" });
  }
});

// Réinitialisation du mot de passe
app.post('/api/auth/reset-password', async (req, res) => {
  try {
    const { token, password } = req.body;
    
    // Vérifier si le token est valide
    const users = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.resetToken, token))
      .where(sql`${schema.users.resetTokenExpiry} > NOW()`);
    
    if (users.length === 0) {
      return res.status(400).json({ message: "Token invalide ou expiré" });
    }
    
    const user = users[0];
    
    // Hasher le nouveau mot de passe
    const hashedPassword = await hashPassword(password);
    
    // Mettre à jour l'utilisateur
    await db
      .update(schema.users)
      .set({
        password: hashedPassword,
        resetToken: null,
        resetTokenExpiry: null,
        updatedAt: new Date(),
      })
      .where(eq(schema.users.id, user.id));
    
    return res.status(200).json({ message: "Mot de passe réinitialisé avec succès" });
  } catch (error) {
    console.error("Erreur lors de la réinitialisation du mot de passe:", error);
    return res.status(500).json({ message: "Erreur serveur" });
  }
});
```

## Vérification d'email

```typescript
// Envoi d'email de vérification
app.post('/api/email/send-verification', async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ message: "Non authentifié" });
    }
    
    // Vérifier si l'utilisateur existe
    const users = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.id, userId));
    
    if (users.length === 0) {
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    }
    
    const user = users[0];
    
    if (user.emailVerified) {
      return res.status(400).json({ message: "Email déjà vérifié" });
    }
    
    // Générer un token de vérification
    const verificationToken = crypto.randomBytes(32).toString('hex');
    
    // Mettre à jour l'utilisateur avec le token
    await db
      .update(schema.users)
      .set({
        emailVerificationToken: verificationToken,
        updatedAt: new Date(),
      })
      .where(eq(schema.users.id, user.id));
    
    // Construire le lien de vérification
    const verificationLink = `https://waabo-app.com/api/email/verify?token=${verificationToken}`;
    
    // Envoyer l'email de vérification
    await sendEmailVerificationEmail(user.email, verificationToken, verificationLink);
    
    return res.status(200).json({ message: "Email de vérification envoyé" });
  } catch (error) {
    console.error("Erreur lors de l'envoi de l'email de vérification:", error);
    return res.status(500).json({ message: "Erreur serveur" });
  }
});

// Vérification d'email
app.get('/api/email/verify', async (req, res) => {
  try {
    const { token } = req.query;
    
    if (!token) {
      return res.status(400).json({ message: "Token manquant" });
    }
    
    // Vérifier si le token est valide
    const users = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.emailVerificationToken, token as string));
    
    if (users.length === 0) {
      return res.status(400).json({ message: "Token invalide" });
    }
    
    const user = users[0];
    
    // Mettre à jour l'utilisateur
    await db
      .update(schema.users)
      .set({
        emailVerified: true,
        emailVerificationToken: null,
        updatedAt: new Date(),
      })
      .where(eq(schema.users.id, user.id));
    
    // Rediriger vers la page de confirmation
    return res.redirect('/email-verified');
  } catch (error) {
    console.error("Erreur lors de la vérification de l'email:", error);
    return res.status(500).json({ message: "Erreur serveur" });
  }
});
```

## Tests et débogage

### Tests unitaires

```typescript
// __tests__/auth.test.ts
import { hashPassword, comparePasswords } from '../server/auth';

describe('Authentification', () => {
  test('Le mot de passe est correctement haché', async () => {
    const password = 'motdepasse123';
    const hashedPassword = await hashPassword(password);
    
    // Vérifier que le mot de passe est différent de l'original
    expect(hashedPassword).not.toBe(password);
    
    // Vérifier que le mot de passe peut être vérifié
    const isMatch = await comparePasswords(password, hashedPassword);
    expect(isMatch).toBe(true);
  });
  
  test('La comparaison de mot de passe échoue avec un mauvais mot de passe', async () => {
    const password = 'motdepasse123';
    const wrongPassword = 'mauvaispassword';
    const hashedPassword = await hashPassword(password);
    
    const isMatch = await comparePasswords(wrongPassword, hashedPassword);
    expect(isMatch).toBe(false);
  });
});
```

### Débogage courant

1. **Problèmes de session**:
   - Vérifier que les cookies sont correctement configurés
   - S'assurer que la table de sessions existe en base de données
   - Vérifier que le secret de session est correctement défini

2. **Problèmes OAuth**:
   - Vérifier les URL de callback dans les consoles de développeurs Google/Facebook/Apple
   - S'assurer que les clés API et secrets sont correctement configurés
   - Examiner les logs pour les erreurs spécifiques

3. **Problèmes de hachage**:
   - S'assurer que bcrypt est correctement installé
   - Vérifier que les mots de passe hachés sont correctement stockés en base de données

## Bonnes pratiques

1. **Sécurité**:
   - Ne jamais stocker les mots de passe en clair
   - Utiliser HTTPS en production
   - Implémenter la limitation de taux pour prévenir les attaques par force brute
   - Utiliser des tokens à durée limitée pour les réinitialisations de mot de passe

2. **Expérience utilisateur**:
   - Fournir des messages d'erreur clairs mais sans révéler d'informations sensibles
   - Mettre en place une authentification à plusieurs facteurs pour les comptes sensibles
   - Permettre la connexion via plusieurs méthodes (email/mot de passe, OAuth, etc.)

3. **Maintenance**:
   - Surveiller les tentatives de connexion échouées
   - Mettre régulièrement à jour les dépendances de sécurité
   - Maintenir des logs d'authentification pour le débogage et l'audit

---

## Conclusion

Le système d'authentification multi-méthodes de Waabo offre une expérience utilisateur flexible et sécurisée. L'intégration de multiples fournisseurs d'identité (Google, Facebook, Apple) aux côtés de l'authentification traditionnelle par email/mot de passe permet d'accommoder les préférences de tous les utilisateurs tout en maintenant un haut niveau de sécurité.

Cette architecture modulaire permet également d'ajouter facilement de nouvelles méthodes d'authentification à l'avenir, garantissant l'évolutivité du système à long terme.

---

**Version du document**: 1.0  
**Date de dernière mise à jour**: 8 mai 2025  
**Auteur**: Équipe de développement Waabo