/**
 * Script pour préparer l'application Waabo pour le packaging iOS et Android
 */
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  red: '\x1b[31m',
  magenta: '\x1b[35m'
};

// Plateforme actuelle
const isMac = process.platform === 'darwin';

/**
 * Exécute une commande shell et retourne une promesse
 */
function execPromise(command) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.blue}Exécution de:${colors.reset} ${command}`);
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`${colors.red}Erreur:${colors.reset} ${error.message}`);
        if (stderr) console.log(`${colors.yellow}Stderr:${colors.reset} ${stderr}`);
        reject(error);
        return;
      }
      if (stderr) {
        console.log(`${colors.yellow}Message:${colors.reset} ${stderr}`);
      }
      if (stdout.trim()) {
        console.log(`${colors.green}Sortie:${colors.reset} ${stdout}`);
      }
      resolve(stdout);
    });
  });
}

/**
 * Fonction pour poser une question à l'utilisateur
 */
function askQuestion(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

/**
 * Affiche un titre de section
 */
function printSectionTitle(title) {
  console.log(`\n${colors.cyan}=== ${title} ===${colors.reset}\n`);
}

/**
 * Affiche un sous-titre
 */
function printSubTitle(title) {
  console.log(`\n${colors.magenta}>> ${title}${colors.reset}`);
}

/**
 * Affiche un message de succès
 */
function printSuccess(message) {
  console.log(`${colors.green}✓ ${message}${colors.reset}`);
}

/**
 * Affiche un avertissement
 */
function printWarning(message) {
  console.log(`${colors.yellow}⚠ ${message}${colors.reset}`);
}

/**
 * Vérifie et installe les dépendances Capacitor
 */
async function checkAndInstallCapacitorDependencies() {
  printSubTitle("Vérification des dépendances Capacitor");
  
  const dependencies = [
    '@capacitor/core',
    '@capacitor/cli',
    '@capacitor/splash-screen'
  ];
  
  // Vérifier si on a déjà les dépendances iOS et Android
  try {
    require.resolve('@capacitor/ios');
    printSuccess("Le module @capacitor/ios est déjà installé");
  } catch (e) {
    console.log("Installation du module @capacitor/ios...");
    await execPromise('npm install @capacitor/ios');
  }
  
  try {
    require.resolve('@capacitor/android');
    printSuccess("Le module @capacitor/android est déjà installé");
  } catch (e) {
    console.log("Installation du module @capacitor/android...");
    await execPromise('npm install @capacitor/android');
  }
  
  // Vérifier les autres dépendances
  for (const dep of dependencies) {
    try {
      require.resolve(dep);
      printSuccess(`Le module ${dep} est déjà installé`);
    } catch (e) {
      console.log(`Installation du module ${dep}...`);
      await execPromise(`npm install ${dep}`);
    }
  }
}

/**
 * Met à jour la configuration Capacitor
 */
function updateCapacitorConfig() {
  printSubTitle("Mise à jour de la configuration Capacitor");
  
  const capConfigPath = path.join(__dirname, 'capacitor.config.ts');
  let capConfig = fs.readFileSync(capConfigPath, 'utf8');
  let configUpdated = false;
  
  // S'assurer que le webDir pointe vers le bon dossier
  if (!capConfig.includes('webDir: \'dist\'')) {
    console.log('Mise à jour du webDir dans capacitor.config.ts...');
    capConfig = capConfig.replace(/webDir: ['"][^'"]+['"]/, 'webDir: \'dist\'');
    configUpdated = true;
  } else {
    printSuccess('La configuration webDir est déjà correcte.');
  }
  
  // Vérifier si la configuration Android existe
  if (!capConfig.includes('android: {')) {
    console.log('Ajout de la configuration Android dans capacitor.config.ts...');
    // Trouver la position où insérer la configuration Android (après la configuration iOS)
    const iosConfigEndIndex = capConfig.indexOf('},', capConfig.indexOf('ios: {')) + 2;
    
    const androidConfig = `
  android: {
    backgroundColor: "#ffffff",
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true
  },`;
    
    capConfig = capConfig.slice(0, iosConfigEndIndex) + androidConfig + capConfig.slice(iosConfigEndIndex);
    configUpdated = true;
  } else {
    printSuccess('La configuration Android existe déjà.');
  }
  
  // Si des modifications ont été apportées, écrire le fichier
  if (configUpdated) {
    fs.writeFileSync(capConfigPath, capConfig);
    printSuccess('Configuration Capacitor mise à jour.');
  }
  
  return true;
}

/**
 * Construit l'application web
 */
async function buildWebApp() {
  printSubTitle("Construction de l'application web");
  
  try {
    await execPromise('npm run build');
    printSuccess("Application web construite avec succès");
    return true;
  } catch (error) {
    console.error(`${colors.red}Erreur lors de la construction de l'application web:${colors.reset} ${error.message}`);
    return false;
  }
}

/**
 * Ajoute et synchronise la plateforme iOS
 */
async function setupIOS() {
  printSectionTitle("Configuration pour iOS");
  
  if (!isMac) {
    printWarning("Vous n'êtes pas sur macOS. La compilation pour iOS nécessite un Mac.");
    printWarning("Ce script préparera les fichiers, mais vous devrez les transférer sur un Mac pour finaliser.");
    
    const proceed = await askQuestion("Voulez-vous continuer quand même avec la préparation iOS? (y/n): ");
    if (proceed.toLowerCase() !== 'y') {
      return false;
    }
  }
  
  try {
    // Vérifier si la plateforme iOS existe déjà
    if (!fs.existsSync(path.join(__dirname, 'ios'))) {
      console.log('Ajout de la plateforme iOS...');
      await execPromise('npx cap add ios');
    } else {
      printSuccess('La plateforme iOS existe déjà.');
    }
    
    // Synchroniser les changements
    console.log('Synchronisation des changements avec iOS...');
    await execPromise('npx cap sync ios');
    
    printSuccess("Plateforme iOS configurée avec succès!");
    
    if (isMac) {
      const openNow = await askQuestion("\nVoulez-vous ouvrir le projet dans Xcode maintenant? (y/n): ");
      if (openNow.toLowerCase() === 'y') {
        await execPromise('npx cap open ios');
      }
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Erreur lors de la configuration iOS:${colors.reset} ${error.message}`);
    return false;
  }
}

/**
 * Ajoute et synchronise la plateforme Android
 */
async function setupAndroid() {
  printSectionTitle("Configuration pour Android");
  
  try {
    // Vérifier si la plateforme Android existe déjà
    if (!fs.existsSync(path.join(__dirname, 'android'))) {
      console.log('Ajout de la plateforme Android...');
      await execPromise('npx cap add android');
    } else {
      printSuccess('La plateforme Android existe déjà.');
    }
    
    // Synchroniser les changements
    console.log('Synchronisation des changements avec Android...');
    await execPromise('npx cap sync android');
    
    printSuccess("Plateforme Android configurée avec succès!");
    
    const openNow = await askQuestion("\nVoulez-vous ouvrir le projet dans Android Studio maintenant? (y/n): ");
    if (openNow.toLowerCase() === 'y') {
      await execPromise('npx cap open android');
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Erreur lors de la configuration Android:${colors.reset} ${error.message}`);
    return false;
  }
}

/**
 * Processus principal
 */
async function main() {
  try {
    console.log(`${colors.green}=======================================================${colors.reset}`);
    console.log(`${colors.green}= Préparation de l'application Waabo pour mobile      =${colors.reset}`);
    console.log(`${colors.green}=======================================================${colors.reset}\n`);
    
    // Étape 1: Vérifier les dépendances
    printSectionTitle("Vérification de l'environnement");
    
    await checkAndInstallCapacitorDependencies();
    
    // Étape 2: Mettre à jour la configuration Capacitor
    updateCapacitorConfig();
    
    // Étape 3: Construire l'application
    if (!await buildWebApp()) {
      const proceed = await askQuestion("La construction a échoué. Voulez-vous continuer quand même? (y/n): ");
      if (proceed.toLowerCase() !== 'y') {
        return;
      }
    }
    
    // Étape 4: Demander les plateformes à configurer
    const platformsInput = await askQuestion("\nQuelles plateformes souhaitez-vous configurer? (ios/android/both): ");
    const platforms = platformsInput.toLowerCase();
    
    let iosSetup = false;
    let androidSetup = false;
    
    if (platforms === 'ios' || platforms === 'both') {
      iosSetup = await setupIOS();
    }
    
    if (platforms === 'android' || platforms === 'both') {
      androidSetup = await setupAndroid();
    }
    
    // Résumé final
    printSectionTitle("Résumé de la préparation");
    
    if (iosSetup) {
      printSuccess("iOS: Configuration terminée avec succès");
      console.log("Pour plus d'informations sur la publication sur l'App Store, consultez le fichier IOS_APP_STORE_GUIDE.md");
    } else if (platforms === 'ios' || platforms === 'both') {
      printWarning("iOS: La configuration a rencontré des problèmes");
    }
    
    if (androidSetup) {
      printSuccess("Android: Configuration terminée avec succès");
      console.log("Pour plus d'informations sur la publication sur le Play Store, consultez le fichier ANDROID_APP_STORE_GUIDE.md");
    } else if (platforms === 'android' || platforms === 'both') {
      printWarning("Android: La configuration a rencontré des problèmes");
    }
    
    console.log(`\n${colors.green}Processus de préparation terminé!${colors.reset}`);
    
  } catch (error) {
    console.error(`${colors.red}Une erreur est survenue:${colors.reset} ${error.message}`);
  } finally {
    rl.close();
  }
}

// Exécuter le script
main();