# Guide de publication sur l'App Store iOS

Ce guide détaille les étapes nécessaires pour préparer et soumettre l'application Waabo à l'App Store iOS.

## Prérequis

1. **Compte Apple Developer**
   - Un compte Apple Developer actif (99$ par an)
   - Accès au Apple Developer Portal et App Store Connect

2. **Environnement de développement**
   - Un Mac avec macOS (version récente)
   - Xcode (dernière version stable)
   - Capacitor CLI installé (`npm install -g @capacitor/cli`)

3. **Certificats et identifiants**
   - Certificat de distribution iOS
   - Profil de provisionnement pour l'App Store

## Étape 1: Préparation de l'application

1. **Construire l'application**
   ```bash
   # Depuis le répertoire de votre projet
   npm run build
   ```

2. **Ajouter la plateforme iOS (si pas déjà fait)**
   ```bash
   npx cap add ios
   ```

3. **Synchroniser les fichiers**
   ```bash
   npx cap sync ios
   ```

## Étape 2: Configuration dans Xcode

1. **Ouvrir le projet dans Xcode**
   ```bash
   npx cap open ios
   ```

2. **Configurer les informations d'application**
   - Sélectionnez le projet Waabo dans le navigateur de projet
   - Sous l'onglet "General", vérifiez les informations suivantes:
     - Bundle Identifier: `com.waabo.app` (doit être unique dans l'App Store)
     - Version: La version de votre application (ex: 1.0.0)
     - Build: Le numéro de build (ex: 1)
     - Deployment Target: iOS 14.0 ou plus récent
     - Device Orientation: Les orientations supportées

3. **Configurer les signing certificates**
   - Sous l'onglet "Signing & Capabilities":
     - Cochez "Automatically manage signing"
     - Sélectionnez votre équipe de développement
     - Vérifiez que le profil de provisionnement est correctement configuré

4. **Ajouter les assets nécessaires**
   - Icônes d'application: Sous "Assets.xcassets", configurez les icônes de l'application
   - Écran de lancement: Configurez "LaunchScreen.storyboard"

## Étape 3: Préparation des métadonnées de l'App Store

1. **Capturez des screenshots**
   - iPhone 13 Pro Max (6.7")
   - iPhone 8 Plus (5.5")
   - iPad Pro (12.9")
   - iPad Pro (11")

2. **Préparez les métadonnées**
   - Titre de l'application: "Waabo - Livraison Internationale"
   - Sous-titre: "Suivi des Colis Chine-Burkina"
   - Description: Une description détaillée de votre application (4000 caractères max)
   - Mots-clés: Livraison, Colis, International, Chine, Burkina Faso, etc.
   - URL du site web de support
   - URL de marketing

## Étape 4: Création du build pour l'App Store

1. **Sélectionnez le schéma et la destination**
   - Scheme: App
   - Destination: Any iOS Device (arm64)

2. **Archive l'application**
   - Menu: Product > Archive

3. **Validation et envoi**
   - Dans la fenêtre Organizer (Window > Organizer), sélectionnez l'archive créée
   - Cliquez sur "Validate App" pour vérifier qu'il n'y a pas d'erreurs
   - Une fois validé, cliquez sur "Distribute App" et suivez les instructions pour soumettre à l'App Store

## Étape 5: Soumission dans App Store Connect

1. **Connectez-vous à App Store Connect**
   - [https://appstoreconnect.apple.com](https://appstoreconnect.apple.com)

2. **Créez une nouvelle application**
   - Apps > My Apps > "+" > New App
   - Remplissez les informations demandées (Platform, Name, Bundle ID, etc.)

3. **Configurez la page de l'application**
   - Ajoutez les screenshots
   - Remplissez toutes les métadonnées préparées à l'étape 3
   - Sélectionnez une catégorie appropriée (ex: Business, Utilities)
   - Définissez le prix (ou gratuit)
   - Définissez la disponibilité (pays)

4. **Conformité à la confidentialité**
   - Répondez aux questions concernant la confidentialité des données
   - Ajoutez un lien vers votre politique de confidentialité

5. **Soumettez pour révision**
   - Une fois que toutes les informations sont remplies et que le build est téléchargé
   - Cliquez sur "Submit for Review"

## Étape 6: Processus de révision et suivi

1. **Attendez l'approbation**
   - Le processus de révision peut prendre de quelques heures à quelques jours
   - Vous recevrez des notifications par email sur l'état de votre soumission

2. **Résolvez les problèmes potentiels**
   - Si votre application est rejetée, lisez attentivement les raisons du rejet
   - Apportez les corrections nécessaires et soumettez à nouveau

3. **Publication**
   - Une fois approuvée, vous pouvez choisir de publier automatiquement
   - Ou vous pouvez la publier manuellement à une date spécifique

## Conseils supplémentaires

1. **Optimisation pour l'App Store (ASO)**
   - Utilisez des mots-clés pertinents dans le titre, le sous-titre et la description
   - Créez des visuels attrayants et des screenshots qui mettent en valeur les fonctionnalités

2. **Tests TestFlight**
   - Avant de soumettre à l'App Store, envisagez de tester avec TestFlight
   - Cela vous permet d'identifier les problèmes potentiels avec des utilisateurs réels

3. **Mises à jour régulières**
   - Planifiez des mises à jour régulières pour corriger les bugs et ajouter de nouvelles fonctionnalités
   - Les mises à jour fréquentes améliorent le classement de votre application dans l'App Store

## Résolution des problèmes courants

1. **Rejet pour UI non conforme**
   - Assurez-vous que l'application respecte les Human Interface Guidelines d'Apple
   - Vérifiez que tous les boutons et zones interactives sont suffisamment grands

2. **Problèmes de confidentialité**
   - Assurez-vous que votre politique de confidentialité est complète et accessible
   - Déclarez correctement toutes les données utilisées par l'application

3. **Problèmes de crash ou de performance**
   - Testez minutieusement votre application sur plusieurs appareils avant de soumettre
   - Utilisez Xcode Instruments pour identifier les problèmes de performance