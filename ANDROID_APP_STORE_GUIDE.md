# Guide de publication sur Google Play Store

Ce guide détaille les étapes nécessaires pour préparer et soumettre l'application Waabo au Google Play Store.

## Prérequis

1. **Compte Google Play Developer**
   - Un compte Google Play Developer actif (frais d'inscription uniques de 25$)
   - Accès à la Google Play Console

2. **Environnement de développement**
   - Android Studio (dernière version stable)
   - JDK 11 ou plus récent
   - Capacitor CLI installé (`npm install -g @capacitor/cli`)

3. **Certificats et clés**
   - Générer une clé de signature pour l'application (keystore)
   - Conserver cette clé en sécurité pour les futures mises à jour

## Étape 1: Préparation de l'application

1. **Construire l'application**
   ```bash
   # Depuis le répertoire de votre projet
   npm run build
   ```

2. **Ajouter la plateforme Android (si pas déjà fait)**
   ```bash
   npx cap add android
   ```

3. **Synchroniser les fichiers**
   ```bash
   npx cap sync android
   ```

4. **Ouvrir le projet dans Android Studio**
   ```bash
   npx cap open android
   ```

## Étape 2: Configuration dans Android Studio

1. **Vérifier le fichier `build.gradle` (niveau application)**
   - Vérifier les informations suivantes:
     - `applicationId`: "com.waabo.app" (doit être unique sur le Play Store)
     - `versionCode`: Numéro entier qui augmente à chaque mise à jour
     - `versionName`: Version sémantique de votre application (ex: "1.0.0")
     - `minSdkVersion`: 22 ou plus récent (Android 5.1+)
     - `targetSdkVersion`: La version cible recommandée

2. **Configurer les ressources**
   - Vérifier que les icônes sont correctement configurées dans `res/mipmap-*`
   - S'assurer que l'écran de démarrage est configuré correctement

3. **Renseigner les informations importantes**
   - Dans `AndroidManifest.xml`, vérifier:
     - Les permissions utilisées par l'application
     - L'orientation de l'écran
     - La configuration pour deep linking si nécessaire

## Étape 3: Signer l'application

1. **Générer une clé de signature (si pas déjà fait)**
   - Menu: Build > Generate Signed Bundle/APK
   - Choisir "Android App Bundle" (recommandé par Google)
   - Créer une nouvelle clé de signature (keystore)
   - Remplir les informations (nom, mot de passe, validité, etc.)
   - **IMPORTANT**: Sauvegardez ce fichier keystore et son mot de passe en lieu sûr!

2. **Configurer la signature**
   - Dans la fenêtre "Generate Signed Bundle", sélectionner la clé de signature
   - Choisir la variante de build "release"
   - Cocher "V1 (Jar Signature)" et "V2 (Full APK Signature)"

## Étape 4: Créer le bundle ou APK signé

1. **Générer le bundle Android (AAB)**
   - Menu: Build > Generate Signed Bundle/APK
   - Sélectionner "Android App Bundle"
   - Choisir la variante "release"
   - Définir la destination du fichier AAB
   - Cliquer sur "Finish"

2. **Vérifier le bundle**
   - Le fichier `.aab` sera généré dans le répertoire spécifié
   - Vous pouvez tester ce bundle avec le Play App Signing

## Étape 5: Préparer les métadonnées pour Google Play

1. **Captures d'écran requises**
   - Téléphone: Au moins 2 captures d'écran (max 8)
     - Format: JPEG ou PNG, 16:9 ou 9:16
   - Tablette: Facultatif, mais recommandé
     - Format: JPEG ou PNG, 16:10

2. **Icône de haute résolution**
   - 512x512 pixels
   - Format: PNG 32-bits (avec transparence)

3. **Image promotionnelle**
   - 1024x500 pixels
   - Format: JPEG ou PNG 24-bits (sans transparence)

4. **Vidéo promotionnelle (optionnelle)**
   - Lien YouTube vers une vidéo de présentation

5. **Textes**
   - Titre de l'application (50 caractères max)
   - Description courte (80 caractères max)
   - Description complète (4000 caractères max)
   - Mots-clés et catégorie

## Étape 6: Publier sur Google Play Console

1. **Se connecter à Google Play Console**
   - [https://play.google.com/console](https://play.google.com/console)

2. **Créer une nouvelle application**
   - Cliquer sur "Créer une application"
   - Remplir les informations demandées
   - Choisir la langue par défaut et le titre de l'application

3. **Remplir les informations de la fiche Play Store**
   - Catégorie et type de contenu
   - Coordonnées
   - Confidentialité et conformité
   - Ajouter les captures d'écran, icônes et média promotionnel

4. **Configurer les prix et la distribution**
   - Définir si l'application est gratuite ou payante
   - Sélectionner les pays où l'application sera disponible

5. **Télécharger le bundle Android**
   - Aller dans "Production" > "Créer une nouvelle version"
   - Téléverser le fichier AAB généré précédemment
   - Ajouter des notes de version
   - Enregistrer et réviser

6. **Lancer la version**
   - Vérifier toutes les sections
   - S'assurer que tous les critères sont respectés
   - Cliquer sur "Lancer la version en production"

## Étape 7: Processus de révision et suivi

1. **Attendez l'approbation**
   - Le processus de révision peut prendre de quelques heures à plusieurs jours
   - Vous recevrez un email lorsque votre application sera approuvée ou rejetée

2. **Résolvez les problèmes potentiels**
   - Si votre application est rejetée, analysez les raisons
   - Corrigez les problèmes et soumettez à nouveau

3. **Surveillez les statistiques**
   - Une fois votre application publiée, utilisez les outils d'analyse de Google Play Console
   - Surveillez les installations, désinstallations, notes et commentaires

## Conseils supplémentaires

1. **Tests internes et ouverts**
   - Utilisez les options de test interne ou ouvert avant la publication complète
   - Cela permet de tester l'application avec un groupe limité d'utilisateurs

2. **Optimisation du référencement (ASO)**
   - Utilisez des mots-clés pertinents dans le titre et la description
   - Choisissez la bonne catégorie pour votre application

3. **Mises à jour régulières**
   - Planifiez des mises à jour régulières pour corriger les bugs et ajouter des fonctionnalités
   - Les mises à jour régulières améliorent le classement dans le Play Store

4. **Répondez aux commentaires des utilisateurs**
   - Prenez le temps de répondre aux avis des utilisateurs
   - Cela améliore l'engagement et la fidélisation

## Résolution des problèmes courants

1. **Rejet pour violation des règles**
   - Lisez attentivement les règles du Google Play Store
   - Vérifiez les permissions demandées par votre application
   - Assurez-vous que votre politique de confidentialité est complète

2. **Problèmes de performance**
   - Testez votre application sur différents appareils avant de soumettre
   - Utilisez Android Profiler pour identifier les problèmes de mémoire ou de batterie

3. **Problèmes d'interface utilisateur**
   - Suivez les directives de conception Material Design
   - Assurez-vous que votre application est utilisable sur différentes tailles d'écran