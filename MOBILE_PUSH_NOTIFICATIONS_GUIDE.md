# Guide de configuration des notifications push pour l'application mobile Waabo

Ce guide explique comment configurer et utiliser les notifications push natives dans les applications mobiles iOS et Android pour Waabo.

## Prérequis

Pour que les notifications push fonctionnent correctement, vous aurez besoin de :

1. Un projet Firebase avec Firebase Cloud Messaging (FCM) activé
2. Des certificats Apple Push Notification service (APNs) pour iOS
3. La clé serveur Firebase Cloud Messaging

## Configuration Firebase

1. **Créer un projet Firebase** (si ce n'est pas déjà fait) :
   - Rendez-vous sur [Firebase Console](https://console.firebase.google.com/)
   - Créez un nouveau projet ou utilisez un projet existant

2. **Activer Firebase Cloud Messaging** :
   - Dans la console Firebase, allez dans "Cloud Messaging"
   - Configurez les API nécessaires

3. **Récupérer la clé serveur FCM** :
   - Dans la console Firebase, allez dans "Paramètres du projet" > "Cloud Messaging"
   - Copiez la "Clé serveur" (Server key)
   - Ajoutez cette clé comme variable d'environnement `FIREBASE_SERVER_KEY` dans votre application Replit

## Configuration pour Android

1. **Ajouter l'application Android à Firebase** :
   - Dans la console Firebase, cliquez sur "Ajouter une application" et sélectionnez Android
   - Entrez `com.waabo.app` comme ID de package
   - Téléchargez le fichier `google-services.json`
   - Placez ce fichier dans le dossier `android/app/` de votre projet Capacitor

2. **Configurer le projet Android** :
   - Assurez-vous que le plugin Firebase est correctement configuré dans `capacitor.config.ts`
   ```typescript
   plugins: {
     // ...
     FirebaseAuthentication: {
       skipNativeAuth: false,
       providers: ["google.com", "apple.com"]
     },
     PushNotifications: {
       presentationOptions: ["badge", "sound", "alert"]
     }
   }
   ```

## Configuration pour iOS

1. **Ajouter l'application iOS à Firebase** :
   - Dans la console Firebase, cliquez sur "Ajouter une application" et sélectionnez iOS
   - Entrez `com.waabo.app` comme ID de bundle
   - Téléchargez le fichier `GoogleService-Info.plist`
   - Placez ce fichier dans le dossier `ios/App/App/` de votre projet Capacitor

2. **Configurer les certificats APNs** :
   - Créez un certificat APNs dans le [Apple Developer Portal](https://developer.apple.com/)
   - Téléchargez et convertissez le certificat au format requis par Firebase
   - Téléversez le certificat dans la section "Cloud Messaging" de votre projet Firebase

3. **Configurer le projet iOS** :
   - Ouvrez le projet dans Xcode
   - Activez les capacités de notifications push
   - Assurez-vous que l'authentification en arrière-plan est activée

## Utilisation dans l'application

L'infrastructure est déjà en place dans l'application. Voici comment elle fonctionne :

### Côté client

1. **Initialisation** : Le composant `PushNotificationManager` initialise automatiquement les notifications push au démarrage de l'application si l'utilisateur est connecté.

2. **Enregistrement** : Lors de la première utilisation, l'application demande l'autorisation à l'utilisateur et envoie le token d'appareil au serveur.

3. **Réception** : Les notifications sont gérées de deux façons :
   - En premier plan : Affichées via une notification toast dans l'application
   - En arrière-plan : Affichées comme notifications natives du système

### Côté serveur

1. **Stockage des tokens** : Les tokens d'appareil sont stockés dans la table `push_tokens` de la base de données.

2. **Envoi de notifications** : Vous pouvez envoyer des notifications via les API suivantes :
   - `POST /api/push-notifications/user/:userId` - Envoyer à un utilisateur spécifique
   - `POST /api/push-notifications/all` - Envoyer à tous les utilisateurs

Exemple de payload pour envoyer une notification :
```json
{
  "title": "Titre de la notification",
  "body": "Corps de la notification",
  "data": {
    "type": "order",
    "orderId": "123456"
  }
}
```

## Tests et débogage

Pour tester les notifications push :

1. **Test côté serveur** : Utilisez un outil comme Postman pour appeler les API d'envoi de notifications.

2. **Test sur appareil** : Les notifications push ne fonctionnent pas dans les émulateurs/simulateurs. Testez sur des appareils physiques.

3. **Débogage** : Consultez les logs de Firebase pour voir si les notifications sont envoyées correctement.

## Personnalisation

Vous pouvez personnaliser les notifications en modifiant :

1. **Apparence** : Modifiez les icônes de notification dans les dossiers `android/app/src/main/res/drawable` pour Android et définissez les options de présentation dans `capacitor.config.ts`.

2. **Sons** : Ajoutez des sons personnalisés dans les ressources de l'application.

3. **Actions** : Modifiez la fonction `onNotificationActionPerformed` dans `PushNotificationManager.tsx` pour gérer différentes actions basées sur les données de la notification.

## Ressources utiles

- [Documentation Capacitor Push Notifications](https://capacitorjs.com/docs/apis/push-notifications)
- [Documentation Firebase Cloud Messaging](https://firebase.google.com/docs/cloud-messaging)
- [Guide Apple Push Notification service](https://developer.apple.com/documentation/usernotifications)