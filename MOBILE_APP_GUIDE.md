# Guide de conversion de Waabo en application mobile

Ce guide explique comment convertir l'application web Waabo en applications mobiles pour iOS et Android à l'aide de Capacitor.

## Prérequis

1. **Application web fonctionnelle**
   - L'application web doit être opérationnelle et responsive
   - Les fonctionnalités critiques doivent fonctionner correctement

2. **Environnement de développement**
   - Node.js et npm installés
   - Pour iOS: macOS avec Xcode installé
   - Pour Android: Android Studio installé

3. **Dépendances Capacitor**
   - @capacitor/core, @capacitor/cli
   - @capacitor/ios (pour iOS)
   - @capacitor/android (pour Android)
   - @capacitor/splash-screen

## Vue d'ensemble du processus

La conversion de Waabo en application mobile suit ces étapes générales:

1. Installation des dépendances Capacitor
2. Configuration du projet Capacitor
3. Construction de l'application web
4. Ajout des plateformes iOS et/ou Android
5. Personnalisation des plateformes spécifiques
6. Test sur des appareils ou émulateurs
7. Préparation pour la publication sur les stores

## Utilisation des scripts

Nous avons préparé plusieurs scripts pour faciliter le processus:

### 1. Script combiné (iOS et Android)

```bash
node prepare-mobile-app.js
```

Ce script interactif:
- Vérifie et installe les dépendances nécessaires
- Met à jour la configuration Capacitor si nécessaire
- Construit l'application web
- Vous permet de choisir les plateformes à configurer (iOS, Android ou les deux)
- Configure et synchronise les plateformes sélectionnées
- Vous propose d'ouvrir les projets natifs dans leurs IDE respectifs

### 2. Script pour iOS uniquement

```bash
node package-for-ios.js
```

Ce script se concentre uniquement sur la préparation pour iOS:
- Vérifie si vous êtes sur macOS (requis pour iOS)
- Construit l'application web
- Configure et synchronise la plateforme iOS
- Propose d'ouvrir le projet dans Xcode

### 3. Script pour Android uniquement

```bash
node package-for-android.js
```

Ce script se concentre uniquement sur la préparation pour Android:
- Construit l'application web
- Configure et synchronise la plateforme Android
- Propose d'ouvrir le projet dans Android Studio

## Processus détaillé

### Étape 1: Préparation initiale

1. Assurez-vous que toutes les dépendances sont installées:
   ```bash
   npm install @capacitor/core @capacitor/cli @capacitor/ios @capacitor/android @capacitor/splash-screen
   ```

2. Vérifiez que la configuration Capacitor est correcte:
   - Le fichier `capacitor.config.ts` doit pointer vers le bon répertoire de build (`webDir: 'dist'`)
   - Les configurations iOS et Android doivent être présentes

### Étape 2: Construction et synchronisation

1. Construisez l'application web:
   ```bash
   npm run build
   ```

2. Ajoutez les plateformes souhaitées (si ce n'est pas déjà fait):
   ```bash
   npx cap add ios
   npx cap add android
   ```

3. Synchronisez les changements:
   ```bash
   npx cap sync
   ```

### Étape 3: Personnalisation des plateformes

#### Pour iOS

1. Ouvrez le projet dans Xcode:
   ```bash
   npx cap open ios
   ```

2. Personnalisez:
   - Icônes d'application
   - Écran de démarrage
   - Configurations spécifiques à iOS

3. Consultez le fichier `IOS_APP_STORE_GUIDE.md` pour les étapes détaillées de publication sur l'App Store.

#### Pour Android

1. Ouvrez le projet dans Android Studio:
   ```bash
   npx cap open android
   ```

2. Personnalisez:
   - Icônes d'application
   - Écran de démarrage
   - Configurations spécifiques à Android

3. Consultez le fichier `ANDROID_APP_STORE_GUIDE.md` pour les étapes détaillées de publication sur le Google Play Store.

## Mise à jour de l'application

Lors de mises à jour futures de l'application:

1. Effectuez vos modifications dans le code web
2. Construisez l'application web: `npm run build`
3. Synchronisez avec Capacitor: `npx cap sync`
4. Pour des modifications importantes aux ressources natives: `npx cap copy`
5. Ouvrez les projets natifs et mettez à jour la version de l'application
6. Effectuez les tests nécessaires
7. Publiez les mises à jour sur les stores

## Dépannage

### Problèmes iOS courants

1. **Erreurs de build Xcode**
   - Vérifiez les paramètres de signature et de provisioning
   - Assurez-vous que le bundle ID est unique et correspond à votre profil de provisionnement

2. **Problèmes d'écran de démarrage**
   - Vérifiez la configuration dans `capacitor.config.ts`
   - Assurez-vous que les images sont aux bonnes dimensions

### Problèmes Android courants

1. **Erreurs de gradle**
   - Vérifiez la version de gradle
   - Synchronisez le projet dans Android Studio

2. **Problèmes de compatibilité API**
   - Vérifiez les versions minimales et cibles d'API dans `build.gradle`

## Ressources additionnelles

1. [Documentation officielle de Capacitor](https://capacitorjs.com/docs)
2. [Guide d'iOS App Store](./IOS_APP_STORE_GUIDE.md)
3. [Guide d'Android Play Store](./ANDROID_APP_STORE_GUIDE.md)
4. [Guide PWA pour iOS](./PWA_IOS_README.md)