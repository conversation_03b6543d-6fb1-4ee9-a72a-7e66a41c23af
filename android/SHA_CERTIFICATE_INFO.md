# SHA Certificate Information - Waabo App

## 🔐 Keystore Details
- **Keystore File**: `key.jks`
- **Alias**: `waabo-key`
- **Password**: `NewPassWorD123`
- **Algorithm**: SHA256withRSA
- **Key Size**: 2048-bit RSA
- **Validity**: Until Nov 11, 2052 (27+ years)

## 📋 Certificate Fingerprints

### SHA1 (Legacy)
```
FE:8F:51:30:66:90:40:1B:E2:36:62:6D:12:1D:CE:6A:1C:AA:F8:40
```

### SHA256 (Recommended)
```
DA:55:02:CF:42:B7:1E:B4:46:E7:AF:41:C4:B6:7E:D9:94:EF:AF:FA:75:E5:41:72:B8:11:F1:4D:A2:D7:92:68
```

## 🔧 Usage

### Firebase Configuration
1. Go to Firebase Console → Project Settings → General
2. Add Android app or update existing app
3. Add SHA certificate fingerprints:
   - **SHA1**: `FE:8F:51:30:66:90:40:1B:E2:36:62:6D:12:1D:CE:6A:1C:AA:F8:40`
   - **SHA256**: `DA:55:02:CF:42:B7:1E:B4:46:E7:AF:41:C4:B6:7E:D9:94:EF:AF:FA:75:E5:41:72:B8:11:F1:4D:A2:D7:92:68`

### Google Play Console
1. Go to Play Console → App signing
2. Upload signing key or use these fingerprints for verification

### Google APIs (OAuth, Maps, etc.)
1. Go to Google Cloud Console → Credentials
2. Add SHA1 and SHA256 fingerprints to your Android app credentials

## ⚠️ Security Notes
- **NEVER** share the keystore file (`key.jks`) or password publicly
- **ALWAYS** backup the keystore file securely
- These SHA fingerprints are safe to share and are required for app configuration
- If you lose the keystore, you cannot update your app on Google Play Store

## 📱 App Information
- **Package Name**: `com.waabo.app`
- **App Name**: Waabo
- **Build Type**: Release
- **Signed**: Yes ✅
