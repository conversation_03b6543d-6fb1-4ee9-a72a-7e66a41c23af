#!/bin/bash

# Script to get SHA fingerprints from keystore
# Usage: ./get-sha-fingerprints.sh

echo "🔐 Getting SHA Certificate Fingerprints for Waabo App"
echo "=================================================="

KEYSTORE_FILE="key.jks"
KEY_ALIAS="waabo-key"

if [ ! -f "$KEYSTORE_FILE" ]; then
    echo "❌ Error: Keystore file '$KEYSTORE_FILE' not found!"
    echo "Make sure you're running this script from the android/ directory"
    exit 1
fi

echo "📋 Keystore Information:"
echo "File: $KEYSTORE_FILE"
echo "Alias: $KEY_ALIAS"
echo ""

echo "🔍 Extracting SHA fingerprints..."
echo ""

# Get SHA1 and SHA256 fingerprints
keytool -list -v -keystore "$KEYSTORE_FILE" -alias "$KEY_ALIAS" | grep -E "(SHA1|SHA256):" | while read line; do
    if [[ $line == *"SHA1"* ]]; then
        sha1=$(echo $line | cut -d' ' -f2-)
        echo "📌 SHA1 (Legacy):"
        echo "   $sha1"
        echo ""
    elif [[ $line == *"SHA256"* ]]; then
        sha256=$(echo $line | cut -d' ' -f2-)
        echo "📌 SHA256 (Recommended):"
        echo "   $sha256"
        echo ""
    fi
done

echo "✅ Use these fingerprints for:"
echo "   • Firebase Console (Project Settings → General)"
echo "   • Google Cloud Console (Credentials)"
echo "   • Google Play Console (App signing)"
echo "   • Any OAuth or API configurations"
echo ""
echo "⚠️  Remember: NEVER share the keystore file or password!"
