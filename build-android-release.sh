#!/bin/bash

# Build Android Release APK Script
# This script builds the web app and then creates a release APK

echo "🚀 Building Waabo Android Release APK..."

# Step 1: Build the web app
echo "📦 Building web app..."
npm run build

# Step 2: Copy web assets to Capacitor
echo "📱 Syncing Capacitor..."
npx cap sync android

# Step 3: Build release APK
echo "🔨 Building release APK..."
cd android
./gradlew assembleRelease

# Step 4: Show output location
echo "✅ Build complete!"
echo "📍 Release APK location: android/app/build/outputs/apk/release/app-release.apk"

# Optional: Open the output directory
if command -v open &> /dev/null; then
    echo "📂 Opening output directory..."
    open app/build/outputs/apk/release/
fi
