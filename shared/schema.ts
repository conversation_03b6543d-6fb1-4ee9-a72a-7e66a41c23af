import { pgTable, text, serial, integer, timestamp, boolean, json, pgEnum } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  username: text("username").notNull().unique(),
  email: text("email"),
  phone: text("phone"),
  phoneVerified: boolean("phone_verified").default(false),
  phoneVerificationToken: text("phone_verification_token"),
  password: text("password"),
  provider: text("provider"),
  providerId: text("provider_id"),
  emailVerified: boolean("email_verified").default(false),
  emailVerificationToken: text("email_verification_token"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  firstName: true,
  lastName: true,
  username: true,
  email: true,
  phone: true,
  password: true,
  provider: true,
  providerId: true,
}).partial({
  username: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Order status enum
export const orderStatusEnum = z.enum([
  "ORDER_CONFIRMED",
  "PROCESSING",
  "IN_TRANSIT",
  "ARRIVED",
  "DELIVERED"
]);

// Custom order status table to allow adding/editing/removing statuses
export const orderStatuses = pgTable("order_statuses", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  label: text("label").notNull(),
  displayOrder: integer("display_order").notNull(),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertOrderStatusSchema = createInsertSchema(orderStatuses).pick({
  code: true,
  label: true,
  displayOrder: true,
  isActive: true,
});

export type InsertOrderStatus = z.infer<typeof insertOrderStatusSchema>;
export type OrderStatusEntry = typeof orderStatuses.$inferSelect;

// Date validator helper
export const dateStringSchema = z.string()
  .transform((val) => val ? new Date(val) : null)
  .optional()
  .nullable();

export type OrderStatus = z.infer<typeof orderStatusEnum>;

// Orders table
export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  reference: text("reference").notNull().unique(),
  userId: integer("user_id").references(() => users.id),
  status: text("status").notNull().default("ORDER_CONFIRMED"),
  estimatedDelivery: timestamp("estimated_delivery"),
  latestUpdate: text("latest_update"),
  latestUpdateTime: timestamp("latest_update_time"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertOrderSchema = createInsertSchema(orders)
  .pick({
    reference: true,
    userId: true,
    status: true,
    estimatedDelivery: true,
    latestUpdate: true,
    latestUpdateTime: true,
  })
  .extend({
    estimatedDelivery: dateStringSchema,
    latestUpdateTime: dateStringSchema,
  });

export type InsertOrder = z.infer<typeof insertOrderSchema>;
export type Order = typeof orders.$inferSelect;

// Products table
export const products = pgTable("products", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: text("price").notNull(),
  imageUrl: text("image_url").notNull(), // Main product image (kept for backward compatibility)
  imageUrls: json("image_urls").default([]).notNull(), // Array of image URLs
  hasFreeshipping: boolean("has_freeshipping").default(true),
  features: json("features").default([]),
  specifications: json("specifications").default({}),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertProductSchema = createInsertSchema(products).pick({
  name: true,
  description: true,
  price: true,
  imageUrl: true,
  imageUrls: true,
  hasFreeshipping: true,
  features: true,
  specifications: true,
});

export type InsertProduct = z.infer<typeof insertProductSchema>;
export type Product = typeof products.$inferSelect;

// Notifications table
export const notifications = pgTable("notifications", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  body: text("body").notNull(),
  sentAt: timestamp("sent_at").defaultNow(),
  targetUserId: integer("target_user_id").references(() => users.id),
  isGlobal: boolean("is_global").default(false),
  isRead: boolean("is_read").default(false),
});

export const insertNotificationSchema = createInsertSchema(notifications).pick({
  title: true,
  body: true,
  targetUserId: true,
  isGlobal: true,
  sentAt: true,
});

export type InsertNotification = z.infer<typeof insertNotificationSchema>;
export type Notification = typeof notifications.$inferSelect;

// Pop-up notifications
export const popupNotifications = pgTable("popup_notifications", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  width: integer("width").default(400),
  height: integer("height").default(300),
  backgroundColor: text("background_color").default("#ffffff"),
  textColor: text("text_color").default("#000000"), // Couleur du texte
  buttonColor: text("button_color").default("#3b82f6"), // Couleur du bouton
  buttonTextColor: text("button_text_color").default("#ffffff"), // Couleur du texte du bouton
  buttonText: text("button_text").default("OK"), // Texte du bouton
  buttonLink: text("button_link"), // Lien vers lequel le bouton redirige
  titleFontSize: text("title_font_size").default("24px"), // Taille du titre
  contentFontSize: text("content_font_size").default("16px"), // Taille du contenu
  fontFamily: text("font_family").default("Inter, sans-serif"),
  fontWeight: text("font_weight").default("normal"),
  borderRadius: text("border_radius").default("8px"), // Bord arrondi
  imageUrl: text("image_url"), // URL de l'image
  imagePosition: text("image_position").default("top"), // Position de l'image (top, bottom, left, right)
  showOnce: boolean("show_once").default(true), // N'afficher qu'une fois
  startDate: timestamp("start_date"), // Date de début (optionnelle)
  endDate: timestamp("end_date"), // Date de fin (optionnelle)
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertPopupNotificationSchema = createInsertSchema(popupNotifications).pick({
  title: true,
  content: true,
  width: true,
  height: true,
  backgroundColor: true,
  textColor: true,
  buttonColor: true,
  buttonTextColor: true,
  buttonText: true,
  buttonLink: true,
  titleFontSize: true,
  contentFontSize: true,
  fontFamily: true,
  fontWeight: true,
  borderRadius: true,
  imageUrl: true,
  imagePosition: true,
  showOnce: true,
  startDate: true,
  endDate: true,
  isActive: true,
});

export type InsertPopupNotification = z.infer<typeof insertPopupNotificationSchema>;
export type PopupNotification = typeof popupNotifications.$inferSelect;

// Stockage des notifications déjà vues par utilisateur
export const userSeenPopups = pgTable("user_seen_popups", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  popupId: integer("popup_id").notNull().references(() => popupNotifications.id, { onDelete: "cascade" }),
  seenAt: timestamp("seen_at").defaultNow().notNull(),
});

// Pages statiques pour le contenu de l'application
export const staticPages = pgTable("static_pages", {
  id: serial("id").primaryKey(),
  slug: text("slug").notNull().unique(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  lastUpdated: timestamp("last_updated").defaultNow()
});

export const insertStaticPageSchema = createInsertSchema(staticPages).pick({
  slug: true,
  title: true,
  content: true
});

export type InsertStaticPage = z.infer<typeof insertStaticPageSchema>;
export type StaticPage = typeof staticPages.$inferSelect;

// Table des jetons de réinitialisation de mot de passe
export const passwordResetTokens = pgTable("password_reset_tokens", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  token: text("token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  used: boolean("used").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertPasswordResetTokenSchema = createInsertSchema(passwordResetTokens).pick({
  userId: true,
  token: true,
  expiresAt: true,
});

export type InsertPasswordResetToken = z.infer<typeof insertPasswordResetTokenSchema>;
export type PasswordResetToken = typeof passwordResetTokens.$inferSelect;

// Table des jetons de vérification d'email
export const emailVerificationTokens = pgTable("email_verification_tokens", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  token: text("token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  used: boolean("used").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertEmailVerificationTokenSchema = createInsertSchema(emailVerificationTokens).pick({
  userId: true,
  token: true,
  expiresAt: true,
});

export type InsertEmailVerificationToken = z.infer<typeof insertEmailVerificationTokenSchema>;
export type EmailVerificationToken = typeof emailVerificationTokens.$inferSelect;

// Saved Orders table
export const savedOrders = pgTable("saved_orders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  orderId: integer("order_id").notNull().references(() => orders.id, { onDelete: "cascade" }),
  savedAt: timestamp("saved_at").defaultNow().notNull(),
});

export const insertSavedOrderSchema = createInsertSchema(savedOrders).pick({
  userId: true,
  orderId: true,
});

export type InsertSavedOrder = z.infer<typeof insertSavedOrderSchema>;
export type SavedOrder = typeof savedOrders.$inferSelect;

// Application Settings table
export const appSettings = pgTable("app_settings", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: text("value").notNull(),
  description: text("description"),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertAppSettingSchema = createInsertSchema(appSettings).pick({
  key: true,
  value: true,
  description: true,
});

export type InsertAppSetting = z.infer<typeof insertAppSettingSchema>;
export type AppSetting = typeof appSettings.$inferSelect;

// Contact Messages table
export const contactMessages = pgTable("contact_messages", {
  id: serial("id").primaryKey(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  email: text("email").notNull(),
  phone: text("phone"),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  isRead: boolean("is_read").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertContactMessageSchema = createInsertSchema(contactMessages).pick({
  firstName: true,
  lastName: true,
  email: true,
  phone: true,
  subject: true,
  message: true,
});

export type InsertContactMessage = z.infer<typeof insertContactMessageSchema>;
export type ContactMessage = typeof contactMessages.$inferSelect;

// Table pour stocker les tokens de notification push des appareils mobiles
export const pushTokens = pgTable("push_tokens", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  token: text("token").notNull().unique(),
  deviceInfo: text("device_info"),
  platform: text("platform"), // 'ios' ou 'android'
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertPushTokenSchema = createInsertSchema(pushTokens).pick({
  userId: true,
  token: true,
  deviceInfo: true,
  platform: true,
});

export type InsertPushToken = z.infer<typeof insertPushTokenSchema>;
export type PushToken = typeof pushTokens.$inferSelect;
