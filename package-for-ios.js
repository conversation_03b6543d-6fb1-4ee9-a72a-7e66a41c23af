/**
 * Script pour préparer l'application Waabo pour le packaging iOS
 */
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m'
};

/**
 * Exécute une commande shell et retourne une promesse
 */
function execPromise(command) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.blue}Exécution de:${colors.reset} ${command}`);
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`${colors.red}Erreur:${colors.reset} ${error.message}`);
        reject(error);
        return;
      }
      if (stderr) {
        console.log(`${colors.yellow}Message:${colors.reset} ${stderr}`);
      }
      console.log(`${colors.green}Sortie:${colors.reset} ${stdout}`);
      resolve(stdout);
    });
  });
}

/**
 * Fonction pour poser une question à l'utilisateur
 */
function askQuestion(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

/**
 * Processus principal
 */
async function main() {
  try {
    console.log(`${colors.green}=== Préparation de l'application Waabo pour iOS ===${colors.reset}\n`);

    // Étape 1: Vérifier si nous sommes sur un Mac (requis pour iOS)
    const platform = process.platform;
    if (platform !== 'darwin') {
      console.log(`${colors.yellow}ATTENTION:${colors.reset} Vous n'êtes pas sur un système macOS. La compilation pour iOS nécessite un Mac.`);
      console.log(`Votre plateforme actuelle est: ${platform}`);
      console.log("Ce script préparera les fichiers, mais vous devrez les transférer sur un Mac pour terminer le processus.");
      
      const proceed = await askQuestion("Voulez-vous continuer quand même? (y/n): ");
      if (proceed.toLowerCase() !== 'y') {
        console.log("Opération annulée.");
        rl.close();
        return;
      }
    }

    // Étape 2: S'assurer que les dépendances sont installées
    console.log(`\n${colors.green}Étape 1: Vérification des dépendances${colors.reset}`);
    await execPromise('npm install');

    // Étape 3: Construire l'application web
    console.log(`\n${colors.green}Étape 2: Construction de l'application web${colors.reset}`);
    await execPromise('npm run build');

    // Étape 4: Mettre à jour la configuration Capacitor si nécessaire
    console.log(`\n${colors.green}Étape 3: Mise à jour de la configuration Capacitor${colors.reset}`);
    
    const capConfigPath = path.join(__dirname, 'capacitor.config.ts');
    let capConfig = fs.readFileSync(capConfigPath, 'utf8');
    
    // S'assurer que le webDir pointe vers le bon dossier
    if (!capConfig.includes('webDir: \'client/dist\'')) {
      console.log('Mise à jour du webDir dans capacitor.config.ts...');
      capConfig = capConfig.replace(/webDir: ['"]client['"]/, 'webDir: \'client/dist\'');
      fs.writeFileSync(capConfigPath, capConfig);
      console.log('Configuration Capacitor mise à jour.');
    } else {
      console.log('La configuration Capacitor est déjà correcte.');
    }

    // Étape 5: Synchroniser avec Capacitor
    console.log(`\n${colors.green}Étape 4: Synchronisation avec Capacitor${colors.reset}`);
    
    try {
      // Vérifier si la plateforme iOS existe déjà
      if (!fs.existsSync(path.join(__dirname, 'ios'))) {
        console.log('Ajout de la plateforme iOS...');
        await execPromise('npx cap add ios');
      } else {
        console.log('La plateforme iOS existe déjà.');
      }
    } catch (error) {
      console.error(`Erreur lors de l'ajout de la plateforme iOS: ${error.message}`);
      throw error;
    }

    // Synchroniser les changements
    console.log('Synchronisation des changements...');
    await execPromise('npx cap sync ios');

    // Étape 6: Résumé et instructions supplémentaires
    console.log(`\n${colors.green}=== Préparation terminée! ===${colors.reset}\n`);
    console.log(`L'application web a été préparée et le projet iOS a été généré.`);
    
    if (platform === 'darwin') {
      console.log('\nPour ouvrir le projet dans Xcode:');
      console.log('npx cap open ios');
      
      const openNow = await askQuestion("\nVoulez-vous ouvrir le projet dans Xcode maintenant? (y/n): ");
      if (openNow.toLowerCase() === 'y') {
        await execPromise('npx cap open ios');
      }
    } else {
      console.log(`\n${colors.yellow}Instructions pour continuer sur un Mac:${colors.reset}`);
      console.log('1. Transférez tout le dossier du projet sur un Mac');
      console.log('2. Installez les dépendances: npm install');
      console.log('3. Ouvrez le projet iOS dans Xcode: npx cap open ios');
    }

    console.log(`\nPour plus d'informations sur la publication sur l'App Store, consultez le fichier IOS_APP_STORE_GUIDE.md`);

  } catch (error) {
    console.error(`${colors.red}Une erreur est survenue:${colors.reset} ${error.message}`);
  } finally {
    rl.close();
  }
}

// Exécuter le script
main();