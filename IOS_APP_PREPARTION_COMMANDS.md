# Commandes pour la préparation iOS

Ce document liste les principales commandes à utiliser pour préparer et packager l'application Waabo pour iOS.

## Préparation et build

```bash
# Construction de l'application web pour production
npm run build

# Ajout de la plateforme iOS à Capacitor (à exécuter une seule fois)
npx cap add ios

# Synchronisation des changements avec la plateforme iOS
npx cap sync ios

# Ouverture du projet dans Xcode (sur Mac uniquement)
npx cap open ios
```

## Utilisation du script d'assistance

Nous avons créé un script d'assistance qui automatise le processus de préparation. Pour l'utiliser :

```bash
# Exécution du script de préparation iOS
node package-for-ios.js
```

Ce script vous guidera à travers les étapes suivantes :
1. Vérification de l'environnement
2. Installation des dépendances
3. Construction de l'application
4. Configuration et synchronisation de Capacitor
5. Génération du projet iOS

## Étapes manuelles sur Mac après le build

Après avoir généré le projet iOS, vous devrez effectuer ces étapes manuelles dans Xcode :

1. Configurer les certificates et profiles de provisioning
2. Ajuster les icônes et écrans de démarrage
3. Configurer les capacités de l'application
4. Construire l'application pour test ou distribution

## Instructions détaillées

Pour des instructions détaillées sur la soumission à l'App Store, consultez le guide complet dans le fichier `IOS_APP_STORE_GUIDE.md`.