# Google Integration Guide for Waabo Mobile App

This guide explains how to configure and use Google authentication in the Waabo mobile app, complementing the existing web authentication.

## Prerequisites

1. A Google Firebase project
2. Your Google project's OAuth 2.0 client ID
3. Firebase configuration file (`google-services.json` for Android, `GoogleService-Info.plist` for iOS)

## Google Firebase Configuration

1. **Create a Firebase project** (if not already done):
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or select your existing project
   - Note down the Firebase project ID

2. **Add your application to Firebase**:
   - In the Firebase console, click "Add app"
   - Choose the platform (Android or iOS)
   - Follow the instructions to register your application:
     - For Android: use `com.waabo.app` as the package ID
     - For iOS: use `com.waabo.app` as the Bundle ID

3. **Download configuration files**:
   - For Android: download the `google-services.json` file
   - For iOS: download the `GoogleService-Info.plist` file

4. **Enable Google authentication**:
   - In the Firebase console, go to "Authentication" > "Sign-in method"
   - Enable the "Google" provider
   - Configure authorized domains by adding your domain `waabo-app.com`

## Application Configuration

### Android (after generating the Android app)

1. **Place the configuration file**:
   - Copy the `google-services.json` file into the `android/app/` folder

2. **Update the `build.gradle` file**:
   - Open `android/app/build.gradle` and verify that the following line is present:
   ```gradle
   apply plugin: 'com.google.gms.google-services'
   ```

3. **Update the root `build.gradle` file**:
   - Open `android/build.gradle` and verify that the following dependencies are present:
   ```gradle
   buildscript {
       dependencies {
           // ...
           classpath 'com.google.gms:google-services:4.3.15'
       }
   }
   ```

### iOS (after generating the iOS app)

1. **Place the configuration file**:
   - Copy the `GoogleService-Info.plist` file into the `ios/App/App/` folder
   - Open the project in Xcode and add the file to the project (by drag and drop in Xcode)

2. **Configure URL Scheme**:
   - Open `ios/App/App/Info.plist` and add:
   ```xml
   <key>CFBundleURLTypes</key>
   <array>
     <dict>
       <key>CFBundleURLSchemes</key>
       <array>
         <string>com.googleusercontent.apps.YOUR-CLIENT-ID</string>
       </array>
     </dict>
   </array>
   ```

## Environment Variables (on your Replit server)

Make sure the following environment variables are defined:

1. `GOOGLE_CLIENT_ID`: Your Google project's OAuth 2.0 client ID
2. `GOOGLE_CLIENT_SECRET`: Your Google project's OAuth 2.0 client secret

## Testing the Integration

To test the integration:

1. Launch the app on a physical device or emulator
2. Press the "Continue with Google" button
3. You should see the Google sign-in screen appear
4. After successful sign-in, you will be redirected to the app

## Troubleshooting

### Common Android Issues

1. **Compilation error**: Verify that the `google-services.json` file is present and properly formatted.

2. **Authentication issue**: Make sure Google authentication is enabled in the Firebase console and your `SHA-1` is configured:
   ```bash
   keytool -exportcert -list -v -alias androiddebugkey -keystore ~/.android/debug.keystore
   ```

### Common iOS Issues

1. **Sign-in error**: Verify that the `GoogleService-Info.plist` file is properly integrated in Xcode.

2. **Invalid URL Scheme**: Make sure the URL Scheme exactly matches what is defined in the Firebase console.

## Code Structure

The Google authentication implementation is organized as follows:

1. **Backend**:
   - `server/app-google-auth.ts`: Handles server-side authentication for mobile apps
   - `server/routes.ts`: Defines API routes for authentication

2. **Frontend**:
   - `client/src/services/capacitorGoogleAuth.ts`: Service for Google authentication on mobile devices
   - `client/src/components/AppGoogleLogin.tsx`: Google sign-in button component for apps
   - `client/src/components/SocialLoginButtons.tsx`: Component that integrates all social login buttons

## References

- [Capacitor Firebase Authentication Documentation](https://github.com/capawesome-team/capacitor-firebase/tree/main/packages/authentication)
- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)
- [Google Sign-In for Android Documentation](https://developers.google.com/identity/sign-in/android/start-integrating)
- [Google Sign-In for iOS Documentation](https://developers.google.com/identity/sign-in/ios/start-integrating)