import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.waabo.app',
  appName: 'Waabo',
  webDir: 'dist',
  plugins: {
    SplashScreen: {
      launchShowDuration: 3000,
      backgroundColor: "#FFFFFF",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP"
    },
    FacebookLogin: {
      appId: "3606778226281282",
      appName: "Waabo",
      androidLogsEnabled: true,
      iosLogsEnabled: true
    },
    FirebaseAuthentication: {
      skipNativeAuth: false,
      providers: ["google.com", "apple.com"]
    },

  },
  ios: {
    contentInset: "always",
    backgroundColor: "#ffffff",
    scheme: "waabo",
    preferredContentMode: "mobile"
  },
  server: {
    url: process.env.CAPACITOR_SERVER_URL || (process.env.PUBLIC_URL ? `https://${process.env.PUBLIC_URL}` : 'https://waabo-app.com'),
    cleartext: true,
    androidScheme: "https",
    iosScheme: "https", // Force HTTPS for iOS to match Android behavior
    allowNavigation: ["waabo-app.com", "*.repl.co", "localhost", "127.0.0.1", "*"]
  }
};

export default config;
