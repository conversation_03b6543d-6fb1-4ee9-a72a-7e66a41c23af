{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "dev:frontend": "vite", "dev:frontend-prod": "VITE_API_URL=https://waabo-app.com vite", "dev:mobile": "vite --mode development --host --port 5174", "dev:mobile-prod": "VITE_API_URL=https://waabo-app.com vite --host --port 5174", "cap:sync-local": "CAPACITOR_SERVER_URL=http://localhost:5174 npx cap sync android", "cap:sync-emulator": "CAPACITOR_SERVER_URL=http://********:5174 npx cap sync android", "cap:sync-android-replit": "CAPACITOR_SERVER_URL=https://fa9e60b0-536f-4378-ad7a-6f78b2fd8070-00-xavz1z84vtkz.worf.replit.dev VITE_API_URL=https://fa9e60b0-536f-4378-ad7a-6f78b2fd8070-00-xavz1z84vtkz.worf.replit.dev npx cap sync android", "cap:sync-ios-replit": "CAPACITOR_SERVER_URL=https://fa9e60b0-536f-4378-ad7a-6f78b2fd8070-00-xavz1z84vtkz.worf.replit.dev VITE_API_URL=https://fa9e60b0-536f-4378-ad7a-6f78b2fd8070-00-xavz1z84vtkz.worf.replit.dev npx cap sync ios", "cap:run-local": "CAPACITOR_SERVER_URL=http://localhost:5174 npx cap run android", "cap:run-emulator": "CAPACITOR_SERVER_URL=http://********:5174 npx cap run android", "cap:run-android-replit": "CAPACITOR_SERVER_URL=https://fa9e60b0-536f-4378-ad7a-6f78b2fd8070-00-xavz1z84vtkz.worf.replit.dev VITE_API_URL=https://fa9e60b0-536f-4378-ad7a-6f78b2fd8070-00-xavz1z84vtkz.worf.replit.dev npx cap run android", "cap:sync-ios": "CAPACITOR_SERVER_URL=http://localhost:5174 npx cap sync ios", "cap:build-ios": "vite build --mode development && CAPACITOR_SERVER_URL=http://localhost:5174 npx cap sync ios", "cap:run-ios": "CAPACITOR_SERVER_URL=http://localhost:5174 npx cap run ios", "cap:open-ios": "npx cap open ios", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "dev:production": "CAPACITOR_SERVER_URL=https://waabo-app.com npx cap sync android", "dev:production-ios": "CAPACITOR_SERVER_URL=https://waabo-app.com npx cap sync ios"}, "dependencies": {"@auth/core": "^0.38.0", "@capacitor-community/facebook-login": "^7.0.0", "@capacitor-firebase/authentication": "^7.2.0", "@capacitor/android": "^7.2.0", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capacitor/push-notifications": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@sendgrid/mail": "^8.1.5", "@tanstack/react-query": "^5.60.5", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.9", "@types/memoizee": "^0.4.12", "@types/passport-facebook": "^3.0.3", "@types/pg": "^8.11.11", "@types/react-helmet": "^6.1.11", "apple-signin-auth": "^2.0.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "exceljs": "^4.4.0", "express": "^4.21.2", "express-session": "^1.18.1", "file-saver": "^2.0.5", "firebase": "^11.8.1", "framer-motion": "^11.18.2", "google-auth-library": "^9.15.1", "html-to-image": "^1.11.13", "input-otp": "^1.2.4", "jsbarcode": "^3.11.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "openid-client": "^6.5.0", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-local": "^1.0.0", "pg": "^8.14.1", "react": "^18.3.1", "react-country-flag": "^3.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-flags-select": "^2.4.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.53.1", "react-icons": "^5.5.0", "react-qr-code": "^2.0.15", "react-resizable-panels": "^2.1.4", "recharts": "^2.13.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.0", "workbox-webpack-plugin": "^7.3.0", "wouter": "^3.3.5", "ws": "^8.18.1", "zod": "^3.23.8", "zod-validation-error": "^3.4.0", "zustand": "^5.0.4"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.0.11", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}