# Guide Technique: Système de Notifications WebSocket de Waabo

## Introduction

Ce document détaille le fonctionnement technique du système de notifications en temps réel de Waabo, basé sur WebSocket. Cette fonctionnalité permet aux utilisateurs de recevoir des notifications instantanées concernant leurs commandes, des messages administratifs, et autres informations importantes sans nécessiter le rechargement de la page.

## Table des matières

1. [Architecture Générale](#architecture-générale)
2. [Implémentation Serveur](#implémentation-serveur)
3. [Implémentation Client](#implémentation-client)
4. [Types de Notifications](#types-de-notifications)
5. [Flux de Données](#flux-de-données)
6. [Sécurité](#sécurité)
7. [Tests et Débogage](#tests-et-débogage)
8. [Optimisations](#optimisations)
9. [Exemples d'utilisation](#exemples-dutilisation)

---

## Architecture Générale

Le système de notifications WebSocket de Waabo est construit selon une architecture client-serveur:

```
+----------------+         WebSocket         +----------------+
|                | <-----------------------> |                |
|  Client React  |        Connexion          |  Serveur Node  |
|                | <-----------------------> |                |
+----------------+                           +----------------+
                                                     |
                                                     | Événements
                                                     v
                                            +----------------+
                                            |   Base de      |
                                            |   Données      |
                                            |  (PostgreSQL)  |
                                            +----------------+
```

### Principes de conception

1. **Temps réel**: Communication instantanée bidirectionnelle
2. **Persistance**: Toutes les notifications sont stockées en base de données
3. **Stateful**: Suivi de l'état de connexion des utilisateurs
4. **Ciblage**: Capacité à envoyer des notifications globales ou spécifiques à un utilisateur
5. **Résilience**: Reconnexion automatique en cas de perte de connexion

---

## Implémentation Serveur

### Configuration du serveur WebSocket

Le serveur WebSocket est configuré dans `server/routes.ts` en utilisant la bibliothèque `ws`:

```typescript
import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';

// Map pour suivre les connexions des utilisateurs
const userConnections = new Map<number, WebSocket[]>();

export async function registerRoutes(app: Express): Promise<Server> {
  // Configuration du serveur HTTP
  const httpServer = createServer(app);
  
  // Configuration du serveur WebSocket sur un chemin spécifique
  const wss = new WebSocketServer({ 
    server: httpServer, 
    path: '/ws' 
  });
  
  // Gestion des connexions WebSocket
  wss.on('connection', (ws: WebSocket, req: IncomingMessage) => {
    // Extraction de l'ID utilisateur depuis les cookies ou paramètres de requête
    const userId = extractUserIdFromRequest(req);
    
    if (!userId) return;
    
    // Association de la connexion à l'utilisateur
    if (!userConnections.has(userId)) {
      userConnections.set(userId, []);
    }
    userConnections.get(userId)?.push(ws);
    
    console.log(`Utilisateur ${userId} connecté via WebSocket`);
    
    // Gestion de la déconnexion
    ws.on('close', () => {
      const connections = userConnections.get(userId) || [];
      const index = connections.indexOf(ws);
      if (index !== -1) {
        connections.splice(index, 1);
      }
      if (connections.length === 0) {
        userConnections.delete(userId);
      }
      console.log(`Utilisateur ${userId} déconnecté`);
    });
    
    // Gestion des messages reçus du client
    ws.on('message', (message) => {
      try {
        const parsedMessage = JSON.parse(message.toString());
        // Traitement des messages si nécessaire
      } catch (error) {
        console.error('Erreur de parsing du message WebSocket:', error);
      }
    });
  });
  
  return httpServer;
}
```

### Fonction d'envoi de notification

```typescript
// Envoi d'une notification à un utilisateur spécifique
function sendNotificationToUser(userId: number, notification: Notification) {
  const connections = userConnections.get(userId);
  if (connections && connections.length > 0) {
    const message = JSON.stringify({
      type: 'notification',
      data: notification
    });
    
    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
  }
}

// Envoi d'une notification globale à tous les utilisateurs connectés
function sendGlobalNotification(notification: Notification) {
  userConnections.forEach((connections, userId) => {
    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'notification',
          data: notification
        }));
      }
    });
  });
}
```

---

## Implémentation Client

### Hook React pour WebSocket

Le hook personnalisé `useWebSocket` gère la connexion WebSocket côté client:

```typescript
// src/hooks/useWebSocket.ts
import { useState, useEffect, useRef } from 'react';
import { useAuth } from './useAuth';

export function useWebSocket() {
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const socketRef = useRef<WebSocket | null>(null);
  
  useEffect(() => {
    if (!user) return;
    
    // Fonction pour établir la connexion
    const connectWebSocket = () => {
      // Déterminer le protocole (ws ou wss)
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      const socket = new WebSocket(wsUrl);
      socketRef.current = socket;
      
      socket.onopen = () => {
        console.log('WebSocket connecté');
        setIsConnected(true);
      };
      
      socket.onclose = () => {
        console.log('WebSocket déconnecté, tentative de reconnexion...');
        setIsConnected(false);
        // Reconnexion après un délai
        setTimeout(connectWebSocket, 3000);
      };
      
      socket.onerror = (error) => {
        console.error('Erreur WebSocket:', error);
      };
      
      socket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          
          if (message.type === 'notification') {
            setNotifications(prev => [message.data, ...prev]);
            // Déclencher une notification système si l'application est en arrière-plan
            if (document.hidden && 'Notification' in window && Notification.permission === 'granted') {
              new Notification(message.data.title, {
                body: message.data.body
              });
            }
          }
        } catch (error) {
          console.error('Erreur de parsing du message WebSocket:', error);
        }
      };
    };
    
    connectWebSocket();
    
    // Nettoyage à la déconnexion
    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, [user]);
  
  return {
    isConnected,
    notifications
  };
}
```

### Intégration dans le composant Layout

```typescript
// src/components/Layout.tsx
import { useWebSocket } from '../hooks/useWebSocket';
import { NotificationIndicator } from './NotificationIndicator';

export function Layout({ children }) {
  const { isConnected, notifications } = useWebSocket();
  
  return (
    <div className="min-h-screen flex flex-col">
      <header>
        <nav className="flex items-center justify-between p-4">
          <Logo />
          <div className="flex items-center gap-4">
            {/* Indicateur de notifications avec badge */}
            <NotificationIndicator 
              count={notifications.filter(n => !n.isRead).length} 
            />
            {/* Autres éléments de navigation */}
          </div>
        </nav>
      </header>
      
      <main className="flex-grow">
        {children}
      </main>
      
      <footer className="bg-gray-100 p-6">
        {/* Contenu du footer */}
      </footer>
    </div>
  );
}
```

---

## Types de Notifications

### 1. Notifications Système

Les notifications système sont persistantes et stockées en base de données. Elles apparaissent dans le centre de notifications de l'utilisateur.

**Structure de données**:

```typescript
interface Notification {
  id: number;
  title: string;
  body: string;
  targetUserId: number | null; // null pour les notifications globales
  isGlobal: boolean;
  isRead: boolean;
  sentAt: Date;
}
```

### 2. Notifications Popup

Les notifications popup sont des messages temporaires qui s'affichent à l'écran lors d'événements importants.

**Structure de données**:

```typescript
interface PopupNotification {
  id: number;
  title: string;
  content: string;
  imageUrl?: string;
  buttonText?: string;
  buttonUrl?: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  targetUserGroups?: string[]; // Types d'utilisateurs ciblés
}

interface PopupSeen {
  id: number;
  userId: number;
  popupId: number;
  seenAt: Date;
}
```

---

## Flux de Données

### Envoi d'une notification

1. **Déclenchement**: Un événement se produit (mise à jour du statut d'une commande, message d'un administrateur, etc.)
2. **Création en DB**: La notification est créée dans la base de données
3. **Diffusion WebSocket**: La notification est envoyée via WebSocket aux utilisateurs concernés
4. **Réception Client**: Le client reçoit la notification et met à jour l'interface utilisateur

### Exemple de flux pour une mise à jour de commande

```
┌─────────────┐         ┌─────────────┐          ┌─────────────┐         ┌─────────────┐
│  Admin UI   │         │   Server    │          │  Database   │         │  Client UI  │
└──────┬──────┘         └──────┬──────┘          └──────┬──────┘         └──────┬──────┘
       │                        │                        │                       │
       │  Update Order Status   │                        │                       │
       │───────────────────────>│                        │                       │
       │                        │                        │                       │
       │                        │  Save Order Update     │                       │
       │                        │───────────────────────>│                       │
       │                        │                        │                       │
       │                        │  Create Notification   │                       │
       │                        │───────────────────────>│                       │
       │                        │                        │                       │
       │                        │  Notification Saved    │                       │
       │                        │<───────────────────────│                       │
       │                        │                        │                       │
       │                        │                        │                       │
       │                        │   Send WebSocket Msg   │                       │
       │                        │───────────────────────────────────────────────>│
       │                        │                        │                       │
       │                        │                        │                       │ Update UI
       │                        │                        │                       │───┐
       │                        │                        │                       │   │
       │                        │                        │                       │<──┘
```

---

## Sécurité

### Authentification et Autorisation

1. **Vérification utilisateur**: L'identité de l'utilisateur est vérifiée lors de la connexion WebSocket via les cookies de session
2. **Isolation des données**: Chaque utilisateur ne reçoit que les notifications qui lui sont destinées
3. **Validation des messages**: Tous les messages reçus sont validés avant traitement

### Prévention des attaques

1. **Rate Limiting**: Limitation du nombre de connexions par utilisateur
2. **Validation des payloads**: Vérification du format et de la taille des messages
3. **Timeout de connexion**: Fermeture des connexions inactives après un certain délai

---

## Tests et Débogage

### Outils de Test

1. **WebSocket Terminal**: Outil pour simuler des connexions WebSocket et envoyer/recevoir des messages
2. **Journalisation côté serveur**: Toutes les connexions et déconnexions sont enregistrées
3. **Moniteur de connexions**: Interface d'administration pour voir les utilisateurs connectés

### Processus de Débogage

1. **Vérifier la connexion WebSocket**: S'assurer que la connexion est établie (voir les logs côté client)
2. **Inspecter les messages entrants/sortants**: Utiliser la console du navigateur pour voir les messages
3. **Vérifier la base de données**: S'assurer que les notifications sont correctement enregistrées
4. **Tester manuellement**: Envoyer une notification de test depuis l'interface d'administration

---

## Optimisations

### Performance

1. **Limitation de la taille des messages**: Les notifications sont de petite taille pour minimiser la bande passante
2. **Limitation du nombre de notifications**: Seules les notifications récentes sont envoyées lors de la connexion
3. **Compression des messages**: Compression des données pour réduire la taille des messages

### Résilience

1. **Reconnexion automatique**: Le client tente de se reconnecter automatiquement en cas de déconnexion
2. **Mise en file d'attente**: Les messages importants sont mis en file d'attente pour être réenvoyés si nécessaire
3. **Détection de l'état de la connexion**: L'interface utilisateur affiche l'état de la connexion WebSocket

---

## Exemples d'utilisation

### Envoi d'une notification depuis l'API

```typescript
// Route pour envoyer une notification
app.post(`${apiPrefix}/notifications`, async (req: Request, res: Response) => {
  try {
    const { title, body, targetUserId, isGlobal } = req.body;
    
    // Validation des données
    if (!title || !body) {
      return res.status(400).json({ message: "Le titre et le corps de la notification sont requis" });
    }
    
    // Création de la notification en base de données
    const notification = await db.insert(schema.notifications).values({
      title,
      body,
      targetUserId: isGlobal ? null : targetUserId,
      isGlobal: !!isGlobal,
      isRead: false,
      sentAt: new Date()
    }).returning();
    
    // Envoi via WebSocket
    if (isGlobal) {
      sendGlobalNotification(notification[0]);
    } else if (targetUserId) {
      sendNotificationToUser(targetUserId, notification[0]);
    }
    
    return res.status(201).json(notification[0]);
  } catch (error) {
    console.error("Erreur lors de l'envoi de la notification:", error);
    return res.status(500).json({ message: "Erreur serveur" });
  }
});
```

### Affichage des notifications côté client

```tsx
// Composant pour afficher les notifications
function NotificationsPanel() {
  const { notifications } = useWebSocket();
  const [filterRead, setFilterRead] = useState(false);
  
  const filteredNotifications = useMemo(() => {
    return filterRead 
      ? notifications.filter(n => !n.isRead) 
      : notifications;
  }, [notifications, filterRead]);
  
  const markAsRead = async (id: number) => {
    try {
      await fetch(`/api/notifications/${id}/read`, {
        method: 'PATCH',
      });
      // Mettre à jour localement
      // (la mise à jour réelle viendra via WebSocket)
    } catch (error) {
      console.error("Erreur lors du marquage de la notification:", error);
    }
  };
  
  return (
    <div className="notifications-panel">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Notifications</h3>
        <Switch
          checked={filterRead}
          onCheckedChange={setFilterRead}
          label="Afficher uniquement les non lues"
        />
      </div>
      
      <div className="space-y-3">
        {filteredNotifications.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <BellOffIcon className="mx-auto h-12 w-12 mb-2" />
            <p>Aucune notification</p>
          </div>
        ) : (
          filteredNotifications.map(notification => (
            <div 
              key={notification.id}
              className={`p-4 rounded-lg border ${notification.isRead ? 'bg-gray-50' : 'bg-white border-green-100'}`}
            >
              <div className="flex justify-between">
                <h4 className="font-medium">{notification.title}</h4>
                <button onClick={() => markAsRead(notification.id)}>
                  <CheckIcon className="h-4 w-4" />
                </button>
              </div>
              <p className="text-sm text-gray-600 mt-1">{notification.body}</p>
              <div className="text-xs text-gray-400 mt-2">
                {formatDistanceToNow(new Date(notification.sentAt), { addSuffix: true, locale: fr })}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
```

---

## Conclusion

Le système de notifications WebSocket de Waabo est une fonctionnalité clé qui améliore l'expérience utilisateur en fournissant des mises à jour en temps réel. Il est conçu pour être performant, fiable et sécurisé, avec une architecture qui permet une extension facile pour prendre en charge de nouveaux types de notifications ou d'interactions en temps réel.

Cette implémentation suit les meilleures pratiques du développement d'applications web modernes, avec une séparation claire des responsabilités entre le client et le serveur, et une utilisation efficace des WebSockets pour la communication bidirectionnelle.

---

**Version du document**: 1.0  
**Date de dernière mise à jour**: 8 mai 2025  
**Auteur**: Équipe de développement Waabo