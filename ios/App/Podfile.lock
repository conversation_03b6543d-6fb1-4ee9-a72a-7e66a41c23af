PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - Capacitor (7.2.0):
    - CapacitorCordova
  - CapacitorCommunityFacebookLogin (7.0.0):
    - Capacitor
    - FBSDKCoreKit (= 17.4.0)
    - FBSDKLoginKit (= 17.4.0)
  - CapacitorCordova (7.2.0)
  - CapacitorFirebaseAuthentication (7.2.0):
    - Capacitor
    - CapacitorFirebaseAuthentication/Lite (= 7.2.0)
    - FirebaseAuth (~> 11.7.0)
  - CapacitorFirebaseAuthentication/Google (7.2.0):
    - Capacitor
    - FirebaseAuth (~> 11.7.0)
    - GoogleSignIn (= 7.1.0)
  - CapacitorFirebaseAuthentication/Lite (7.2.0):
    - Capacitor
    - FirebaseAuth (~> 11.7.0)
  - CapacitorPushNotifications (7.0.1):
    - Capacitor
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - FBAEMKit (17.4.0):
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBSDKCoreKit (17.4.0):
    - FBAEMKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBSDKCoreKit_Basics (17.4.0)
  - FBSDKLoginKit (17.4.0):
    - FBSDKCoreKit (= 17.4.0)
  - FirebaseAppCheckInterop (11.14.0)
  - FirebaseAuth (11.7.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.7.0)
    - FirebaseCoreExtension (~> 11.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.14.0)
  - FirebaseCore (11.7.0):
    - FirebaseCoreInternal (~> 11.7.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.7.0):
    - FirebaseCore (~> 11.7.0)
  - FirebaseCoreInternal (11.7.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - RecaptchaInterop (100.0.0)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorCommunityFacebookLogin (from `../../node_modules/@capacitor-community/facebook-login`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorFirebaseAuthentication (from `../../node_modules/@capacitor-firebase/authentication`)"
  - "CapacitorFirebaseAuthentication/Google (from `../../node_modules/@capacitor-firebase/authentication`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - GoogleSignIn (~> 7.0)

SPEC REPOS:
  trunk:
    - AppAuth
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - RecaptchaInterop

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorCommunityFacebookLogin:
    :path: "../../node_modules/@capacitor-community/facebook-login"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorFirebaseAuthentication:
    :path: "../../node_modules/@capacitor-firebase/authentication"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  Capacitor: 106e7a4205f4618d582b886a975657c61179138d
  CapacitorCommunityFacebookLogin: d509d10f51c6458a3d200ad5c7e87f5f8164b685
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorFirebaseAuthentication: 7043f8e03813762af1b037da82c67963fa5768e0
  CapacitorPushNotifications: 0b653a3264d56daccf0e8769fff15d2f48306de6
  CapacitorSplashScreen: 19cd3573e57507e02d6f34597a8c421e00931487
  FBAEMKit: 58cb5f302cdd715a56d4c1d0dfdd2e423ac1421a
  FBSDKCoreKit: 94d7461d0cecf441b1ba7c41acfff41daa8ccd41
  FBSDKCoreKit_Basics: 151b43db8b834d3f0e02f95d36a44ffd36265e45
  FBSDKLoginKit: 5c1cd53c91a2282b3a4fe6e6d3dcf2b8b0d33d55
  FirebaseAppCheckInterop: a92ba81d0ee3c4cddb1a2e52c668ea51dc63c3ae
  FirebaseAuth: 77e25aa24f3e1c626c5babd3338551fc1669ee0e
  FirebaseAuthInterop: e25b58ecb90f3285085fa2118861a3c9dfdc62ad
  FirebaseCore: 3227e35f4197a924206fbcdc0349325baf4f5de4
  FirebaseCoreExtension: 206c1b399f0d103055207c16f299b28e3dbd1949
  FirebaseCoreInternal: d6c17dafc8dc33614733a8b52df78fcb4394c881
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21

PODFILE CHECKSUM: 40e7e3ded72aaeef3d117a498d05453a9d1a6317

COCOAPODS: 1.16.2
