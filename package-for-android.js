/**
 * Script pour préparer l'application Waabo pour le packaging Android
 */
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m'
};

/**
 * Exécute une commande shell et retourne une promesse
 */
function execPromise(command) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.blue}Exécution de:${colors.reset} ${command}`);
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`${colors.red}Erreur:${colors.reset} ${error.message}`);
        reject(error);
        return;
      }
      if (stderr) {
        console.log(`${colors.yellow}Message:${colors.reset} ${stderr}`);
      }
      console.log(`${colors.green}Sortie:${colors.reset} ${stdout}`);
      resolve(stdout);
    });
  });
}

/**
 * Fonction pour poser une question à l'utilisateur
 */
function askQuestion(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

/**
 * Processus principal
 */
async function main() {
  try {
    console.log(`${colors.green}=== Préparation de l'application Waabo pour Android ===${colors.reset}\n`);

    // Étape 1: S'assurer que les dépendances sont installées
    console.log(`\n${colors.green}Étape 1: Vérification des dépendances${colors.reset}`);
    await execPromise('npm install');

    // Étape 2: Construire l'application web
    console.log(`\n${colors.green}Étape 2: Construction de l'application web${colors.reset}`);
    await execPromise('npm run build');

    // Étape 3: Mettre à jour la configuration Capacitor si nécessaire
    console.log(`\n${colors.green}Étape 3: Mise à jour de la configuration Capacitor${colors.reset}`);
    
    const capConfigPath = path.join(__dirname, 'capacitor.config.ts');
    let capConfig = fs.readFileSync(capConfigPath, 'utf8');
    
    // S'assurer que le webDir pointe vers le bon dossier
    if (!capConfig.includes('webDir: \'dist\'')) {
      console.log('Mise à jour du webDir dans capacitor.config.ts...');
      capConfig = capConfig.replace(/webDir: ['"][^'"]+['"]/, 'webDir: \'dist\'');
      fs.writeFileSync(capConfigPath, capConfig);
      console.log('Configuration Capacitor mise à jour.');
    } else {
      console.log('La configuration webDir est déjà correcte.');
    }
    
    // Vérifier si la configuration Android existe
    if (!capConfig.includes('android: {')) {
      console.log('Ajout de la configuration Android dans capacitor.config.ts...');
      // Trouver la position où insérer la configuration Android (après la configuration iOS)
      const iosConfigEndIndex = capConfig.indexOf('},', capConfig.indexOf('ios: {')) + 2;
      
      const androidConfig = `
  android: {
    backgroundColor: "#ffffff",
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true
  },`;
      
      capConfig = capConfig.slice(0, iosConfigEndIndex) + androidConfig + capConfig.slice(iosConfigEndIndex);
      fs.writeFileSync(capConfigPath, capConfig);
      console.log('Configuration Android ajoutée.');
    } else {
      console.log('La configuration Android existe déjà.');
    }

    // Étape 4: Installer le module @capacitor/android si nécessaire
    console.log(`\n${colors.green}Étape 4: Vérification du module @capacitor/android${colors.reset}`);
    
    try {
      require.resolve('@capacitor/android');
      console.log('Le module @capacitor/android est déjà installé.');
    } catch (e) {
      console.log('Installation du module @capacitor/android...');
      await execPromise('npm install @capacitor/android');
    }

    // Étape 5: Synchroniser avec Capacitor
    console.log(`\n${colors.green}Étape 5: Synchronisation avec Capacitor${colors.reset}`);
    
    try {
      // Vérifier si la plateforme Android existe déjà
      if (!fs.existsSync(path.join(__dirname, 'android'))) {
        console.log('Ajout de la plateforme Android...');
        await execPromise('npx cap add android');
      } else {
        console.log('La plateforme Android existe déjà.');
      }
    } catch (error) {
      console.error(`Erreur lors de l'ajout de la plateforme Android: ${error.message}`);
      throw error;
    }

    // Synchroniser les changements
    console.log('Synchronisation des changements...');
    await execPromise('npx cap sync android');

    // Étape 6: Résumé et instructions supplémentaires
    console.log(`\n${colors.green}=== Préparation terminée! ===${colors.reset}\n`);
    console.log(`L'application web a été préparée et le projet Android a été généré.`);
    
    console.log('\nPour ouvrir le projet dans Android Studio:');
    console.log('npx cap open android');
    
    const openNow = await askQuestion("\nVoulez-vous ouvrir le projet dans Android Studio maintenant? (y/n): ");
    if (openNow.toLowerCase() === 'y') {
      await execPromise('npx cap open android');
    }

    console.log(`\nPour plus d'informations sur la publication sur le Google Play Store, consultez la documentation Capacitor.`);

  } catch (error) {
    console.error(`${colors.red}Une erreur est survenue:${colors.reset} ${error.message}`);
  } finally {
    rl.close();
  }
}

// Exécuter le script
main();