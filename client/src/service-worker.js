// Nom du cache
const CACHE_NAME = 'waabo-cache-v1';

// Liste des ressources à mettre en cache
const urlsToCache = [
  '/',
  '/index.html',
  '/src/assets/waabo.svg',
  '/src/assets/waabo-logo.png',
  '/src/assets/waabo-logo-transparent.png',
  '/src/main.tsx',
  '/src/index.css'
];

// Installation du service worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Cache ouvert');
        return cache.addAll(urlsToCache);
      })
  );
});

// Stratégie de cache : cache d'abord, puis réseau (pour les requêtes GET)
self.addEventListener('fetch', (event) => {
  if (event.request.method !== 'GET') return;
  
  // Ignorer les requêtes d'API pour éviter des problèmes de cache avec les données dynamiques
  if (event.request.url.includes('/api/')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Retourne la réponse du cache si elle existe
        if (response) {
          return response;
        }
        
        // Sinon, récupérer depuis le réseau
        return fetch(event.request)
          .then((response) => {
            // S'assurer que nous avons une réponse valide
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }
            
            // Cloner la réponse car elle ne peut être consommée qu'une fois
            const responseToCache = response.clone();
            
            // Ajouter la nouvelle ressource au cache
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseToCache);
              });
              
            return response;
          });
      })
  );
});

// Nettoyage des anciens caches lors de l'activation d'un nouveau service worker
self.addEventListener('activate', (event) => {
  const cacheWhitelist = [CACHE_NAME];
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            // Supprimer les anciens caches
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});