<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Animated Shipping Logo</title>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
  <style>
    body {
      background-color: #f9f9f9;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      overflow: hidden;
    }

    .logo-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      font-family: 'Roboto', sans-serif;
      font-weight: bold;
      margin-bottom: 80px;
      line-height: 1;
    }

    .logo-waabo span, .logo-express span {
      display: inline-block;
      opacity: 0;
    }

    .logo-waabo span {
      color: #004225;
      font-size: 75px;
      transform: translateY(20px);
      animation: fadeInUp 0.3s forwards;
    }

    .logo-express span {
      color: #e5a100;
      font-size: 30px;
      transform: translateX(-20px);
      animation: slideInLeft 0.3s forwards;
    }

    .logo-waabo span:nth-child(1) { animation-delay: 0s; }
    .logo-waabo span:nth-child(2) { animation-delay: 0.05s; }
    .logo-waabo span:nth-child(3) { animation-delay: 0.1s; }
    .logo-waabo span:nth-child(4) { animation-delay: 0.15s; }
    .logo-waabo span:nth-child(5) { animation-delay: 0.2s; }

    .logo-express {
      margin-top: -8px;
    }

    .logo-express span:nth-child(1) { animation-delay: 0.5s; }
    .logo-express span:nth-child(2) { animation-delay: 0.55s; }
    .logo-express span:nth-child(3) { animation-delay: 0.6s; }
    .logo-express span:nth-child(4) { animation-delay: 0.65s; }
    .logo-express span:nth-child(5) { animation-delay: 0.7s; }
    .logo-express span:nth-child(6) { animation-delay: 0.75s; }
    .logo-express span:nth-child(7) { animation-delay: 0.8s; }

    @keyframes fadeInUp {
      0% {
        opacity: 0;
        transform: translateY(20px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes slideInLeft {
      0% {
        opacity: 0;
        transform: translateX(-20px);
      }
      100% {
        opacity: 1;
        transform: translateX(0);
      }
    }
  </style>
</head>
<body>

<div class="logo-container">
  <div class="logo-waabo">
    <span>w</span><span>a</span><span>a</span><span>b</span><span>o</span>
  </div>
  <div class="logo-express">
    <span>e</span><span>x</span><span>p</span><span>r</span><span>e</span><span>s</span><span>s</span>
  </div>
</div>

</body>
</html>
