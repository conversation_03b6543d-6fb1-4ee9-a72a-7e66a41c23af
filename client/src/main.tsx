import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { initializeCapacitor } from "./capacitor-app";

// Initialiser Capacitor si l'application est exécutée dans un environnement natif
document.addEventListener('DOMContentLoaded', () => {
  initializeCapacitor()
    .catch(error => console.error('Erreur d\'initialisation Capacitor:', error));
});

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <App />
    <Toaster />
  </QueryClientProvider>
);
