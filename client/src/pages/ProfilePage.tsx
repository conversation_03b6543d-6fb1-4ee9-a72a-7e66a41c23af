import React, { useState, useEffect, useRef } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Order } from "@/lib/types";
import { Eye, EyeOff, QrCode } from "lucide-react";

import {
  RiNotification3Line,
  RiUserLine,
  RiShareForwardLine,
  RiMailCheckLine,
  RiMailCloseLine,
  RiMailSendLine,
  RiArrowLeftSLine,
  RiQrCodeLine,
  RiBarcodeLine,
} from "react-icons/ri";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Schema de validation pour la mise à jour du profil
const profileSchema = z.object({
  firstName: z.string().min(2, {
    message: "Le prénom doit contenir au moins 2 caractères",
  }),
  lastName: z.string().min(2, {
    message: "Le nom doit contenir au moins 2 caractères",
  }),
  username: z.string().min(3, {
    message: "Le nom d'utilisateur doit contenir au moins 3 caractères",
  }),
  email: z
    .string()
    .email({ message: "Veuillez saisir une adresse email valide" }),
  phone: z.string().optional(),
});

// Schema de validation pour le changement de mot de passe
const passwordSchema = z
  .object({
    currentPassword: z.string().min(6, {
      message: "Le mot de passe actuel doit contenir au moins 6 caractères",
    }),
    newPassword: z.string().min(6, {
      message: "Le nouveau mot de passe doit contenir au moins 6 caractères",
    }),
    confirmPassword: z
      .string()
      .min(6, { message: "Veuillez confirmer votre nouveau mot de passe" }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Les mots de passe ne correspondent pas",
    path: ["confirmPassword"],
  });

type ProfileFormValues = z.infer<typeof profileSchema>;
type PasswordFormValues = z.infer<typeof passwordSchema>;

const formatDate = (date: Date | string | undefined) => {
  if (!date) return "";
  const d = new Date(date);
  return d.toLocaleDateString("fr-FR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });
};

const ProfilePage: React.FC = () => {
  const { user, logoutMutation } = useAuth();
  const { toast } = useToast();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isVerifyingPhone, setIsVerifyingPhone] = useState(false);
  const [otpDigits, setOtpDigits] = useState<string[]>(Array(6).fill(''));
  const [otpError, setOtpError] = useState<string | null>(null);
  // Initialiser l'onglet par défaut
  const [activeTab, setActiveTab] = useState<string>(() => localStorage.getItem("activeTab") || "profile");

  useEffect(() => {
    localStorage.setItem("activeTab", activeTab);
  }, [activeTab]);
  const firstOtpInputRef = useRef<HTMLInputElement>(null);
  const profileMenuRef = useRef<HTMLDivElement>(null);

  // Effet pour fermer le menu lorsqu'on clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        profileMenuRef.current &&
        !profileMenuRef.current.contains(event.target as Node)
      ) {
        setIsProfileMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Récupération des commandes de l'utilisateur
  const { data: userOrders, isLoading } = useQuery<Order[]>({
    queryKey: ["/api/orders/user", user?.id],
    queryFn: async () => {
      if (!user) return [];
      const res = await fetch(`/api/orders/user/${user.id}`);
      if (!res.ok) return [];
      return res.json();
    },
    enabled: !!user,
  });

  // Formulaire pour mettre à jour le profil
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      username: user?.username || "",
      email: user?.email || "",
      phone: user?.phone || "",
    },
  });

  // Mettre à jour les valeurs par défaut lorsque les données de l'utilisateur sont chargées
  useEffect(() => {
    if (user) {
      profileForm.reset({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        username: user.username,
        email: user.email || "",
        phone: user.phone || "",
      });
    }
  }, [user, profileForm]);

  // Mutation pour mettre à jour le profil
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormValues) => {
      if (!user?.id) {
        throw new Error("Vous devez être connecté pour effectuer cette action");
      }

      console.log(
        "Mise à jour du profil pour:",
        user.id,
        "avec les données:",
        data,
      );

      // Simplifier les données à envoyer
      const updateData = {
        firstName: data.firstName,
        lastName: data.lastName,
        username: data.username,
        email: data.email,
        phone: data.phone
      };

      return await apiRequest("PATCH", `/api/users/${user.id}`, updateData);
    },
    onSuccess: (data) => {
      console.log("Profil mis à jour avec succès:", data);

      // Rafraîchir les données utilisateur
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });

      // Afficher une notification de réussite
      toast({
        title: "Profil mis à jour",
        description: "Vos informations ont été mises à jour avec succès",
      });

      // Si le numéro de téléphone a changé, informer l'utilisateur et ouvrir l'onglet vérification
      if (user?.phone !== data.phone && data.phone) {
        toast({
          title: "Numéro de téléphone modifié",
          description: "Vous devez vérifier votre nouveau numéro",
        });

        // Passer directement à l'onglet de vérification téléphonique
        setTimeout(() => {
          setActiveTab("verify-phone");
        }, 500);
      }
    },
    onError: (error: Error) => {
      console.error("Erreur lors de la mise à jour du profil:", error);
      toast({
        title: "Erreur",
        description:
          error.message ||
          "Une erreur est survenue lors de la mise à jour du profil",
        variant: "destructive",
      });
    },
  });

  // Formulaire pour changer le mot de passe
  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // Mutation pour changer le mot de passe
  const changePasswordMutation = useMutation({
    mutationFn: async (data: PasswordFormValues) => {
      if (!user?.id) {
        throw new Error("Vous devez être connecté pour effectuer cette action");
      }

      console.log("Changement de mot de passe pour:", user.id);
      return await apiRequest("PATCH", `/api/users/${user.id}/password`, {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
    },
    onSuccess: (data) => {
      console.log("Mot de passe mis à jour avec succès:", data);
      passwordForm.reset();
      toast({
        title: "Mot de passe mis à jour",
        description: "Votre mot de passe a été changé avec succès",
      });
    },
    onError: (error: Error) => {
      console.error("Erreur lors du changement de mot de passe:", error);
      toast({
        title: "Erreur",
        description:
          error.message ||
          "Une erreur est survenue lors du changement de mot de passe",
        variant: "destructive",
      });
    },
  });

  // Soumettre le formulaire de profil
  const onProfileSubmit = (data: ProfileFormValues) => {
    updateProfileMutation.mutate(data);
  };

  // Soumettre le formulaire de mot de passe
  const onPasswordSubmit = (data: PasswordFormValues) => {
    changePasswordMutation.mutate(data);
  };

  // Mutation pour envoyer l'email de vérification
  const sendVerificationEmailMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) {
        throw new Error("Vous devez être connecté pour effectuer cette action");
      }
      return await apiRequest("POST", "/api/email/send-verification", {});
    },
    onSuccess: () => {
      toast({
        title: "Email envoyé",
        description:
          "Un email de vérification a été envoyé à votre adresse email",
      });
    },
    onError: (error: Error) => {
      console.error(
        "Erreur lors de l'envoi de l'email de vérification:",
        error,
      );
      toast({
        title: "Erreur",
        description:
          error.message ||
          "Une erreur est survenue lors de l'envoi de l'email de vérification",
        variant: "destructive",
      });
    },
  });

  // Mutation pour envoyer le code de vérification du téléphone
  const sendPhoneVerificationMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id || !user?.phone) {
        throw new Error("Vous devez être connecté et avoir un numéro de téléphone enregistré");
      }
      return await apiRequest("POST", "/api/phone/send-verification", { phone: user.phone });
    },
    onSuccess: () => {
      console.log('Code de vérification envoyé avec succès, activation du mode de vérification');

      // Réinitialiser les champs OTP
      setOtpDigits(Array(6).fill(''));
      setOtpError(null);

      // Activer IMMÉDIATEMENT le mode de vérification pour afficher l'interface OTP
      setIsVerifyingPhone(true);

      // Focus sur le premier champ OTP avec un petit délai
      setTimeout(() => {
        if (firstOtpInputRef.current) {
          firstOtpInputRef.current.focus();
        }
      }, 200);

      // Pas de notification toast pour éviter toute interférence avec l'interface OTP
    },
    onError: (error: Error) => {
      console.error("Erreur lors de l'envoi du code de vérification:", error);
      toast({
        title: "Erreur d'envoi",
        description: error.message || "Une erreur est survenue lors de l'envoi du code de vérification",
        variant: "destructive",
      });
    },
  });

  // Mutation pour vérifier le téléphone
  const verifyPhoneMutation = useMutation({
    mutationFn: async (code: string) => {
      if (!user?.id) {
        throw new Error("Vous devez être connecté pour effectuer cette action");
      }
      return await apiRequest("POST", "/api/phone/verify", { code });
    },
    onSuccess: (data) => {
      setIsVerifyingPhone(false);
      setOtpDigits(Array(6).fill(''));

      queryClient.invalidateQueries({ queryKey: ["/api/user"] });

      toast({
        title: "Téléphone vérifié",
        description: "Votre numéro de téléphone a été vérifié avec succès",
      });
    },
    onError: (error: Error) => {
      console.error("Erreur lors de la vérification du téléphone:", error);
      setOtpError(error.message || "Code de vérification invalide. Veuillez réessayer.");
    },
  });

  // Fonction pour gérer l'envoi du code de vérification téléphonique
  const handleSendPhoneVerification = () => {
    if (!user?.phone) {
      toast({
        title: "Erreur",
        description: "Vous devez d'abord ajouter un numéro de téléphone dans votre profil",
        variant: "destructive",
      });
      return;
    }

    console.log("Envoi du code de vérification au numéro:", user.phone);

    // Activer IMMÉDIATEMENT le mode de vérification avant la mutation
    setIsVerifyingPhone(true);

    // Envoi de la mutation (se fait en parallèle à la mise à jour de l'UI)
    sendPhoneVerificationMutation.mutate();
  };

  // Fonction pour gérer la vérification du code OTP
  const handleVerifyPhone = (e: React.FormEvent) => {
    e.preventDefault();

    // Assurer que tous les champs sont remplis
    if (otpDigits.some(d => !d)) {
      setOtpError("Veuillez remplir tous les champs du code");
      return;
    }

    // Combiner les chiffres en un seul code
    const code = otpDigits.join('');

    // Vérifier le code
    verifyPhoneMutation.mutate(code);
  };

  // Fonction pour gérer le changement d'un chiffre OTP
  const handleOtpChange = (index: number, value: string) => {
    // Autoriser seulement les chiffres
    if (!/^\d*$/.test(value)) return;

    // Créer une copie du tableau des chiffres OTP
    const newOtpDigits = [...otpDigits];

    // Mettre à jour le chiffre
    newOtpDigits[index] = value;

    // Mettre à jour l'état
    setOtpDigits(newOtpDigits);

    // Effacer l'erreur précédente
    setOtpError(null);

    // Si un chiffre est entré et qu'il y a un champ suivant, focus sur ce champ
    if (value && index < 5) {
      const nextInput = document.querySelector(`input[name="otp-${index + 1}"]`) as HTMLInputElement;
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  // Fonction pour gérer les touches spéciales dans les champs OTP
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    // Si Backspace et champ vide, focus sur le champ précédent
    if (e.key === 'Backspace' && !otpDigits[index] && index > 0) {
      const prevInput = document.querySelector(`input[name="otp-${index - 1}"]`) as HTMLInputElement;
      if (prevInput) {
        prevInput.focus();
      }
    }
  };

  // Fonction pour renvoyer le code de vérification
  const handleResendCode = () => {
    // Réinitialiser les champs OTP
    setOtpDigits(Array(6).fill(''));
    setOtpError(null);

    // Renvoyer le code
    sendPhoneVerificationMutation.mutate();

    toast({
      title: "Code renvoyé",
      description: "Un nouveau code de vérification a été envoyé à votre numéro de téléphone",
    });
  };

  // Aucune fonction de partage n'est nécessaire car nous avons supprimé le bouton

  return (
    <div className="min-h-screen flex flex-col safe-area-layout">
      {/* Header fixe */}
      <header className="bg-white text-[#004d25] shadow-sm fixed top-0 left-0 right-0 z-50 safe-area-header">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <Link
              href="/"
              className="flex items-center text-[#004d25] hover:text-[#003d1e]"
            >
              <RiArrowLeftSLine className="h-6 w-6 mr-1" />
              <span>Retour</span>
            </Link>
            <div className="flex items-center space-x-4">
              {user && <></>}
              {user && user.username === "admin" && (
                <Link href="/admin">
                  <Button
                    variant="outline"
                    className="text-[#004d25] border-[#004d25] hover:bg-[#f0f9f5]"
                  >
                    Administration
                  </Button>
                </Link>
              )}
              {/* Le bouton de déconnexion a été supprimé */}
            </div>
          </div>
        </div>
      </header>

      {/* Espace pour compenser la hauteur du header fixe */}
      <div className="h-16"></div>

      {/* Main content */}
      <main className="flex-grow safe-area-content">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="border-4 border-dashed border-white bg-white rounded-lg h-full">
              <div className="text-center py-6 bg-white">
                <div className="relative inline-block">
                  <div className="w-24 h-24 rounded-full bg-gray-300 text-[#004d25] flex items-center justify-center text-3xl font-bold">
                    {user?.firstName?.charAt(0) || user?.username?.charAt(0) || "U"}
                  </div>
                </div>
                <h1 className="mt-4 text-2xl font-bold text-gray-900">
                  {user?.firstName
                    ? `${user.firstName} ${user.lastName || ""}`
                    : user?.username}
                </h1>
                <p className="text-gray-500">
                  {user?.email || "Aucun email renseigné"}
                </p>
              </div>

              <Tabs
                defaultValue={activeTab}
                onValueChange={setActiveTab}
                className="max-w-3xl mx-auto"
              >
                <TabsList className="grid grid-cols-4 mb-6">
                  <TabsTrigger value="profile">Profil</TabsTrigger>
                  <TabsTrigger value="security">Sécurité</TabsTrigger>
                  <TabsTrigger value="verify-email">Email</TabsTrigger>
                  <TabsTrigger value="verify-phone">Téléphone</TabsTrigger>
                </TabsList>

                <TabsContent value="profile">
                  <Card>
                    <CardContent className="pt-6">
                      <Form {...profileForm}>
                        <form
                          onSubmit={profileForm.handleSubmit(onProfileSubmit)}
                          className="space-y-6"
                        >
                          {/* Nom */}
                          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <FormField
                              control={profileForm.control}
                              name="firstName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Prénom</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Votre prénom" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={profileForm.control}
                              name="lastName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nom</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Votre nom" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* Nom d'utilisateur */}
                          <FormField
                            control={profileForm.control}
                            name="username"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Nom d'utilisateur</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Votre nom d'utilisateur"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Email */}
                          <FormField
                            control={profileForm.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email</FormLabel>
                                <FormControl>
                                  <Input
                                    type="email"
                                    placeholder="Votre adresse email"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Téléphone */}
                          <FormField
                            control={profileForm.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Téléphone</FormLabel>
                                <FormControl>
                                  <Input
                                    type="tel"
                                    placeholder="Votre numéro de téléphone"
                                    {...field}
                                    value={field.value || ""}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <Button
                            type="submit"
                            className="w-full bg-[#004d25] hover:bg-[#003d1e]"
                            disabled={updateProfileMutation.isPending}
                          >
                            {updateProfileMutation.isPending
                              ? "Mise à jour..."
                              : "Mettre à jour le profil"}
                          </Button>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>

                  {userOrders && userOrders.length > 0 && (
                    <div className="mt-6">
                      <Card>
                        <CardContent className="pt-6">
                          <h3 className="text-lg font-medium mb-4">Vos commandes récentes</h3>
                          <div className="space-y-4">
                            {userOrders.slice(0, 3).map((order) => (
                              <div
                                key={order.id}
                                className="p-4 border rounded-lg hover:bg-gray-50"
                              >
                                <div className="flex justify-between">
                                  <div>
                                    <p className="font-medium">
                                      Commande #{order.reference}
                                    </p>
                                    <p className="text-sm text-gray-500">
                                      {formatDate(order.createdAt)}
                                    </p>
                                  </div>
                                  <div>
                                    <Link href={`/tracking/${order.reference}`}>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-[#004d25] border-[#004d25] hover:bg-[#f0f9f5]"
                                      >
                                        Suivre
                                      </Button>
                                    </Link>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                          {userOrders.length > 3 && (
                            <div className="mt-4 text-center">
                              <Link href="/orders">
                                <Button variant="link" className="text-[#004d25]">
                                  Voir toutes vos commandes
                                </Button>
                              </Link>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="security">
                  <Card>
                    <CardContent className="pt-6">
                      <h3 className="text-lg font-medium mb-4">Modifier votre mot de passe</h3>
                      <Form {...passwordForm}>
                        <form
                          onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
                          className="space-y-6"
                        >
                          {/* Mot de passe actuel */}
                          <FormField
                            control={passwordForm.control}
                            name="currentPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Mot de passe actuel</FormLabel>
                                <div className="relative">
                                  <FormControl>
                                    <Input
                                      type={showCurrentPassword ? "text" : "password"}
                                      placeholder="Votre mot de passe actuel"
                                      {...field}
                                    />
                                  </FormControl>
                                  <button
                                    type="button"
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                    onClick={() =>
                                      setShowCurrentPassword(!showCurrentPassword)
                                    }
                                  >
                                    {showCurrentPassword ? (
                                      <EyeOff className="h-4 w-4" />
                                    ) : (
                                      <Eye className="h-4 w-4" />
                                    )}
                                  </button>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Nouveau mot de passe */}
                          <FormField
                            control={passwordForm.control}
                            name="newPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Nouveau mot de passe</FormLabel>
                                <div className="relative">
                                  <FormControl>
                                    <Input
                                      type={showNewPassword ? "text" : "password"}
                                      placeholder="Votre nouveau mot de passe"
                                      {...field}
                                    />
                                  </FormControl>
                                  <button
                                    type="button"
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                    onClick={() => setShowNewPassword(!showNewPassword)}
                                  >
                                    {showNewPassword ? (
                                      <EyeOff className="h-4 w-4" />
                                    ) : (
                                      <Eye className="h-4 w-4" />
                                    )}
                                  </button>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Confirmation du nouveau mot de passe */}
                          <FormField
                            control={passwordForm.control}
                            name="confirmPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Confirmer le mot de passe</FormLabel>
                                <div className="relative">
                                  <FormControl>
                                    <Input
                                      type={showConfirmPassword ? "text" : "password"}
                                      placeholder="Confirmez votre nouveau mot de passe"
                                      {...field}
                                    />
                                  </FormControl>
                                  <button
                                    type="button"
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                    onClick={() =>
                                      setShowConfirmPassword(!showConfirmPassword)
                                    }
                                  >
                                    {showConfirmPassword ? (
                                      <EyeOff className="h-4 w-4" />
                                    ) : (
                                      <Eye className="h-4 w-4" />
                                    )}
                                  </button>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <Button
                            type="submit"
                            className="w-full bg-[#004d25] hover:bg-[#003d1e]"
                            disabled={changePasswordMutation.isPending}
                          >
                            {changePasswordMutation.isPending
                              ? "Changement..."
                              : "Changer le mot de passe"}
                          </Button>
                        </form>
                      </Form>


                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="verify-email">
                  <Card>
                    <CardContent className="p-0">
                      <div className="border-b p-4">
                        <h3 className="text-lg font-medium text-gray-900">
                          <span>Vérification de l'email</span>
                        </h3>
                      </div>

                      <div className="p-6">
                        {user?.emailVerified ? (
                          <div className="flex items-center justify-center flex-col text-center py-8">
                            <div className="w-16 h-16 rounded-full bg-green-50 flex items-center justify-center mb-4">
                              <RiMailCheckLine className="h-8 w-8 text-green-500" />
                            </div>
                            <h4 className="text-lg font-medium text-gray-800">
                              Email vérifié avec succès
                            </h4>
                            <p className="mt-2 text-sm text-gray-600 max-w-md">
                              Votre adresse email{" "}
                              <span className="font-semibold text-gray-700">
                                {user.email}
                              </span>{" "}
                              a été vérifiée avec succès. Vous recevrez
                              maintenant toutes les notifications
                              importantes sur cette adresse.
                            </p>
                          </div>
                        ) : (
                          <div className="space-y-6">
                            <p className="text-sm text-amber-700 mb-6">
                              Votre adresse email{" "}
                              <span className="font-semibold">
                                {user?.email}
                              </span>{" "}
                              n'est pas encore vérifiée. Veuillez
                              vérifier votre boîte de réception après
                              avoir demandé un email de vérification.
                            </p>

                            <div className="bg-[#f8fafb] rounded-2xl p-5 border border-gray-100">
                              <h5 className="font-medium text-gray-700 mb-3">
                                Pourquoi vérifier votre email ?
                              </h5>
                              <ul className="space-y-3">
                                <li className="flex gap-3 items-start">
                                  <div className="w-6 h-6 bg-[#e9f5ef] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg
                                      className="h-3.5 w-3.5 text-[#004d25]"
                                      xmlns="http://www.w3.org/2000/svg"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2.5"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <path d="M5 12l5 5l10 -10"></path>
                                    </svg>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    Recevoir des notifications importantes concernant
                                    vos commandes et livraisons
                                  </p>
                                </li>
                                <li className="flex gap-3 items-start">
                                  <div className="w-6 h-6 bg-[#e9f5ef] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg
                                      className="h-3.5 w-3.5 text-[#004d25]"
                                      xmlns="http://www.w3.org/2000/svg"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2.5"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <path d="M5 12l5 5l10 -10"></path>
                                    </svg>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    Sécuriser votre compte en prouvant votre
                                    identité
                                  </p>
                                </li>
                                <li className="flex gap-3 items-start">
                                  <div className="w-6 h-6 bg-[#e9f5ef] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg
                                      className="h-3.5 w-3.5 text-[#004d25]"
                                      xmlns="http://www.w3.org/2000/svg"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2.5"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <path d="M5 12l5 5l10 -10"></path>
                                    </svg>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    Récupérer votre compte en cas d'oubli de mot de
                                    passe
                                  </p>
                                </li>
                              </ul>
                            </div>

                            <div className="mt-6 text-center">
                              <Button
                                onClick={() => sendVerificationEmailMutation.mutate()}
                                className="inline-flex items-center justify-center bg-[#004d25] text-white hover:bg-[#003d1e]"
                                disabled={sendVerificationEmailMutation.isPending}
                              >
                                <RiMailSendLine className="h-5 w-5 mr-2" />
                                {sendVerificationEmailMutation.isPending
                                  ? "Envoi en cours..."
                                  : "Envoyer un email de vérification"}
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="verify-phone">
                  <Card>
                    <CardContent className="p-0">
                      <div className="border-b p-4">
                        <h3 className="text-lg font-medium text-gray-900">
                          Vérification du numéro de téléphone
                        </h3>
                      </div>

                      <div className="p-6">
                        {user?.phoneVerified ? (
                          <div className="flex items-center justify-center flex-col text-center py-8">
                            <div className="w-16 h-16 rounded-full bg-green-50 flex items-center justify-center mb-4">
                              <RiMailCheckLine className="h-8 w-8 text-green-500" />
                            </div>
                            <h4 className="text-lg font-medium text-gray-800">
                              Téléphone vérifié avec succès
                            </h4>
                            <p className="mt-2 text-sm text-gray-600 max-w-md">
                              Votre numéro {" "}
                              <span className="font-semibold text-gray-700">
                                {user.phone}
                              </span>{" "}
                              a été vérifié avec succès. Vous pourrez recevoir des SMS importants sur ce numéro.
                            </p>
                          </div>
                        ) : (
                          <div className="space-y-6">
                            {!isVerifyingPhone ? (
                              <>
                                {user?.phone ? (
                                  <p className="text-sm text-amber-700">
                                    Votre numéro {" "}
                                    <span className="font-semibold">
                                      {user.phone}
                                    </span>{" "}
                                    n'est pas encore vérifié. Cliquez sur le bouton ci-dessous
                                    pour recevoir un code de vérification par SMS.
                                  </p>
                                ) : (
                                  <p className="text-sm text-amber-700">
                                    Vous n'avez pas encore ajouté de numéro de téléphone.
                                    Veuillez d'abord ajouter un numéro dans votre profil.
                                  </p>
                                )}

                                <div className="bg-[#f8fafb] rounded-2xl p-5 border border-gray-100">
                                  <h5 className="font-medium text-gray-700 mb-3">
                                    Pourquoi vérifier votre téléphone ?
                                  </h5>
                                  <ul className="space-y-3">
                                    <li className="flex gap-3 items-start">
                                      <div className="w-6 h-6 bg-[#e9f5ef] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                        <svg
                                          className="h-3.5 w-3.5 text-[#004d25]"
                                          xmlns="http://www.w3.org/2000/svg"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2.5"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                        >
                                          <path d="M5 12l5 5l10 -10"></path>
                                        </svg>
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        Recevoir des alertes SMS concernant le statut de vos colis
                                      </p>
                                    </li>
                                    <li className="flex gap-3 items-start">
                                      <div className="w-6 h-6 bg-[#e9f5ef] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                        <svg
                                          className="h-3.5 w-3.5 text-[#004d25]"
                                          xmlns="http://www.w3.org/2000/svg"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2.5"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                        >
                                          <path d="M5 12l5 5l10 -10"></path>
                                        </svg>
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        Renforcer la sécurité de votre compte avec la vérification en deux étapes
                                      </p>
                                    </li>
                                    <li className="flex gap-3 items-start">
                                      <div className="w-6 h-6 bg-[#e9f5ef] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                        <svg
                                          className="h-3.5 w-3.5 text-[#004d25]"
                                          xmlns="http://www.w3.org/2000/svg"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2.5"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                        >
                                          <path d="M5 12l5 5l10 -10"></path>
                                        </svg>
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        Être contacté par notre équipe en cas de problème avec votre commande
                                      </p>
                                    </li>
                                  </ul>
                                </div>

                                <div className="mt-6 text-center">
                                  <Button
                                    onClick={handleSendPhoneVerification}
                                    className="inline-flex items-center justify-center bg-[#004d25] text-white hover:bg-[#003d1e]"
                                    disabled={!user?.phone || sendPhoneVerificationMutation.isPending}
                                  >
                                    <RiMailSendLine className="h-5 w-5 mr-2" />
                                    {sendPhoneVerificationMutation.isPending
                                      ? "Envoi en cours..."
                                      : "Recevoir un code par SMS"}
                                  </Button>
                                </div>
                              </>
                            ) : (
                              <div className="flex flex-col items-center">
                                <div className="w-16 h-16 rounded-full bg-amber-50 flex items-center justify-center mb-4">
                                  <RiMailSendLine className="h-8 w-8 text-amber-500" />
                                </div>

                                <h4 className="text-lg font-medium text-gray-800 mb-2">
                                  Entrez le code de vérification
                                </h4>

                                <p className="text-sm text-gray-600 text-center mb-6">
                                  Nous avons envoyé un code à 6 chiffres au{" "}
                                  <span className="font-medium">{user?.phone}</span>.
                                  Veuillez l'entrer ci-dessous.
                                </p>

                                <form onSubmit={handleVerifyPhone} className="w-full max-w-md">
                                  <div className="flex justify-between items-center gap-2 mb-6">
                                    {Array(6).fill(0).map((_, index) => (
                                      <Input
                                        key={index}
                                        ref={index === 0 ? firstOtpInputRef : undefined}
                                        name={`otp-${index}`}
                                        type="text"
                                        maxLength={1}
                                        className="w-12 h-14 text-center text-xl font-medium"
                                        value={otpDigits[index] || ''}
                                        onChange={(e) => handleOtpChange(index, e.target.value)}
                                        onKeyDown={(e) => handleKeyDown(e, index)}
                                      />
                                    ))}
                                  </div>

                                  {otpError && (
                                    <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4 text-sm">
                                      {otpError}
                                    </div>
                                  )}

                                  <div className="flex flex-col sm:flex-row gap-3">
                                    <Button
                                      type="submit"
                                      className="flex-1 bg-[#004d25] hover:bg-[#003d1e]"
                                      disabled={verifyPhoneMutation.isPending}
                                    >
                                      {verifyPhoneMutation.isPending ? 'Vérification...' : 'Vérifier'}
                                    </Button>

                                    <Button
                                      type="button"
                                      variant="outline"
                                      className="flex-1"
                                      onClick={handleResendCode}
                                      disabled={sendPhoneVerificationMutation.isPending}
                                    >
                                      {sendPhoneVerificationMutation.isPending ? 'Envoi...' : 'Renvoyer le code'}
                                    </Button>

                                    <Button
                                      type="button"
                                      variant="ghost"
                                      className="flex-1"
                                      onClick={() => setIsVerifyingPhone(false)}
                                    >
                                      Annuler
                                    </Button>
                                  </div>
                                </form>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <p className="text-sm text-gray-500 text-center">
            &copy; {new Date().getFullYear()} Waabo Express. Tous droits réservés.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default ProfilePage;