import React, { useEffect } from 'react';

interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onFinish }) => {
  useEffect(() => {
    // After animation completes, call onFinish
    const finishTimeout = setTimeout(() => {
      onFinish();
    }, 3500);

    return () => {
      clearTimeout(finishTimeout);
    };
  }, [onFinish]);

  // Style inspiré du HTML fourni pour l'animation
  return (
    <div className="fixed inset-0 flex flex-col justify-center items-center bg-[#f9f9f9] z-50" style={{ fontFamily: "'Roboto', sans-serif" }}>
      <div className="flex flex-col items-end mb-20 leading-none">
        {/* Logo waabo avec animation fadeInUp */}
        <div className="text-[#004225] text-[75px] font-bold">
          <span className="inline-block animate-fadeInUp" style={{ animationDelay: '0s', animationFillMode: 'forwards', opacity: 0, transform: 'translateY(20px)' }}>w</span>
          <span className="inline-block animate-fadeInUp" style={{ animationDelay: '0.05s', animationFillMode: 'forwards', opacity: 0, transform: 'translateY(20px)' }}>a</span>
          <span className="inline-block animate-fadeInUp" style={{ animationDelay: '0.1s', animationFillMode: 'forwards', opacity: 0, transform: 'translateY(20px)' }}>a</span>
          <span className="inline-block animate-fadeInUp" style={{ animationDelay: '0.15s', animationFillMode: 'forwards', opacity: 0, transform: 'translateY(20px)' }}>b</span>
          <span className="inline-block animate-fadeInUp" style={{ animationDelay: '0.2s', animationFillMode: 'forwards', opacity: 0, transform: 'translateY(20px)' }}>o</span>
        </div>
        
        {/* Logo express avec animation slideInLeft, aligné à droite */}
        <div className="text-[#e5a100] text-[30px] font-bold -mt-2 flex justify-end">
          <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.5s', animationFillMode: 'forwards', opacity: 0, transform: 'translateX(-20px)' }}>e</span>
          <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.55s', animationFillMode: 'forwards', opacity: 0, transform: 'translateX(-20px)' }}>x</span>
          <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.6s', animationFillMode: 'forwards', opacity: 0, transform: 'translateX(-20px)' }}>p</span>
          <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.65s', animationFillMode: 'forwards', opacity: 0, transform: 'translateX(-20px)' }}>r</span>
          <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.7s', animationFillMode: 'forwards', opacity: 0, transform: 'translateX(-20px)' }}>e</span>
          <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.75s', animationFillMode: 'forwards', opacity: 0, transform: 'translateX(-20px)' }}>s</span>
          <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.8s', animationFillMode: 'forwards', opacity: 0, transform: 'translateX(-20px)' }}>s</span>
        </div>
      </div>
      
      {/* Indicateur de chargement */}
      <div className="flex justify-center items-center mt-5">
        <div className="w-3 h-3 mx-1 rounded-full animate-blink bg-[#004225]" style={{ animationDelay: '0s', animationDuration: '1.2s' }}></div>
        <div className="w-3 h-3 mx-1 rounded-full animate-blink bg-[#004225]" style={{ animationDelay: '0.2s', animationDuration: '1.2s' }}></div>
        <div className="w-3 h-3 mx-1 rounded-full animate-blink bg-[#004225]" style={{ animationDelay: '0.4s', animationDuration: '1.2s' }}></div>
      </div>
    </div>
  );
};

export default SplashScreen;