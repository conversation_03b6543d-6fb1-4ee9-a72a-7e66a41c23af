import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Logo from '@/components/Logo';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { LockKeyhole, Mail, ArrowLeft, Check } from 'lucide-react';

const forgotPasswordSchema = z.object({
  email: z.string().email('Veuillez entrer un email valide').min(1, 'Email requis'),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

const ForgotPasswordScreen: React.FC = () => {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const { register, handleSubmit, formState: { errors } } = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    setIsLoading(true);
    
    try {
      // Envoi de la demande de réinitialisation à l'API
      await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      // Quelle que soit la réponse (même si l'email n'existe pas), on affiche le même message
      // pour des raisons de sécurité
      setIsLoading(false);
      setEmailSent(true);
      toast({
        title: 'Email envoyé',
        description: 'Si cet email existe dans notre base de données, vous recevrez un lien pour réinitialiser votre mot de passe.',
      });
    } catch (error) {
      console.error('Erreur lors de la demande de réinitialisation:', error);
      setIsLoading(false);
      toast({
        title: 'Erreur',
        description: 'Une erreur est survenue lors de la demande de réinitialisation.',
        variant: 'destructive',
      });
    };
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            <Logo size="large" withText linkTo="/auth" />
          </div>
        </div>
        
        <Card className="border-0 shadow-lg overflow-hidden">
          <div className="h-2 bg-gradient-to-r from-[#004d25] to-[#1e6e42]"></div>
          
          <CardHeader className="pt-8">
            <div className="flex justify-center mb-4">
              <div className="bg-[#004d25]/10 p-3 rounded-full">
                <LockKeyhole className="h-8 w-8 text-[#004d25]" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-center text-[#004d25]">
              Mot de passe oublié?
            </CardTitle>
            <CardDescription className="text-center max-w-xs mx-auto">
              Nous vous enverrons un lien pour réinitialiser votre mot de passe. Cela peut prendre quelques minutes.
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pb-8 px-8">
            {!emailSent ? (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-neutral-700">Adresse email</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input 
                      id="email" 
                      type="email" 
                      placeholder="Votre adresse email" 
                      className="pl-10 bg-white border-neutral-200"
                      {...register('email')} 
                    />
                  </div>
                  {errors.email && (
                    <p className="text-sm text-red-500 font-medium">{errors.email.message}</p>
                  )}
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full bg-[#004d25] hover:bg-[#003d1e] text-white rounded-md transition-all py-6 h-auto font-semibold shadow-md hover:shadow-lg" 
                  disabled={isLoading}
                >
                  {isLoading ? 'Envoi en cours...' : 'Envoyer le lien de réinitialisation'}
                </Button>
              </form>
            ) : (
              <div className="text-center space-y-4">
                <div className="flex justify-center mb-6">
                  <div className="bg-green-100 rounded-full p-3">
                    <Check className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-neutral-800">Email envoyé</h3>
                <div className="bg-green-50 border border-green-100 text-green-800 p-4 rounded-lg mb-2">
                  <p className="text-sm">Un email avec des instructions pour réinitialiser votre mot de passe a été envoyé à l'adresse que vous avez fournie.</p>
                </div>
                <p className="text-sm text-neutral-600 mb-2">
                  Vérifiez votre boîte de réception et suivez les instructions contenues dans l'email.
                </p>
                <p className="text-xs text-neutral-500 mb-6">
                  Si vous ne recevez pas l'email dans les prochaines minutes, vérifiez votre dossier spam.
                </p>
              </div>
            )}
          </CardContent>
          
          <CardFooter className="flex justify-center pb-8 px-8">
            <Button 
              variant="outline"
              onClick={() => setLocation('/auth')} 
              className="w-full border-[#004d25] text-[#004d25] hover:bg-[#004d25]/5 group"
            >
              <ArrowLeft className="mr-2 h-4 w-4 group-hover:translate-x-[-2px] transition-transform" />
              Retour à la connexion
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPasswordScreen;