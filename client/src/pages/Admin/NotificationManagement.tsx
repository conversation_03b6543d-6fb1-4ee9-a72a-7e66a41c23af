import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import Logo from '@/components/Logo';
import { RiDashboardLine, RiShoppingBag3Line, RiBox3Line, RiBellLine, RiLogoutBoxLine, RiSendPlaneFill, RiGroupLine, RiSearchLine } from 'react-icons/ri';
import { apiRequest } from '@/lib/queryClient';
import { User } from '@/lib/types';
import { ScrollArea } from '@/components/ui/scroll-area';

const NotificationManagement: React.FC = () => {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  
  // Récupérer la liste des utilisateurs
  const { data: users = [], isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: ['/api/users'],
    staleTime: 60000, // 1 minute
  });

  // Notification form schema
  const notificationSchema = z.object({
    title: z.string().min(1, 'Le titre est requis'),
    body: z.string().min(1, 'Le message est requis'),
    isGlobal: z.boolean().default(true),
    userIds: z.string().optional()
      .refine(
        val => !val || val.split(',').every(id => !isNaN(Number(id.trim())) && Number(id.trim()) > 0),
        { message: 'Les IDs utilisateurs doivent être des nombres séparés par des virgules' }
      ),
  });

  // Form for sending notifications
  const notificationForm = useForm<z.infer<typeof notificationSchema>>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      title: '',
      body: '',
      isGlobal: true,
      userIds: '',
    },
  });

  // Create notification mutation
  const sendNotificationMutation = useMutation({
    mutationFn: async (data: z.infer<typeof notificationSchema>) => {
      // Si c'est global, on envoie juste une notification globale
      if (data.isGlobal) {
        return apiRequest('POST', '/api/notifications', {
          title: data.title,
          body: data.body,
          isGlobal: true
        });
      }
      
      // Si ce n'est pas global et qu'on a des IDs utilisateurs
      if (data.userIds) {
        const userIdList = data.userIds.split(',')
          .map(id => id.trim())
          .filter(id => id !== '')
          .map(id => parseInt(id));
        
        // Pour chaque ID utilisateur, envoyer une notification
        const promises = userIdList.map(userId => 
          apiRequest('POST', '/api/notifications', {
            title: data.title,
            body: data.body,
            isGlobal: false,
            targetUserId: userId
          })
        );
        
        // Attendre que toutes les notifications soient envoyées
        return Promise.all(promises);
      }
      
      // Si pas d'IDs et pas global
      return Promise.reject(new Error('Veuillez spécifier des IDs utilisateurs ou sélectionner "Tous les utilisateurs"'));
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Notification(s) envoyée(s) avec succès',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      notificationForm.reset({
        title: '',
        body: '',
        isGlobal: true,
        userIds: '',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erreur',
        description: error instanceof Error ? error.message : 'Échec de l\'envoi de la notification',
        variant: 'destructive',
      });
    },
  });

  // Filtrer les utilisateurs en fonction du terme de recherche
  const filteredUsers = users.filter(user => {
    const searchLower = searchTerm.toLowerCase();
    const usernameMatch = user.username && user.username.toLowerCase().includes(searchLower);
    const firstNameMatch = user.firstName && user.firstName.toLowerCase().includes(searchLower);
    const lastNameMatch = user.lastName && user.lastName.toLowerCase().includes(searchLower);
    const phoneMatch = user.phone && user.phone.toLowerCase().includes(searchLower);
    
    return usernameMatch || firstNameMatch || lastNameMatch || phoneMatch;
  });

  // Gérer la sélection/désélection d'un utilisateur
  const toggleUser = (userId: number) => {
    setSelectedUserIds(prev => {
      if (prev.includes(userId)) {
        return prev.filter(id => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };
  
  // Mettre à jour le champ userIds du form quand les utilisateurs sélectionnés changent
  React.useEffect(() => {
    if (!notificationForm.getValues('isGlobal') && selectedUserIds.length > 0) {
      notificationForm.setValue('userIds', selectedUserIds.join(','));
    }
  }, [selectedUserIds, notificationForm]);
  
  // Réinitialiser les utilisateurs sélectionnés quand on passe en mode global
  React.useEffect(() => {
    if (notificationForm.getValues('isGlobal')) {
      setSelectedUserIds([]);
    }
  }, [notificationForm.watch('isGlobal')]);

  const handleSubmit = (data: z.infer<typeof notificationSchema>) => {
    sendNotificationMutation.mutate(data);
  };

  const navigateTo = (path: string) => {
    setLocation(path);
  };

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-secondary text-white p-6 flex flex-col">
        <div className="mb-8">
          <Logo className="mb-2" />
          <h1 className="text-xl font-bold">Panneau d'Administration</h1>
        </div>
        
        <nav className="flex-grow">
          <ul className="space-y-2">
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin')}
              >
                <RiDashboardLine className="mr-2 h-5 w-5" />
                Tableau de bord
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/orders')}
              >
                <RiBox3Line className="mr-2 h-5 w-5" />
                Commandes
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/products')}
              >
                <RiShoppingBag3Line className="mr-2 h-5 w-5" />
                Produits
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark bg-secondary-dark"
                onClick={() => navigateTo('/admin/notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Notifications
              </Button>
            </li>
          </ul>
        </nav>
        
        <div className="mt-auto">
          <Button 
            variant="ghost" 
            className="w-full justify-start text-white hover:bg-secondary-dark"
            onClick={() => navigateTo('/')}
          >
            <RiLogoutBoxLine className="mr-2 h-5 w-5" />
            Déconnexion
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow bg-gray-50 p-6">
        <header className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Notifications</h1>
          <p className="text-gray-500">Envoyez des notifications à vos utilisateurs</p>
        </header>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Send Notification Form */}
          <Card>
            <CardHeader>
              <CardTitle>Envoyer une Notification</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...notificationForm}>
                <form onSubmit={notificationForm.handleSubmit(handleSubmit)} className="space-y-6">
                  <FormField
                    control={notificationForm.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Titre de la notification</FormLabel>
                        <FormControl>
                          <Input placeholder="Entrez le titre de la notification" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={notificationForm.control}
                    name="body"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Message</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Entrez le message de la notification" 
                            className="min-h-24"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={notificationForm.control}
                    name="isGlobal"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Envoyer à tous les utilisateurs</FormLabel>
                          <FormDescription>
                            Désactivez pour cibler des utilisateurs spécifiques
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {!notificationForm.watch('isGlobal') && (
                    <FormField
                      control={notificationForm.control}
                      name="userIds"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sélection des Utilisateurs</FormLabel>
                          
                          <div className="border rounded-md p-4 space-y-4">
                            {/* Champ de recherche */}
                            <div className="flex items-center relative">
                              <Input
                                type="text"
                                placeholder="Rechercher par nom ou numéro de téléphone"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                              />
                              <RiSearchLine className="absolute left-3 text-gray-500 h-5 w-5" />
                            </div>
                            
                            {/* Liste des utilisateurs */}
                            <div className="border rounded-md">
                              {isLoadingUsers ? (
                                <div className="p-4 text-center">
                                  <p>Chargement des utilisateurs...</p>
                                </div>
                              ) : (
                                <>
                                  {filteredUsers.length === 0 ? (
                                    <div className="p-4 text-center">
                                      <p>Aucun utilisateur trouvé</p>
                                    </div>
                                  ) : (
                                    <ScrollArea className="h-60">
                                      <div className="space-y-1 p-2">
                                        {filteredUsers.map((user) => (
                                          <div 
                                            key={user.id} 
                                            className="flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-md"
                                          >
                                            <Checkbox 
                                              id={`user-${user.id}`}
                                              checked={selectedUserIds.includes(user.id)}
                                              onCheckedChange={() => toggleUser(user.id)}
                                            />
                                            <label 
                                              htmlFor={`user-${user.id}`}
                                              className="flex-1 cursor-pointer"
                                            >
                                              <div className="flex justify-between">
                                                <span className="font-medium">
                                                  {user.firstName} {user.lastName || user.username}
                                                </span>
                                                {user.phone && (
                                                  <span className="text-gray-500 text-sm">
                                                    {user.phone}
                                                  </span>
                                                )}
                                              </div>
                                            </label>
                                          </div>
                                        ))}
                                      </div>
                                    </ScrollArea>
                                  )}
                                </>
                              )}
                            </div>
                            
                            <div className="flex items-center justify-between">
                              <div className="text-sm text-gray-500">
                                {selectedUserIds.length} utilisateur(s) sélectionné(s)
                              </div>
                              {selectedUserIds.length > 0 && (
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => setSelectedUserIds([])}
                                >
                                  Tout désélectionner
                                </Button>
                              )}
                            </div>
                          </div>
                          
                          <FormControl>
                            <Input 
                              type="hidden"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                  
                  <Button 
                    type="submit" 
                    className="w-full bg-primary hover:bg-primary-dark"
                    disabled={sendNotificationMutation.isPending}
                  >
                    {sendNotificationMutation.isPending ? (
                      <span className="flex items-center">
                        <span className="mr-2">Envoi en cours...</span>
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <RiSendPlaneFill className="mr-2" />
                        Envoyer la notification
                      </span>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
          
          {/* Notification Tips */}
          <Card>
            <CardHeader>
              <CardTitle>Bonnes pratiques pour les notifications</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium">Soyez concis</h3>
                <p className="text-sm text-gray-600">
                  Les notifications doivent être brèves et précises. Utilisez un langage clair que les utilisateurs peuvent comprendre en un coup d'œil.
                </p>
              </div>
              
              <div>
                <h3 className="font-medium">Soyez pertinent</h3>
                <p className="text-sm text-gray-600">
                  Envoyez des notifications qui intéressent vos utilisateurs au bon moment. Évitez d'envoyer trop de notifications.
                </p>
              </div>
              
              <div>
                <h3 className="font-medium">Incluez un appel à l'action clair</h3>
                <p className="text-sm text-gray-600">
                  Indiquez clairement l'action que vous souhaitez que les utilisateurs prennent quand ils reçoivent votre notification.
                </p>
              </div>
              
              <div>
                <h3 className="font-medium">Testez avant d'envoyer</h3>
                <p className="text-sm text-gray-600">
                  Prévisualisez toujours comment votre notification apparaîtra sur différents appareils avant de l'envoyer à tous les utilisateurs.
                </p>
              </div>
              
              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <h3 className="font-medium text-yellow-800">Note</h3>
                <p className="text-sm text-yellow-700">
                  À des fins de démonstration, les notifications apparaîtront comme des toasts dans l'application. Dans un environnement de production, elles seraient envoyées comme de véritables notifications push aux appareils des utilisateurs.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default NotificationManagement;
