import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from '@/components/ui/badge';
import Logo from '@/components/Logo';
import { RiDashboardLine, RiUserLine, RiUserSearchLine, RiFileExcel2Line, RiDeleteBin5Line } from 'react-icons/ri';
import { apiRequest } from '@/lib/queryClient';
import { User } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';

const UserManagement: React.FC = () => {
  const [_, setLocation] = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [userToDeleteId, setUserToDeleteId] = useState<number | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: users, isLoading } = useQuery<User[]>({
    queryKey: ['/api/users'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/users');
      return Array.isArray(res) ? res : [];
    }
  });

  // Get URL params to see if we should view a specific user
  const location = window.location.search;
  const params = new URLSearchParams(location);
  const viewUserId = params.get('view');

  useEffect(() => {
    if (viewUserId) {
      const userId = parseInt(viewUserId);
      if (!isNaN(userId)) {
        setSelectedUserId(userId);
        setIsDetailModalOpen(true);
      }
    }
  }, [viewUserId]);

  const filteredUsers = users?.filter(user => 
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const selectedUser = users?.find(user => user.id === selectedUserId);

  const handleUserClick = (userId: number) => {
    setSelectedUserId(userId);
    setIsDetailModalOpen(true);
    // Update URL without navigating
    const url = new URL(window.location.href);
    url.searchParams.set('view', userId.toString());
    window.history.pushState({}, '', url);
  };

  const closeDetailModal = () => {
    setIsDetailModalOpen(false);
    // Remove the view parameter from URL
    const url = new URL(window.location.href);
    url.searchParams.delete('view');
    window.history.pushState({}, '', url);
  };

  const formatDate = (dateString?: Date) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };
  
  // Fonction pour supprimer un utilisateur
  const handleDeleteUser = async () => {
    if (!userToDeleteId) return;
    
    try {
      setIsDeleting(true);
      
      // Empêcher la suppression de l'administrateur (ID 1)
      if (userToDeleteId === 1) {
        toast({
          title: "Action non autorisée",
          description: "L'administrateur principal ne peut pas être supprimé",
          variant: "destructive",
        });
        return;
      }
      
      // Appel API pour supprimer l'utilisateur
      const response = await apiRequest('DELETE', `/api/users/${userToDeleteId}`);
      
      if (response && response.message) {
        // Rafraîchir la liste des utilisateurs
        queryClient.invalidateQueries({ queryKey: ['/api/users'] });
        
        toast({
          title: "Utilisateur supprimé",
          description: "L'utilisateur et toutes ses données associées ont été supprimés",
          variant: "default",
        });
        
        // Si nous étions en train de visualiser l'utilisateur supprimé, fermer le modal
        if (selectedUserId === userToDeleteId) {
          setIsDetailModalOpen(false);
        }
      }
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'utilisateur:', error);
      toast({
        title: "Échec de la suppression",
        description: "Une erreur s'est produite lors de la suppression de l'utilisateur",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setUserToDeleteId(null);
    }
  };
  
  // Fonction pour demander confirmation avant de supprimer un utilisateur
  const confirmDeleteUser = (userId: number, event?: React.MouseEvent) => {
    // Empêcher la propagation pour éviter d'ouvrir le modal de détails
    if (event) {
      event.stopPropagation();
    }
    
    setUserToDeleteId(userId);
    setIsDeleteDialogOpen(true);
  };
  
  // Fonction pour exporter les utilisateurs au format Excel
  const handleExportExcel = async () => {
    try {
      setIsExporting(true);
      
      // Télécharger directement le fichier Excel depuis l'API
      const response = await fetch('/api/users/export/excel', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Important pour inclure les cookies d'authentification
      });
      
      if (!response.ok) {
        throw new Error("Erreur lors de l'exportation Excel");
      }
      
      // Créer un blob à partir de la réponse
      const blob = await response.blob();
      
      // Créer un URL pour le blob
      const url = window.URL.createObjectURL(blob);
      
      // Créer un lien et cliquer dessus pour déclencher le téléchargement
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = 'utilisateurs.xlsx';
      document.body.appendChild(a);
      a.click();
      
      // Nettoyer
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Export réussi",
        description: "La liste des utilisateurs a été exportée avec succès",
        variant: "default",
      });
      
    } catch (error) {
      console.error('Erreur lors de l\'exportation Excel:', error);
      toast({
        title: "Échec de l'exportation",
        description: "Une erreur s'est produite lors de l'exportation des données",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-secondary text-white p-6 flex flex-col">
        <div className="mb-8">
          <Logo className="mb-2" />
          <h1 className="text-xl font-bold">Panneau Admin</h1>
        </div>
        
        <nav className="flex-grow">
          <ul className="space-y-2">
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => setLocation('/admin')}
              >
                <RiDashboardLine className="mr-2 h-5 w-5" />
                Tableau de bord
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark bg-secondary-dark"
                onClick={() => setLocation('/admin/users')}
              >
                <RiUserLine className="mr-2 h-5 w-5" />
                Utilisateurs
              </Button>
            </li>
          </ul>
        </nav>
        
        <div className="mt-auto">
          <Button 
            variant="ghost" 
            className="w-full justify-start text-white hover:bg-secondary-dark"
            onClick={() => setLocation('/')}
          >
            Déconnexion
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow bg-gray-50 p-6">
        <header className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Utilisateurs</h1>
          <p className="text-gray-500">Consulter et gérer les informations des utilisateurs</p>
        </header>
        
        {/* Search Box */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <RiUserSearchLine className="text-gray-400" />
              <Input 
                placeholder="Rechercher un utilisateur par nom ou email..." 
                className="flex-grow"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Button variant="secondary" onClick={() => setSearchTerm('')}>
                Effacer
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Users List */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Liste des Utilisateurs</CardTitle>
              <CardDescription>
                {filteredUsers?.length || 0} utilisateur(s) trouvé(s)
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center gap-1 text-green-700 border-green-200 hover:bg-green-50"
              onClick={handleExportExcel}
              disabled={isExporting || !filteredUsers || filteredUsers.length === 0}
            >
              <RiFileExcel2Line className="h-5 w-5" />
              {isExporting ? 'Exportation...' : 'Exporter vers Excel'}
            </Button>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center p-6">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : (
              filteredUsers && filteredUsers.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Nom d'utilisateur</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Email vérifié</TableHead>
                      <TableHead>Téléphone</TableHead>
                      <TableHead>Tél. vérifié</TableHead>
                      <TableHead>Date d'inscription</TableHead>
                      <TableHead>Méthode de connexion</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map(user => (
                      <TableRow key={user.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => handleUserClick(user.id)}>
                        <TableCell>{user.id}</TableCell>
                        <TableCell>{user.username}</TableCell>
                        <TableCell>{user.email || 'N/A'}</TableCell>
                        <TableCell>
                          {user.emailVerified ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              Vérifié
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                              Non vérifié
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{user.phone || 'N/A'}</TableCell>
                        <TableCell>
                          {user.phone ? (
                            user.phoneVerified ? (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                Vérifié
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                                Non vérifié
                              </Badge>
                            )
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </TableCell>
                        <TableCell>{user.createdAt ? formatDate(user.createdAt) : 'N/A'}</TableCell>
                        <TableCell>
                          {user.provider ? (
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                              {user.provider}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                              Email/Mot de passe
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell onClick={(e) => e.stopPropagation()}>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="sm" onClick={() => handleUserClick(user.id)}>
                              Voir détails
                            </Button>
                            {user.id !== 1 && (
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="text-red-600 hover:text-red-800 hover:bg-red-50"
                                onClick={(e) => confirmDeleteUser(user.id, e)}
                              >
                                <RiDeleteBin5Line className="mr-1 h-4 w-4" />
                                Supprimer
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  Aucun utilisateur trouvé.
                </div>
              )
            )}
          </CardContent>
        </Card>
        
        {/* User Details Modal */}
        <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Détails de l'utilisateur</DialogTitle>
              <DialogDescription>
                Informations complètes sur l'utilisateur sélectionné
              </DialogDescription>
            </DialogHeader>
            
            {selectedUser ? (
              <div className="space-y-6 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-sm text-gray-500">ID</h3>
                    <p>{selectedUser.id}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-gray-500">Nom d'utilisateur</h3>
                    <p>{selectedUser.username}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-gray-500">Email</h3>
                    <p>{selectedUser.email || 'Non renseigné'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-gray-500">Téléphone</h3>
                    <p>{selectedUser.phone || 'Non renseigné'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-gray-500">Date d'inscription</h3>
                    <p>{selectedUser.createdAt ? formatDate(selectedUser.createdAt) : 'N/A'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-gray-500">Dernière connexion</h3>
                    <p>{selectedUser.updatedAt ? formatDate(selectedUser.updatedAt) : 'N/A'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-gray-500">Méthode de connexion</h3>
                    <p>{selectedUser.provider || 'Email/Mot de passe'}</p>
                  </div>
                  {selectedUser.providerId && (
                    <div>
                      <h3 className="font-medium text-sm text-gray-500">ID Externe</h3>
                      <p>{selectedUser.providerId}</p>
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">Activité récente</h3>
                  <p className="text-gray-500 text-sm">
                    Les informations d'activité de l'utilisateur apparaîtront ici.
                  </p>
                </div>
                
                <div className="pt-4 flex justify-between">
                  {selectedUser.id !== 1 && (
                    <Button 
                      variant="destructive" 
                      onClick={() => confirmDeleteUser(selectedUser.id)}
                    >
                      <RiDeleteBin5Line className="mr-2 h-4 w-4" />
                      Supprimer cet utilisateur
                    </Button>
                  )}
                  <Button variant="outline" onClick={closeDetailModal}>
                    Fermer
                  </Button>
                </div>
              </div>
            ) : (
              <div className="py-4 text-center">
                Chargement des données...
              </div>
            )}
          </DialogContent>
        </Dialog>
        
        {/* Confirmation de suppression d'utilisateur */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer cet utilisateur ?</AlertDialogTitle>
              <AlertDialogDescription>
                Cette action ne peut pas être annulée. Toutes les données associées à cet utilisateur (commandes, notifications, préférences) seront définitivement supprimées.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>Annuler</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleDeleteUser} 
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                    Suppression...
                  </>
                ) : (
                  'Confirmer la suppression'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default UserManagement;