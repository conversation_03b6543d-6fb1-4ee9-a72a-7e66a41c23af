import { useState } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Loader2, Save } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { ProtectedRoute } from "@/lib/protected-route";
import Layout from "@/components/Layout";

import { StaticPage } from '@/pages/StaticPage';

const formSchema = z.object({
  title: z.string().min(1, "Le titre est requis"),
  content: z.string().min(1, "Le contenu est requis"),
});

type FormValues = z.infer<typeof formSchema>;

function EditStaticPageContent() {
  const { slug } = useParams<{ slug: string }>();
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  
  const { data: page, isLoading } = useQuery<StaticPage>({
    queryKey: [`/api/static-pages/${slug}`],
    enabled: !!slug,
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: page?.title || "",
      content: page?.content || "",
    },
    values: {
      title: page?.title || "",
      content: page?.content || "",
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      try {
        console.log("Envoi de la requête de mise à jour:", values);
        const result = await apiRequest("PATCH", `/api/static-pages/${slug}`, values);
        console.log("Résultat de la mise à jour:", result);
        return result;
      } catch (error) {
        console.error("Erreur lors de la mise à jour:", error);
        throw error;
      }
    },
    onSuccess: () => {
      // Invalider la requête pour forcer un rechargement des données
      queryClient.invalidateQueries({ queryKey: [`/api/static-pages/${slug}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/static-pages`] });
      
      toast({
        title: "Page mise à jour",
        description: "La page a été mise à jour avec succès.",
      });
      
      // Attendre un peu avant de naviguer pour s'assurer que les données sont mises à jour
      setTimeout(() => {
        navigate(`/${slug}`);
      }, 500);
    },
    onError: (error) => {
      console.error("Erreur de mutation:", error);
      toast({
        title: "Erreur",
        description: `Une erreur est survenue: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: FormValues) => {
    updateMutation.mutate(values);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-[60vh]">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
        </div>
      </Layout>
    );
  }

  if (!page) {
    return (
      <Layout>
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold mb-4">Page non trouvée</h1>
          <p className="mb-6">La page que vous cherchez à modifier n'existe pas.</p>
          <Button onClick={() => navigate("/admin")}>Retour à l'administration</Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Modifier la page: {page.title}</CardTitle>
            <CardDescription>
              Slug: {page.slug} | Dernière modification: {new Date(page.lastUpdated).toLocaleDateString('fr-FR')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Titre</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contenu</FormLabel>
                      <FormControl>
                        <Textarea 
                          {...field} 
                          className="min-h-[300px] font-mono"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate(`/${page.slug}`)}
                  >
                    Annuler
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={updateMutation.isPending}
                    className="gap-2"
                  >
                    {updateMutation.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                    <Save className="h-4 w-4" />
                    Enregistrer
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}

export default function EditStaticPage() {
  return (
    <ProtectedRoute
      path="/admin/static-pages/edit/:slug"
      component={EditStaticPageContent}
    />
  );
}