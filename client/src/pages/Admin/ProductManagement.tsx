import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import Logo from '@/components/Logo';
import { Product } from '@/lib/types';
import { RiDashboardLine, RiShoppingBag3Line, RiBox3Line, RiBellLine, RiLogoutBoxLine, RiAddLine, RiEdit2Line, RiDeleteBin6Line } from 'react-icons/ri';
import { apiRequest } from '@/lib/queryClient';

const ProductManagement: React.FC = () => {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Fetch products
  const { data: products, isLoading } = useQuery<Product[]>({
    queryKey: ['/api/products'],
  });

  // Product form schema
  const productSchema = z.object({
    name: z.string().min(1, 'Nom requis'),
    description: z.string().min(1, 'Description requise'),
    price: z.string().min(1, 'Prix requis'),
    imageUrl: z.string().optional()
      .refine(val => !val || val.trim() === '' || val.startsWith('http') || val.startsWith('data:'), {
        message: 'L\'URL doit commencer par http://, https:// ou être une image encodée en base64'
      }),
    imageUrls: z.array(z.string()).default([]),
    hasFreeshipping: z.boolean().default(true),
    features: z.string().transform(val => val ? val.split('\n').filter(Boolean) : []),
    specifications: z.string().optional().transform(val => {
      if (!val) return {};
      
      try {
        return val.split('\n')
          .filter(Boolean)
          .reduce((acc: Record<string, string>, line) => {
            const [key, value] = line.split(':').map(s => s.trim());
            if (key && value) acc[key] = value;
            return acc;
          }, {});
      } catch (e) {
        return {};
      }
    }),
  });

  // State pour gérer les images multiples
  const [productImages, setProductImages] = useState<string[]>([]);

  // Create/Edit product form
  const productForm = useForm<{
    name: string;
    description: string;
    price: string;
    imageUrl: string;
    imageUrls: string[];
    hasFreeshipping: boolean;
    features: string;
    specifications: string;
  }>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      price: '',
      imageUrl: '',
      imageUrls: [],
      hasFreeshipping: true,
      features: '',
      specifications: '',
    },
  });

  // Create product mutation
  const createProductMutation = useMutation({
    mutationFn: async (data: z.infer<typeof productSchema>) => {
      return apiRequest('POST', '/api/products', data);
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Produit créé avec succès',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      setCreateDialogOpen(false);
      productForm.reset();
    },
    onError: (error) => {
      toast({
        title: 'Erreur',
        description: 'Échec de la création du produit',
        variant: 'destructive',
      });
    },
  });

  // Update product mutation
  const updateProductMutation = useMutation({
    mutationFn: async (data: z.infer<typeof productSchema>) => {
      if (!selectedProduct) return null;
      
      return apiRequest('PATCH', `/api/products/${selectedProduct.id}`, data);
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Produit mis à jour avec succès',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      setEditDialogOpen(false);
      productForm.reset();
      setSelectedProduct(null);
    },
    onError: (error) => {
      toast({
        title: 'Erreur',
        description: 'Échec de la mise à jour du produit',
        variant: 'destructive',
      });
    },
  });

  // Delete product mutation
  const deleteProductMutation = useMutation({
    mutationFn: async () => {
      if (!selectedProduct) return null;
      
      return apiRequest('DELETE', `/api/products/${selectedProduct.id}`);
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Produit supprimé avec succès',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      setDeleteDialogOpen(false);
      setSelectedProduct(null);
    },
    onError: (error) => {
      toast({
        title: 'Erreur',
        description: 'Échec de la suppression du produit',
        variant: 'destructive',
      });
    },
  });

  // Fonction pour traiter les données du formulaire avant la soumission
  const processFormData = (formData: any) => {
    // S'assurer que imageUrls est un tableau
    const processedData = {
      ...formData,
      imageUrls: productImages,
    };
    
    return processedData as z.infer<typeof productSchema>;
  };

  const handleCreateSubmit = (formData: any) => {
    const data = processFormData(formData);
    createProductMutation.mutate(data);
  };

  const handleEditSubmit = (formData: any) => {
    const data = processFormData(formData);
    updateProductMutation.mutate(data);
  };

  const handleDeleteSubmit = () => {
    deleteProductMutation.mutate();
  };

  const openEditDialog = (product: Product) => {
    setSelectedProduct(product);
    
    // Prepare features and specifications for the form
    const featuresText = Array.isArray(product.features) 
      ? product.features.join('\n') 
      : '';
      
    const specificationsText = Object.entries(product.specifications || {})
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
    
    // Initialiser les images additionnelles
    if (product.imageUrls && Array.isArray(product.imageUrls)) {
      setProductImages(product.imageUrls.slice(0, 3));
    }
    
    productForm.reset({
      name: product.name,
      description: product.description,
      price: product.price,
      imageUrl: product.imageUrl,
      imageUrls: [], // Géré par l'état productImages
      hasFreeshipping: product.hasFreeshipping,
      features: featuresText,
      specifications: specificationsText,
    });
    
    setEditDialogOpen(true);
  };

  const openDeleteDialog = (product: Product) => {
    setSelectedProduct(product);
    setDeleteDialogOpen(true);
  };

  const navigateTo = (path: string) => {
    setLocation(path);
  };

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-secondary text-white p-6 flex flex-col">
        <div className="mb-8">
          <Logo className="mb-2" />
          <h1 className="text-xl font-bold">Panneau d'Administration</h1>
        </div>
        
        <nav className="flex-grow">
          <ul className="space-y-2">
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin')}
              >
                <RiDashboardLine className="mr-2 h-5 w-5" />
                Tableau de bord
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/orders')}
              >
                <RiBox3Line className="mr-2 h-5 w-5" />
                Commandes
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark bg-secondary-dark"
                onClick={() => navigateTo('/admin/products')}
              >
                <RiShoppingBag3Line className="mr-2 h-5 w-5" />
                Produits
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Notifications
              </Button>
            </li>
          </ul>
        </nav>
        
        <div className="mt-auto">
          <Button 
            variant="ghost" 
            className="w-full justify-start text-white hover:bg-secondary-dark"
            onClick={() => navigateTo('/')}
          >
            <RiLogoutBoxLine className="mr-2 h-5 w-5" />
            Déconnexion
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow bg-gray-50 p-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des Produits</h1>
            <p className="text-gray-500">Ajoutez et gérez les produits à afficher dans l'application</p>
          </div>
          
          <Button 
            onClick={() => {
              productForm.reset({
                name: '',
                description: '',
                price: '',
                imageUrl: '',
                imageUrls: [],
                hasFreeshipping: true,
                features: '',
                specifications: '',
              });
              setProductImages([]);
              setCreateDialogOpen(true);
            }}
            className="bg-primary hover:bg-primary-dark"
          >
            <RiAddLine className="mr-2 h-5 w-5" />
            Ajouter un Produit
          </Button>
        </header>
        
        {/* Products Grid */}
        {isLoading ? (
          <div className="text-center py-10">
            <p>Chargement des produits...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products && products.length > 0 ? (
              products.map(product => (
                <Card key={product.id} className="overflow-hidden">
                  <div className="h-48 overflow-hidden">
                    <img 
                      src={product.imageUrls && product.imageUrls.length > 0 
                        ? product.imageUrls[0] // Utilise la première image du tableau si disponible
                        : product.imageUrl}  // Fallback sur l'image principale
                      alt={product.name}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                  <CardHeader className="p-4">
                    <CardTitle className="text-lg">{product.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-sm text-gray-600 mb-2">{product.description}</p>
                    <p className="font-bold text-primary">{product.price}</p>
                  </CardContent>
                  <CardFooter className="flex justify-between p-4 bg-gray-50">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => openEditDialog(product)}
                    >
                      <RiEdit2Line className="mr-1 h-4 w-4" />
                      Modifier
                    </Button>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => openDeleteDialog(product)}
                    >
                      <RiDeleteBin6Line className="mr-1 h-4 w-4" />
                      Supprimer
                    </Button>
                  </CardFooter>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-10">
                <p className="text-gray-500">Aucun produit trouvé. Ajoutez votre premier produit pour commencer.</p>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Create/Edit Product Dialog */}
      <Dialog 
        open={createDialogOpen || editDialogOpen} 
        onOpenChange={(open) => {
          if (!open) {
            setCreateDialogOpen(false);
            setEditDialogOpen(false);
            setProductImages([]);
          }
        }}
      >
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {createDialogOpen ? 'Ajouter un Produit' : 'Modifier le Produit'}
            </DialogTitle>
            <DialogDescription>
              {createDialogOpen 
                ? 'Ajouter un nouveau produit à afficher dans l\'application' 
                : `Modification du produit: ${selectedProduct?.name}`
              }
            </DialogDescription>
          </DialogHeader>
          
          <Form {...productForm}>
            <form 
              onSubmit={productForm.handleSubmit(
                createDialogOpen ? handleCreateSubmit : handleEditSubmit
              )} 
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={productForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nom du Produit</FormLabel>
                      <FormControl>
                        <Input placeholder="Nom du produit" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={productForm.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prix</FormLabel>
                      <FormControl>
                        <Input placeholder="ex: 99,99€" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={productForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Brève description du produit" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={productForm.control}
                name="imageUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>URL de l'image principale (optionnel)</FormLabel>
                    <FormControl>
                      <div className="space-y-2">
                        <Input placeholder="https://example.com/image.jpg" {...field} />
                        <div className="flex flex-col space-y-2">
                          <p className="text-xs text-gray-500">OU télécharger une image locale:</p>
                          <input 
                            type="file"
                            accept="image/*"
                            className="text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark"
                            onChange={(e) => {
                              if (e.target.files && e.target.files[0]) {
                                const file = e.target.files[0];
                                const reader = new FileReader();
                                reader.onloadend = () => {
                                  if (typeof reader.result === 'string') {
                                    field.onChange(reader.result);
                                  }
                                };
                                reader.readAsDataURL(file);
                              }
                            }}
                          />
                        </div>
                        {field.value && field.value.startsWith('data:image') && (
                          <div className="mt-2">
                            <p className="text-xs text-green-600 mb-1">Image principale:</p>
                            <img 
                              src={field.value} 
                              alt="Aperçu de l'image" 
                              className="h-24 object-contain border rounded"
                            />
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Champ pour les images additionnelles (jusqu'à 3) */}
              <div>
                <h3 className="text-sm font-medium mb-2">Images additionnelles du produit (jusqu'à 3)</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  {[0, 1, 2].map((index) => (
                    <div key={index} className="border rounded p-3 flex flex-col items-center">
                      <p className="text-xs text-gray-500 mb-2">Image {index + 1}</p>
                      
                      {productImages[index] ? (
                        <div className="relative w-full">
                          <img 
                            src={productImages[index]} 
                            alt={`Image ${index + 1}`} 
                            className="h-32 w-full object-contain mb-2" 
                          />
                          <Button 
                            type="button"
                            variant="destructive" 
                            size="sm"
                            className="absolute top-0 right-0"
                            onClick={() => {
                              const newImages = [...productImages];
                              newImages.splice(index, 1);
                              setProductImages(newImages);
                            }}
                          >
                            ×
                          </Button>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center gap-2">
                          <input 
                            type="file"
                            accept="image/*"
                            className="text-xs text-gray-500 file:mr-2 file:py-1 file:px-2 file:rounded-full file:border-0 file:text-xs file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark"
                            onChange={(e) => {
                              if (e.target.files && e.target.files[0]) {
                                const file = e.target.files[0];
                                const reader = new FileReader();
                                reader.onloadend = () => {
                                  if (typeof reader.result === 'string') {
                                    // Vérifier si on n'a pas déjà 3 images
                                    if (productImages.length < 3) {
                                      setProductImages([...productImages, reader.result]);
                                    } else {
                                      // Remplacer l'image à la position index
                                      const newImages = [...productImages];
                                      newImages[index] = reader.result;
                                      setProductImages(newImages);
                                    }
                                  }
                                };
                                reader.readAsDataURL(file);
                              }
                            }}
                          />
                          <p className="text-xs text-gray-400">Ou glissez l'image ici</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500">Ces images seront affichées dans le carousel d'images du produit.</p>
              </div>
              
              <FormField
                control={productForm.control}
                name="hasFreeshipping"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Livraison Gratuite</FormLabel>
                      <p className="text-sm text-gray-500">
                        Offrir la livraison gratuite pour ce produit
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={productForm.control}
                  name="features"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Caractéristiques</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Une caractéristique par ligne" 
                          className="h-32"
                          {...field} 
                        />
                      </FormControl>
                      <p className="text-xs text-gray-500">Saisissez une caractéristique par ligne</p>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={productForm.control}
                  name="specifications"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Spécifications</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Écran: 6.1 pouces&#10;Batterie: 3000mAh" 
                          className="h-32"
                          {...field} 
                        />
                      </FormControl>
                      <p className="text-xs text-gray-500">Format: Clé: Valeur (une par ligne)</p>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={() => {
                    setCreateDialogOpen(false);
                    setEditDialogOpen(false);
                  }}
                >
                  Annuler
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={createProductMutation.isPending || updateProductMutation.isPending}
                >
                  {(createProductMutation.isPending || updateProductMutation.isPending)
                    ? (createDialogOpen ? 'Ajout...' : 'Mise à jour...')
                    : (createDialogOpen ? 'Ajouter Produit' : 'Mettre à jour')
                  }
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Supprimer le Produit</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer "{selectedProduct?.name}" ? Cette action ne peut pas être annulée.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)}
            >
              Annuler
            </Button>
            <Button 
              variant="destructive"
              onClick={handleDeleteSubmit}
              disabled={deleteProductMutation.isPending}
            >
              {deleteProductMutation.isPending ? 'Suppression...' : 'Supprimer'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductManagement;
