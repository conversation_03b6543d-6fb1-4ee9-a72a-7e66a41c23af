import React, { useState, useEffect } from 'react';
import { He<PERSON><PERSON> } from 'react-helmet';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Save } from 'lucide-react';
import { RiInformationLine, RiCheckLine } from 'react-icons/ri';

const WarehouseSettingsPage: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [warehouseAddress, setWarehouseAddress] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [currentAddress, setCurrentAddress] = useState<string>('');
  const [previewMode, setPreviewMode] = useState<boolean>(false);

  useEffect(() => {
    const fetchWarehouseAddress = async () => {
      try {
        const response = await fetch('/api/app-settings/warehouse_address');
        if (response.ok) {
          const data = await response.json();
          setWarehouseAddress(data.value);
          setCurrentAddress(data.value);
        } else {
          setWarehouseAddress('');
          setCurrentAddress('');
        }
      } catch (error) {
        console.error('Erreur lors de la récupération de l\'adresse de l\'entrepôt:', error);
        setWarehouseAddress('');
        setCurrentAddress('');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWarehouseAddress();
  }, []);

  const handleSaveAddress = async () => {
    setIsSaving(true);

    try {
      const response = await fetch('/api/app-settings/warehouse_address', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: warehouseAddress,
          description: 'Adresse de l\'entrepôt en Chine pour les marques d\'expédition',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentAddress(data.value);
        toast({
          title: 'Adresse mise à jour',
          description: 'L\'adresse de l\'entrepôt a été mise à jour avec succès.',
          variant: 'default',
          className: 'bg-green-100 border-green-400 text-green-900',
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la mise à jour de l\'adresse');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'adresse:', error);
      toast({
        title: 'Erreur',
        description: error instanceof Error ? error.message : 'Une erreur est survenue lors de la mise à jour de l\'adresse',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const isAdmin = user?.username === 'admin';

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-red-600">Accès refusé</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p>Vous n'avez pas les droits d'accès à cette page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Gestion de l'Adresse d'Entrepôt | Admin Waabo</title>
      </Helmet>

      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-semibold text-gray-900 mb-6">Paramètres de l'Adresse d'Entrepôt</h1>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="w-8 h-8 text-[#004d25] animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Adresse de l'Entrepôt en Chine</CardTitle>
                  <CardDescription>
                    Cette adresse apparaîtra sur les marques d'expédition des utilisateurs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center mb-4">
                      <Button
                        type="button"
                        variant={previewMode ? "outline" : "default"}
                        size="sm"
                        onClick={() => setPreviewMode(false)}
                        className={!previewMode ? "bg-[#004d25]" : ""}
                      >
                        Modifier
                      </Button>
                      <div className="w-px h-4 bg-gray-300 mx-2"></div>
                      <Button
                        type="button"
                        variant={previewMode ? "default" : "outline"}
                        size="sm"
                        onClick={() => setPreviewMode(true)}
                        className={previewMode ? "bg-[#004d25]" : ""}
                      >
                        Aperçu
                      </Button>
                    </div>

                    {previewMode ? (
                      <div className="bg-white border rounded-md p-4 whitespace-pre-line min-h-[200px]">
                        {warehouseAddress || <span className="text-gray-400">Pas d'adresse configurée</span>}
                      </div>
                    ) : (
                      <Textarea
                        value={warehouseAddress}
                        onChange={(e) => setWarehouseAddress(e.target.value)}
                        placeholder="Entrez l'adresse complète de l'entrepôt en Chine..."
                        className="min-h-[200px]"
                      />
                    )}

                    <div className="flex items-start gap-2 text-amber-600 bg-amber-50 p-3 rounded-md">
                      <RiInformationLine className="h-5 w-5 flex-shrink-0 mt-0.5" />
                      <p className="text-sm">
                        Assurez-vous d'inclure le nom de l'entrepôt, l'adresse complète, la ville, la province, 
                        le code postal et le pays (Chine). Vous pouvez également ajouter des informations de contact.
                      </p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div>
                    {currentAddress && (
                      <p className="text-xs text-gray-500 flex items-center gap-1">
                        <RiCheckLine className="text-green-500" /> 
                        Dernière mise à jour: {new Date().toLocaleDateString('fr-FR')}
                      </p>
                    )}
                  </div>
                  <Button
                    onClick={handleSaveAddress}
                    disabled={isSaving || warehouseAddress === currentAddress}
                    className="bg-[#004d25] hover:bg-[#003d1e]"
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Enregistrement...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Enregistrer
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Aperçu de la Marque d'Expédition</CardTitle>
                    <CardDescription>
                      Voici comment l'adresse apparaîtra sur les marques d'expédition
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md p-4">
                      <div className="border-b pb-2 mb-2">
                        <h3 className="text-sm font-bold text-gray-700 uppercase">Adresse d'Entrepôt en Chine:</h3>
                      </div>
                      <div className="bg-gray-50 p-3 rounded border border-gray-200">
                        <p className="text-sm whitespace-pre-line">{warehouseAddress || "Pas d'adresse configurée"}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Guide de Format</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 text-sm">
                      <p className="font-medium">Format recommandé:</p>
                      <div className="bg-gray-50 p-3 rounded border border-gray-200 whitespace-pre-line text-xs text-gray-600">
{`Waabo Express Entrepôt Chine
Nom du contact: [Nom]
Téléphone: [Numéro]
[Ligne d'adresse 1]
[Ligne d'adresse 2]
[Ville], [Province] [Code postal]
Chine`}</div>
                      <p className="mt-3 text-gray-600">
                        L'adresse doit être claire et complète pour faciliter la livraison des colis à l'entrepôt.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default WarehouseSettingsPage;