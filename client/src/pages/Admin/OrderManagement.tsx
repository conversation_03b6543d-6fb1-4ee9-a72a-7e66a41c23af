import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import Logo from '@/components/Logo';
import { ProgressSteps } from '@/components/ui/progress-steps';
import { Order, TrackingStep } from '@/lib/types';
import { RiDashboardLine, RiShoppingBag3Line, RiBox3Line, RiBellLine, RiLogoutBoxLine, RiAddLine } from 'react-icons/ri';
import { apiRequest } from '@/lib/queryClient';

const orderStatusOptions = [
  { value: 'ORDER_CONFIRMED', label: 'Commande Confirmée' },
  { value: 'PROCESSING', label: 'En Traitement' },
  { value: 'IN_TRANSIT', label: 'En Transit' },
  { value: 'ARRIVED', label: 'Arrivée au Burkina' },
  { value: 'DELIVERED', label: 'Livrée' }
];

const orderStatusToSteps = (status: string): TrackingStep[] => {
  const allSteps: TrackingStep[] = [
    { number: 1, label: 'Commande Confirmée', status: 'pending' },
    { number: 2, label: 'En Traitement', status: 'pending' },
    { number: 3, label: 'En Transit', status: 'pending' },
    { number: 4, label: 'Arrivée au Burkina', status: 'pending' },
    { number: 5, label: 'Livrée', status: 'pending' },
  ];

  const statusIndex = {
    'ORDER_CONFIRMED': 0,
    'PROCESSING': 1,
    'IN_TRANSIT': 2,
    'ARRIVED': 3,
    'DELIVERED': 4,
  };

  const currentIndex = statusIndex[status as keyof typeof statusIndex] || 0;

  return allSteps.map((step, index) => ({
    ...step,
    status: index < currentIndex 
      ? 'completed' 
      : index === currentIndex 
      ? 'active' 
      : 'pending'
  }));
};

const OrderManagement: React.FC = () => {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  // Fetch orders
  const { data: orders, isLoading, refetch: refetchOrders } = useQuery<Order[]>({
    queryKey: ['/api/orders'],
    queryFn: async () => {
      console.log('Fetching orders...');
      const response = await apiRequest('GET', '/api/orders');
      console.log('Orders response:', response);
      // Ensure we always return an array, even if the API returns something else
      return Array.isArray(response) ? response : [];
    }
  });

  // Create order form
  const createOrderSchema = z.object({
    reference: z.string().min(4, 'Le code de référence doit contenir au moins 4 caractères'),
    estimatedDelivery: z.string().optional(),
    userId: z.string().optional(),
  });

  const createOrderForm = useForm<z.infer<typeof createOrderSchema>>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      reference: '',
      estimatedDelivery: '',
      userId: '',
    },
  });

  // Update order form
  const updateOrderSchema = z.object({
    status: z.string(),
    latestUpdate: z.string().optional(),
  });

  const updateOrderForm = useForm<z.infer<typeof updateOrderSchema>>({
    resolver: zodResolver(updateOrderSchema),
    defaultValues: {
      status: '',
      latestUpdate: '',
    },
  });

  // Create order mutation
  const createOrderMutation = useMutation({
    mutationFn: async (data: z.infer<typeof createOrderSchema>) => {
      const payload = {
        reference: data.reference,
        status: 'ORDER_CONFIRMED',
        estimatedDelivery: data.estimatedDelivery || undefined,
        userId: data.userId ? parseInt(data.userId) : undefined,
      };
      
      console.log('Creating order with payload:', payload);
      const result = await apiRequest('POST', '/api/orders', payload);
      console.log('Create order result:', result);
      return result;
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Commande créée avec succès',
      });
      // Forcer l'actualisation des commandes
      refetchOrders();
      setCreateDialogOpen(false);
      createOrderForm.reset();
    },
    onError: (error) => {
      console.error('Error creating order:', error);
      toast({
        title: 'Erreur',
        description: 'Échec de la création de la commande',
        variant: 'destructive',
      });
    },
  });

  // Update order mutation
  const updateOrderMutation = useMutation({
    mutationFn: async (data: z.infer<typeof updateOrderSchema>) => {
      if (!selectedOrder) return null;
      
      console.log('Updating order with ID:', selectedOrder.id, 'data:', data);
      const result = await apiRequest('PATCH', `/api/orders/${selectedOrder.id}`, {
        status: data.status,
        latestUpdate: data.latestUpdate,
      });
      console.log('Update order result:', result);
      return result;
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Commande mise à jour avec succès',
      });
      // Forcer l'actualisation des commandes
      refetchOrders();
      setUpdateDialogOpen(false);
      updateOrderForm.reset();
      setSelectedOrder(null);
    },
    onError: (error) => {
      console.error('Error updating order:', error);
      toast({
        title: 'Erreur',
        description: 'Échec de la mise à jour de la commande',
        variant: 'destructive',
      });
    },
  });

  const handleCreateSubmit = (data: z.infer<typeof createOrderSchema>) => {
    createOrderMutation.mutate(data);
  };

  const handleUpdateSubmit = (data: z.infer<typeof updateOrderSchema>) => {
    updateOrderMutation.mutate(data);
  };

  const openUpdateDialog = (order: Order) => {
    setSelectedOrder(order);
    updateOrderForm.reset({
      status: order.status,
      latestUpdate: order.latestUpdate || '',
    });
    setUpdateDialogOpen(true);
  };

  const navigateTo = (path: string) => {
    setLocation(path);
  };

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-secondary text-white p-6 flex flex-col">
        <div className="mb-8">
          <Logo className="mb-2" />
          <h1 className="text-xl font-bold">Panneau d'Administration</h1>
        </div>
        
        <nav className="flex-grow">
          <ul className="space-y-2">
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin')}
              >
                <RiDashboardLine className="mr-2 h-5 w-5" />
                Tableau de bord
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark bg-secondary-dark"
                onClick={() => navigateTo('/admin/orders')}
              >
                <RiBox3Line className="mr-2 h-5 w-5" />
                Commandes
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/products')}
              >
                <RiShoppingBag3Line className="mr-2 h-5 w-5" />
                Produits
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Notifications
              </Button>
            </li>
          </ul>
        </nav>
        
        <div className="mt-auto">
          <Button 
            variant="ghost" 
            className="w-full justify-start text-white hover:bg-secondary-dark"
            onClick={() => navigateTo('/')}
          >
            <RiLogoutBoxLine className="mr-2 h-5 w-5" />
            Déconnexion
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow bg-gray-50 p-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des Commandes</h1>
            <p className="text-gray-500">Créer et gérer les codes de suivi</p>
          </div>
          
          <Button 
            onClick={() => setCreateDialogOpen(true)}
            className="bg-primary hover:bg-primary-dark"
          >
            <RiAddLine className="mr-2 h-5 w-5" />
            Nouvelle Commande
          </Button>
        </header>
        
        {/* Orders List */}
        <div className="space-y-4">
          {isLoading ? (
            <p>Chargement des commandes...</p>
          ) : orders && orders.length > 0 ? (
            orders.map(order => (
              <Card key={order.id} className="overflow-hidden">
                <CardHeader className="p-4 bg-gray-50">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Commande #{order.reference}</CardTitle>
                    <Button onClick={() => openUpdateDialog(order)}>
                      Mettre à jour le statut
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="flex justify-between mb-4">
                    <div>
                      <p className="text-sm text-gray-500">Statut</p>
                      <p className="font-medium">{order.status.replace('_', ' ')}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Créé le</p>
                      <p className="font-medium">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Livraison estimée</p>
                      <p className="font-medium">
                        {order.estimatedDelivery ? new Date(order.estimatedDelivery).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                  </div>
                  
                  <ProgressSteps steps={orderStatusToSteps(order.status)} />
                  
                  {order.latestUpdate && (
                    <div className="mt-4 bg-gray-50 p-3 rounded">
                      <p className="text-sm font-medium">Dernière mise à jour</p>
                      <p className="text-sm text-gray-600">{order.latestUpdate}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">Aucune commande trouvée. Créez une nouvelle commande pour commencer.</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      
      {/* Create Order Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Créer une nouvelle commande</DialogTitle>
            <DialogDescription>
              Créer une nouvelle référence de suivi pour les clients
            </DialogDescription>
          </DialogHeader>
          
          <Form {...createOrderForm}>
            <form onSubmit={createOrderForm.handleSubmit(handleCreateSubmit)} className="space-y-4">
              <FormField
                control={createOrderForm.control}
                name="reference"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Code de référence</FormLabel>
                    <FormControl>
                      <Input placeholder="ex. CBF2023451" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={createOrderForm.control}
                name="estimatedDelivery"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de livraison estimée</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={createOrderForm.control}
                name="userId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ID Client (Optionnel)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="ID Client" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={() => setCreateDialogOpen(false)}
                >
                  Annuler
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={createOrderMutation.isPending}
                >
                  {createOrderMutation.isPending ? 'Création...' : 'Créer la commande'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Update Order Dialog */}
      <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mettre à jour le statut de la commande</DialogTitle>
            <DialogDescription>
              Mettre à jour le statut et ajouter des détails pour la commande #{selectedOrder?.reference}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...updateOrderForm}>
            <form onSubmit={updateOrderForm.handleSubmit(handleUpdateSubmit)} className="space-y-4">
              <FormField
                control={updateOrderForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Statut de la commande</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner le statut" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {orderStatusOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={updateOrderForm.control}
                name="latestUpdate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dernière mise à jour</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Ajouter des détails sur le statut actuel" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={() => setUpdateDialogOpen(false)}
                >
                  Annuler
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={updateOrderMutation.isPending}
                >
                  {updateOrderMutation.isPending ? 'Mise à jour...' : 'Mettre à jour'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderManagement;
