import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useWatch } from "react-hook-form";
import { PopupNotification } from "@/lib/types";
import Logo from "@/components/Logo";
import { useToast } from "@/hooks/use-toast";
import { usePopupNotifications } from "@/hooks/use-popup-notifications";
import { format } from "date-fns";
import * as z from "zod";
import { ImageUploader } from "@/components/ui/image-uploader";
import { PopupPreview } from "@/components/ui/popup-preview";
import { PopupNotificationPreviewTab } from "@/components/ui/popup-notification-preview-tab";

import {
  RiDashboard<PERSON>ine,
  RiB<PERSON>3<PERSON><PERSON>,
  RiShoppingBag3Line,
  RiUserLine,
  RiBellLine,
  RiFileTextLine,
  RiLogoutBoxLine,
  RiAddLine,
  RiDeleteBin6Line,
  RiEdit2Line,
  RiCloseLine,
  RiEyeLine,
} from "react-icons/ri";

import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";

const formSchema = z.object({
  title: z.string().min(1, "Le titre est requis"),
  content: z.string().min(1, "Le contenu est requis"),
  // Support pour les images téléchargées (Base64 ou URL)
  imageUrl: z.string().optional(),
  backgroundColor: z.string().default("#ffffff"),
  textColor: z.string().default("#000000"),
  buttonColor: z.string().default("#3b82f6"),
  buttonTextColor: z.string().default("#ffffff"),
  showOnce: z.boolean().default(true),
  isActive: z.boolean().default(true),
  // Champs pour permettre une fenêtre de temps
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  buttonText: z.string().default("OK"),
  buttonLink: z.string().optional(),
  // Personnalisation de la typographie
  titleFontSize: z.string().default("24px"),
  contentFontSize: z.string().default("16px"),
  fontFamily: z.string().default("Inter, sans-serif"),
  fontWeight: z.string().default("normal"),
  // Personnalisation de l'apparence
  borderRadius: z.string().default("8px"),
  // Dimensions avec conversion des unités (px) en nombres entiers
  width: z.union([
    z.string().transform(val => {
      // Convertir les valeurs comme "500px" en nombre 500
      const match = val.match(/^(\d+)px$/);
      if (match) return parseInt(match[1], 10);
      return parseInt(val, 10) || 500; // Valeur par défaut si la conversion échoue
    }),
    z.number()
  ]).default(500),
  height: z.union([
    z.string().transform(val => {
      const match = val.match(/^(\d+)px$/);
      if (match) return parseInt(match[1], 10);
      return parseInt(val, 10) || 300; // Valeur par défaut si la conversion échoue
    }), 
    z.number()
  ]).default(300),
  // Position de l'image
  imagePosition: z.enum(["top", "bottom", "left", "right"]).default("top"),
});

export default function PopupNotificationManagement() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const { showPopup, closeAllPopups } = usePopupNotifications();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedPopup, setSelectedPopup] = useState<PopupNotification | null>(null);

  // Chargement de toutes les notifications popup
  const { data: popups = [] as PopupNotification[], isLoading, isError } = useQuery<PopupNotification[]>({
    queryKey: ['/api/popup-notifications'],
    staleTime: 60000,
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      content: "",
      imageUrl: "",
      backgroundColor: "#ffffff",
      textColor: "#000000",
      buttonColor: "#3b82f6",
      buttonTextColor: "#ffffff",
      showOnce: true,
      isActive: true,
      buttonText: "OK",
      buttonLink: "",
      titleFontSize: "24px",
      contentFontSize: "16px",
      borderRadius: "8px",
      width: 500,
    },
  });

  // Mutation pour créer une notification popup
  const createMutation = useMutation({
    mutationFn: (data: any) => apiRequest('POST', '/api/popup-notifications', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/popup-notifications'] });
      toast({
        title: "Succès!",
        description: "Notification popup créée avec succès.",
        variant: "default",
      });
      form.reset();
      setIsCreateModalOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Erreur",
        description: `Échec de la création de la notification popup: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Mutation pour mettre à jour une notification popup
  const updateMutation = useMutation({
    mutationFn: (data: any) => apiRequest('PATCH', `/api/popup-notifications/${data.id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/popup-notifications'] });
      toast({
        title: "Succès!",
        description: "Notification popup mise à jour avec succès.",
        variant: "default",
      });
      setIsEditModalOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Erreur",
        description: `Échec de la mise à jour de la notification popup: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Mutation pour supprimer une notification popup
  const deleteMutation = useMutation({
    mutationFn: (id: number) => apiRequest('DELETE', `/api/popup-notifications/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/popup-notifications'] });
      toast({
        title: "Succès!",
        description: "Notification popup supprimée avec succès.",
        variant: "default",
      });
      setIsDeleteModalOpen(false);
      setSelectedPopup(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Erreur",
        description: `Échec de la suppression de la notification popup: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const loadPopupDataToForm = (popup: PopupNotification) => {
    form.reset({
      title: popup.title || "",
      content: popup.content || "",
      imageUrl: popup.imageUrl || "",
      backgroundColor: popup.backgroundColor || "#ffffff",
      textColor: popup.textColor || "#000000",
      buttonColor: popup.buttonColor || "#3b82f6",
      buttonTextColor: popup.buttonTextColor || "#ffffff",
      showOnce: popup.showOnce || true,
      isActive: popup.isActive || true,
      startDate: popup.startDate ? format(new Date(popup.startDate), "yyyy-MM-dd") : "",
      endDate: popup.endDate ? format(new Date(popup.endDate), "yyyy-MM-dd") : "",
      buttonText: popup.buttonText || "OK",
      buttonLink: popup.buttonLink || "",
      titleFontSize: popup.titleFontSize || "24px",
      contentFontSize: popup.contentFontSize || "16px",
      borderRadius: popup.borderRadius || "8px",
      width: popup.width || 500,
    });
  };

  const handleSubmit = (data: z.infer<typeof formSchema>) => {
    createMutation.mutate(data);
  };

  const handleEdit = (popup: PopupNotification) => {
    setSelectedPopup(popup);
    loadPopupDataToForm(popup);
    setIsEditModalOpen(true);
  };

  const handleEditSubmit = (data: z.infer<typeof formSchema>) => {
    if (selectedPopup) {
      updateMutation.mutate({
        id: selectedPopup.id,
        ...data,
      });
    }
  };

  const handleDelete = (popup: PopupNotification) => {
    setSelectedPopup(popup);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = () => {
    if (selectedPopup) {
      deleteMutation.mutate(selectedPopup.id);
    }
  };

  const handlePreview = (popup: PopupNotification) => {
    // Afficher la popup en utilisant le hook usePopupNotifications
    showPopup(popup);
  };

  const navigateTo = (path: string) => {
    setLocation(path);
  };

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-secondary text-white p-6 flex flex-col">
        <div className="mb-8">
          <Logo className="mb-2" />
          <h1 className="text-xl font-bold">Panneau d'Administration</h1>
        </div>
        
        <nav className="flex-grow">
          <ul className="space-y-2">
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin')}
              >
                <RiDashboardLine className="mr-2 h-5 w-5" />
                Tableau de bord
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/orders')}
              >
                <RiBox3Line className="mr-2 h-5 w-5" />
                Commandes
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/products')}
              >
                <RiShoppingBag3Line className="mr-2 h-5 w-5" />
                Produits
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/users')}
              >
                <RiUserLine className="mr-2 h-5 w-5" />
                Utilisateurs
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Notifications
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark bg-secondary-dark"
                onClick={() => navigateTo('/admin/popup-notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Popups
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/static-pages')}
              >
                <RiFileTextLine className="mr-2 h-5 w-5" />
                Pages Statiques
              </Button>
            </li>
          </ul>
        </nav>
        
        <div className="mt-auto">
          <Button 
            variant="ghost" 
            className="w-full justify-start text-white hover:bg-secondary-dark"
            onClick={() => navigateTo('/')}
          >
            <RiLogoutBoxLine className="mr-2 h-5 w-5" />
            Déconnexion
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow bg-gray-50 p-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des Notifications Popup</h1>
            <p className="text-gray-500">Créer, modifier et supprimer les notifications popup</p>
          </div>
          <Button 
            onClick={() => {
              form.reset();
              setIsCreateModalOpen(true);
            }}
          >
            <RiAddLine className="mr-2" />
            Nouvelle Popup
          </Button>
        </header>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <p>Chargement des notifications popup...</p>
          </div>
        ) : isError ? (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>
              Une erreur est survenue lors du chargement des notifications popup. Veuillez réessayer.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popups && popups.length > 0 ? (
              popups.map((popup: PopupNotification) => (
                <Card key={popup.id} className={`overflow-hidden ${!popup.isActive ? 'border-gray-300 opacity-70' : 'border-primary'}`}>
                  <CardHeader className="p-4 bg-gray-50">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg break-words">{popup.title}</CardTitle>
                      <div className="flex space-x-1">
                        {popup.isActive ? (
                          <Badge variant="default">Actif</Badge>
                        ) : (
                          <Badge variant="outline">Inactif</Badge>
                        )}
                      </div>
                    </div>
                    {popup.startDate && popup.endDate && (
                      <CardDescription className="text-xs">
                        Du {new Date(popup.startDate).toLocaleDateString()} au {new Date(popup.endDate).toLocaleDateString()}
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="mb-3 max-h-24 overflow-hidden">
                      <p className="text-sm text-gray-600 whitespace-pre-line line-clamp-4">{popup.content}</p>
                    </div>
                    {popup.imageUrl && (
                      <div className="mb-3 h-32 overflow-hidden rounded">
                        <img 
                          src={popup.imageUrl} 
                          alt={popup.title} 
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = 'https://placehold.co/600x400?text=Image+non+disponible';
                          }}
                        />
                      </div>
                    )}
                    <div className="grid grid-cols-2 gap-2 text-xs mt-3">
                      <div className="flex items-center">
                        <span className="mr-2">Afficher une fois:</span>
                        <span>{popup.showOnce ? 'Oui' : 'Non'}</span>
                      </div>
                      <div className="flex items-center">
                        <div 
                          className="w-4 h-4 rounded-full mr-2" 
                          style={{ backgroundColor: popup.backgroundColor }}
                        ></div>
                        <span>Fond</span>
                      </div>
                      <div className="flex items-center">
                        <div 
                          className="w-4 h-4 rounded-full mr-2" 
                          style={{ backgroundColor: popup.textColor }}
                        ></div>
                        <span>Texte</span>
                      </div>
                      <div className="flex items-center">
                        <div 
                          className="w-4 h-4 rounded-full mr-2" 
                          style={{ backgroundColor: popup.buttonColor }}
                        ></div>
                        <span>Bouton</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-between">
                    <div className="flex space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handlePreview(popup)}
                      >
                        <RiEyeLine className="mr-1" />
                        Aperçu
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleEdit(popup)}
                      >
                        <RiEdit2Line className="mr-1" />
                        Modifier
                      </Button>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      onClick={() => handleDelete(popup)}
                    >
                      <RiDeleteBin6Line className="mr-1" />
                      Supprimer
                    </Button>
                  </CardFooter>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center p-12 bg-white rounded-lg border border-dashed border-gray-300">
                <RiBellLine className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-1">Aucune notification popup</h3>
                <p className="text-gray-500 mb-4">Vous n'avez pas encore créé de notification popup.</p>
                <Button onClick={() => {
                  form.reset();
                  setIsCreateModalOpen(true);
                }}>
                  <RiAddLine className="mr-2" />
                  Créer une notification popup
                </Button>
              </div>
            )}
          </div>
        )}
        
        {/* Create Dialog */}
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Créer une Notification Popup</DialogTitle>
              <DialogDescription>
                Remplissez le formulaire ci-dessous pour créer une notification popup qui s'affichera aux utilisateurs.
              </DialogDescription>
            </DialogHeader>
            
            <div className="overflow-y-auto pr-6" style={{ maxHeight: 'calc(90vh - 200px)' }}>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                  <Tabs defaultValue="content" className="w-full">
                    <TabsList className="w-full mb-4">
                      <TabsTrigger value="content" className="flex-1">Contenu</TabsTrigger>
                      <TabsTrigger value="appearance" className="flex-1">Apparence</TabsTrigger>
                      <TabsTrigger value="settings" className="flex-1">Paramètres</TabsTrigger>
                      <TabsTrigger value="preview" className="flex-1">Prévisualisation</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="content" className="space-y-4">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Titre</FormLabel>
                            <FormControl>
                              <Input placeholder="Entrez le titre de la notification" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="content"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contenu</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Entrez le contenu de la notification"
                                className="min-h-32"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <ImageUploader />
                      
                      <FormField
                        control={form.control}
                        name="buttonText"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Texte du bouton</FormLabel>
                            <FormControl>
                              <Input placeholder="OK" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="buttonLink"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Lien du bouton (optionnel)</FormLabel>
                            <FormControl>
                              <Input placeholder="https://exemple.com/page" {...field} />
                            </FormControl>
                            <FormDescription>
                              Si renseigné, le bouton redirigera vers ce lien
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TabsContent>
                    
                    <TabsContent value="appearance" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="backgroundColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur de fond</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="textColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur du texte</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="buttonColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur du bouton</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="buttonTextColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur du texte du bouton</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="titleFontSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Taille du titre</FormLabel>
                              <Select 
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Sélectionnez une taille" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="16px">Petit (16px)</SelectItem>
                                  <SelectItem value="20px">Moyen (20px)</SelectItem>
                                  <SelectItem value="24px">Grand (24px)</SelectItem>
                                  <SelectItem value="28px">Très grand (28px)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="contentFontSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Taille du contenu</FormLabel>
                              <Select 
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Sélectionnez une taille" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="12px">Petit (12px)</SelectItem>
                                  <SelectItem value="14px">Moyen (14px)</SelectItem>
                                  <SelectItem value="16px">Grand (16px)</SelectItem>
                                  <SelectItem value="18px">Très grand (18px)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="borderRadius"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bordure arrondie</FormLabel>
                              <Select 
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Sélectionnez un rayon" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="0px">Aucun (0px)</SelectItem>
                                  <SelectItem value="4px">Léger (4px)</SelectItem>
                                  <SelectItem value="8px">Moyen (8px)</SelectItem>
                                  <SelectItem value="12px">Grand (12px)</SelectItem>
                                  <SelectItem value="16px">Très grand (16px)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="width"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Largeur (px)</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  min="200"
                                  max="1000"
                                  step="10"
                                  {...field} 
                                  onChange={(e) => field.onChange(parseInt(e.target.value, 10))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="settings" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="showOnce"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between p-4 border rounded-lg">
                              <div className="space-y-0.5">
                                <FormLabel>Afficher une seule fois</FormLabel>
                                <FormDescription>
                                  Si activé, la notification ne sera montrée qu'une seule fois à chaque utilisateur
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="isActive"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between p-4 border rounded-lg">
                              <div className="space-y-0.5">
                                <FormLabel>Notification active</FormLabel>
                                <FormDescription>
                                  Si désactivé, la notification ne sera pas affichée aux utilisateurs
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <Separator />
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="startDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Date de début (optionnel)</FormLabel>
                              <FormControl>
                                <Input type="date" {...field} />
                              </FormControl>
                              <FormDescription>
                                La date à partir de laquelle la notification sera visible
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="endDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Date de fin (optionnel)</FormLabel>
                              <FormControl>
                                <Input type="date" {...field} />
                              </FormControl>
                              <FormDescription>
                                La date jusqu'à laquelle la notification sera visible
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="preview" className="space-y-4">
                      <PopupNotificationPreviewTab />
                    </TabsContent>
                  </Tabs>
                </form>
              </Form>
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsCreateModalOpen(false)}
              >
                Annuler
              </Button>
              <Button 
                type="submit" 
                onClick={form.handleSubmit(handleSubmit)}
                disabled={createMutation.isPending}
              >
                {createMutation.isPending ? 'Création...' : 'Créer'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Edit Dialog */}
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Modifier la Notification Popup</DialogTitle>
              <DialogDescription>
                Modifiez les champs ci-dessous pour mettre à jour cette notification popup.
              </DialogDescription>
            </DialogHeader>
            
            <div className="overflow-y-auto pr-6" style={{ maxHeight: 'calc(90vh - 200px)' }}>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleEditSubmit)} className="space-y-4">
                  <Tabs defaultValue="content" className="w-full">
                    <TabsList className="w-full mb-4">
                      <TabsTrigger value="content" className="flex-1">Contenu</TabsTrigger>
                      <TabsTrigger value="appearance" className="flex-1">Apparence</TabsTrigger>
                      <TabsTrigger value="settings" className="flex-1">Paramètres</TabsTrigger>
                      <TabsTrigger value="preview" className="flex-1">Prévisualisation</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="content" className="space-y-4">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Titre</FormLabel>
                            <FormControl>
                              <Input placeholder="Entrez le titre de la notification" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="content"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contenu</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Entrez le contenu de la notification"
                                className="min-h-32"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <ImageUploader />
                      
                      <FormField
                        control={form.control}
                        name="buttonText"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Texte du bouton</FormLabel>
                            <FormControl>
                              <Input placeholder="OK" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="buttonLink"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Lien du bouton (optionnel)</FormLabel>
                            <FormControl>
                              <Input placeholder="https://exemple.com/page" {...field} />
                            </FormControl>
                            <FormDescription>
                              Si renseigné, le bouton redirigera vers ce lien
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TabsContent>
                    
                    <TabsContent value="appearance" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="backgroundColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur de fond</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="textColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur du texte</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="buttonColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur du bouton</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="buttonTextColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Couleur du texte du bouton</FormLabel>
                              <div className="flex">
                                <FormControl>
                                  <Input type="color" {...field} className="w-16 h-10 p-0" />
                                </FormControl>
                                <Input 
                                  className="ml-2 flex-grow" 
                                  value={field.value}
                                  onChange={(e) => field.onChange(e.target.value)}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="titleFontSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Taille du titre</FormLabel>
                              <Select 
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Sélectionnez une taille" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="16px">Petit (16px)</SelectItem>
                                  <SelectItem value="20px">Moyen (20px)</SelectItem>
                                  <SelectItem value="24px">Grand (24px)</SelectItem>
                                  <SelectItem value="28px">Très grand (28px)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="contentFontSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Taille du contenu</FormLabel>
                              <Select 
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Sélectionnez une taille" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="12px">Petit (12px)</SelectItem>
                                  <SelectItem value="14px">Moyen (14px)</SelectItem>
                                  <SelectItem value="16px">Grand (16px)</SelectItem>
                                  <SelectItem value="18px">Très grand (18px)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="borderRadius"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bordure arrondie</FormLabel>
                              <Select 
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Sélectionnez un rayon" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="0px">Aucun (0px)</SelectItem>
                                  <SelectItem value="4px">Léger (4px)</SelectItem>
                                  <SelectItem value="8px">Moyen (8px)</SelectItem>
                                  <SelectItem value="12px">Grand (12px)</SelectItem>
                                  <SelectItem value="16px">Très grand (16px)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="width"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Largeur (px)</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number" 
                                  min="200"
                                  max="1000"
                                  step="10"
                                  {...field} 
                                  onChange={(e) => field.onChange(parseInt(e.target.value, 10))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="settings" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="showOnce"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between p-4 border rounded-lg">
                              <div className="space-y-0.5">
                                <FormLabel>Afficher une seule fois</FormLabel>
                                <FormDescription>
                                  Si activé, la notification ne sera montrée qu'une seule fois à chaque utilisateur
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="isActive"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between p-4 border rounded-lg">
                              <div className="space-y-0.5">
                                <FormLabel>Notification active</FormLabel>
                                <FormDescription>
                                  Si désactivé, la notification ne sera pas affichée aux utilisateurs
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <Separator />
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="startDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Date de début (optionnel)</FormLabel>
                              <FormControl>
                                <Input type="date" {...field} />
                              </FormControl>
                              <FormDescription>
                                La date à partir de laquelle la notification sera visible
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="endDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Date de fin (optionnel)</FormLabel>
                              <FormControl>
                                <Input type="date" {...field} />
                              </FormControl>
                              <FormDescription>
                                La date jusqu'à laquelle la notification sera visible
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="preview" className="space-y-4">
                      <PopupNotificationPreviewTab />
                    </TabsContent>
                  </Tabs>
                </form>
              </Form>
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsEditModalOpen(false)}
              >
                Annuler
              </Button>
              <Button 
                type="submit" 
                onClick={form.handleSubmit(handleEditSubmit)}
                disabled={updateMutation.isPending}
              >
                {updateMutation.isPending ? 'Mise à jour...' : 'Mettre à jour'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Delete Dialog */}
        <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Supprimer la Notification Popup</DialogTitle>
              <DialogDescription>
                Êtes-vous sûr de vouloir supprimer cette notification popup? Cette action est irréversible.
              </DialogDescription>
            </DialogHeader>
            
            {selectedPopup && (
              <div className="py-4">
                <p className="font-medium text-gray-700">{selectedPopup.title}</p>
                <p className="text-sm text-gray-500 mt-1 line-clamp-2">{selectedPopup.content}</p>
              </div>
            )}
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsDeleteModalOpen(false)}
              >
                Annuler
              </Button>
              <Button 
                variant="destructive"
                onClick={confirmDelete}
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? 'Suppression...' : 'Supprimer'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}