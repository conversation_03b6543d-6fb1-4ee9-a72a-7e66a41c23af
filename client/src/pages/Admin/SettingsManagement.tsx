import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { ProtectedRoute } from '@/lib/protected-route';
import Layout from '@/components/Layout';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Loader2, Save, Phone } from 'lucide-react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';

// Type de paramètre d'application
interface AppSetting {
  id: number;
  key: string;
  value: string;
  description: string | null;
  updatedAt: string;
}

// Schéma de validation pour la mise à jour du numéro WhatsApp
const whatsappFormSchema = z.object({
  whatsappNumber: z.string()
    .min(9, "Le numéro doit comporter au moins 9 chiffres")
    .regex(/^\+?[0-9]+$/, "Format de numéro invalide. Utilisez uniquement des chiffres et éventuellement le préfixe +")
});

type WhatsappFormValues = z.infer<typeof whatsappFormSchema>;

export default function SettingsManagement() {
  return (
    <ProtectedRoute
      path="/admin/settings"
      component={SettingsManagementContent}
    />
  );
}

function SettingsManagementContent() {
  const { toast } = useToast();
  const [, navigate] = useLocation();

  // Récupération du numéro WhatsApp actuel
  const { data: whatsappSetting, isLoading } = useQuery<any, Error, any>({
    queryKey: ['/api/app-settings/whatsapp_number']
  });

  // Formulaire pour le numéro WhatsApp
  const form = useForm<WhatsappFormValues>({
    resolver: zodResolver(whatsappFormSchema),
    defaultValues: {
      whatsappNumber: ""
    },
    values: {
      whatsappNumber: whatsappSetting && whatsappSetting.value ? String(whatsappSetting.value) : ""
    }
  });

  // Mutation pour mettre à jour le numéro WhatsApp
  const updateWhatsappMutation = useMutation({
    mutationFn: async (whatsappNumber: string) => {
      console.log("Envoi de la requête pour mettre à jour le numéro WhatsApp:", whatsappNumber);
      // Utiliser fetch directement pour pouvoir accéder aux détails de l'erreur
      const response = await fetch('/api/app-settings/whatsapp_number', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          value: whatsappNumber,
          description: "Numéro WhatsApp affiché sur les pages de détail des produits"
        })
      });
      
      console.log("Réponse reçue:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Erreur lors de la mise à jour:", errorText);
        throw new Error(errorText);
      }
      
      return await response.json();
    },
    onSuccess: () => {
      console.log("Mise à jour réussie!");
      queryClient.invalidateQueries({ queryKey: ['/api/app-settings/whatsapp_number'] });
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp-number'] });
      toast({
        title: "Succès",
        description: "Numéro WhatsApp mis à jour avec succès",
      });
    },
    onError: (error: any) => {
      console.error("Erreur lors de la mutation:", error);
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour le numéro WhatsApp: " + (error.message || "Raison inconnue"),
        variant: "destructive"
      });
    }
  });

  // Gestion de la soumission du formulaire
  const onSubmit = (data: WhatsappFormValues) => {
    updateWhatsappMutation.mutate(data.whatsappNumber);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-[60vh]">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-8">
        <h1 className="text-2xl font-bold mb-6">Gestion des Paramètres</h1>
        
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Numéro WhatsApp</CardTitle>
            <CardDescription>
              Ce numéro sera affiché sur le bouton de contact WhatsApp dans les pages de détail des produits.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="whatsappNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Numéro WhatsApp (avec l'indicatif pays)</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Phone className="w-5 h-5 mr-2 text-muted-foreground" />
                          <Input
                            placeholder="+22670000000"
                            {...field}
                            className="flex-1"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button 
                  type="submit" 
                  className="mt-4"
                  disabled={updateWhatsappMutation.isPending}
                >
                  {updateWhatsappMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Enregistrer
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
        
        <div className="flex justify-between items-center mt-6">
          <Button
            variant="outline"
            onClick={() => navigate('/admin')}
          >
            Retour au tableau de bord
          </Button>
        </div>
      </div>
    </Layout>
  );
}