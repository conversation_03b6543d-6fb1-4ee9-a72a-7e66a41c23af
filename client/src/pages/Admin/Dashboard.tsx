import React from 'react';
import { useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import Logo from '@/components/Logo';
import { RiDashboardLine, RiShoppingBag3Line, RiBox3Line, RiBellLine, RiLogoutBoxLine, RiUserLine, RiFileTextLine, RiPagesLine, RiEdit2Line, RiSettings4Line, RiMailLine, RiBarcodeLine, RiStore2Line } from 'react-icons/ri';
import { Eye } from 'lucide-react';
import { Order, OrderStatus } from '@/lib/types';
import { apiRequest } from '@/lib/queryClient';

// Interface pour les messages de contact
interface ContactMessage {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  subject: string;
  message: string;
  isRead: boolean;
  createdAt: string;
}

const AdminDashboard: React.FC = () => {
  const [_, setLocation] = useLocation();

  // Fetch orders
  const { data: orders } = useQuery<Order[]>({
    queryKey: ['/api/orders'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/orders');
      // Ensure we always return an array, even if the API returns something else
      return Array.isArray(response) ? response : [];
    }
  });

  // Fetch products count
  const { data: products } = useQuery({
    queryKey: ['/api/products'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/products');
      // Ensure we always return an array, even if the API returns something else
      return Array.isArray(response) ? response : [];
    }
  });

  // Fetch users
  const { data: users } = useQuery({
    queryKey: ['/api/users'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/users');
      return Array.isArray(response) ? response : [];
    }
  });
  
  // Fetch recent unread contact messages
  const { data: unreadMessages, isLoading: isLoadingMessages } = useQuery<ContactMessage[]>({
    queryKey: ['/api/contact-messages/unread'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/contact-messages/unread');
      return Array.isArray(response) ? response : [];
    }
  });

  // Compute statistics
  const stats = React.useMemo(() => {
    const totalOrders = orders?.length || 0;
    const pendingOrders = orders?.filter(o => o.status !== 'DELIVERED').length || 0;
    const completedOrders = orders?.filter(o => o.status === 'DELIVERED').length || 0;
    const totalProducts = products?.length || 0;
    const totalUsers = users?.length || 0;

    return {
      totalOrders,
      pendingOrders,
      completedOrders,
      totalProducts,
      totalUsers
    };
  }, [orders, products, users]);

  // Get recent orders - latest 5
  const recentOrders = React.useMemo(() => {
    return [...(orders || [])]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
  }, [orders]);

  // Helper functions for status display
  const getStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case 'ORDER_CONFIRMED': return 'Commande Confirmée';
      case 'PROCESSING': return 'En Traitement';
      case 'IN_TRANSIT': return 'En Transit';
      case 'ARRIVED': return 'Arrivée au Burkina';
      case 'DELIVERED': return 'Livrée';
      default: return status;
    }
  };

  const getStatusColor = (status: OrderStatus): string => {
    switch (status) {
      case 'ORDER_CONFIRMED': return 'bg-blue-100 text-blue-800';
      case 'PROCESSING': return 'bg-yellow-100 text-yellow-800';
      case 'IN_TRANSIT': return 'bg-purple-100 text-purple-800';
      case 'ARRIVED': return 'bg-green-100 text-green-800';
      case 'DELIVERED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const navigateTo = (path: string) => {
    setLocation(path);
  };

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-secondary text-white p-6 flex flex-col">
        <div className="mb-8">
          <Logo className="mb-2" />
          <h1 className="text-xl font-bold">Panneau Admin</h1>
        </div>
        
        <nav className="flex-grow">
          <ul className="space-y-2">
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin')}
              >
                <RiDashboardLine className="mr-2 h-5 w-5" />
                Tableau de bord
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/orders')}
              >
                <RiBox3Line className="mr-2 h-5 w-5" />
                Commandes
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/products')}
              >
                <RiShoppingBag3Line className="mr-2 h-5 w-5" />
                Produits
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/users')}
              >
                <RiUserLine className="mr-2 h-5 w-5" />
                Utilisateurs
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Notifications
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/popup-notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Popups
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/static-pages')}
              >
                <RiFileTextLine className="mr-2 h-5 w-5" />
                Pages Statiques
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/contact-messages')}
              >
                <RiMailLine className="mr-2 h-5 w-5" />
                Messages de Contact
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/warehouse-settings')}
              >
                <RiStore2Line className="mr-2 h-5 w-5" />
                Entrepôt Chine
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/settings')}
              >
                <RiSettings4Line className="mr-2 h-5 w-5" />
                Paramètres
              </Button>
            </li>
          </ul>
        </nav>
        
        <div className="mt-auto">
          <Button 
            variant="ghost" 
            className="w-full justify-start text-white hover:bg-secondary-dark"
            onClick={() => navigateTo('/')}
          >
            <RiLogoutBoxLine className="mr-2 h-5 w-5" />
            Déconnexion
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow bg-gray-50 p-6">
        <header className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
          <p className="text-gray-500">Bienvenue sur votre tableau de bord d'administration</p>
        </header>
        
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Total Commandes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats?.totalOrders || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Commandes en Attente</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats?.pendingOrders || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Commandes Complétées</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats?.completedOrders || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Total Produits</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats?.totalProducts || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Total Utilisateurs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats?.totalUsers || 0}</div>
            </CardContent>
          </Card>
        </div>
        
        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Actions Rapides</h2>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Button 
              onClick={() => navigateTo('/admin/orders')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiBox3Line className="h-8 w-8 mb-2" />
                <span>Gérer les Commandes</span>
              </div>
            </Button>
            
            <Button 
              onClick={() => navigateTo('/admin/products')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiShoppingBag3Line className="h-8 w-8 mb-2" />
                <span>Gérer les Produits</span>
              </div>
            </Button>
            
            <Button 
              onClick={() => navigateTo('/admin/users')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiUserLine className="h-8 w-8 mb-2" />
                <span>Gérer les Utilisateurs</span>
              </div>
            </Button>
            
            <Button 
              onClick={() => navigateTo('/admin/notifications')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiBellLine className="h-8 w-8 mb-2" />
                <span>Envoyer des Notifications</span>
              </div>
            </Button>
            
            <Button 
              onClick={() => navigateTo('/admin/popup-notifications')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiBellLine className="h-8 w-8 mb-2" />
                <span>Gérer les Popups</span>
              </div>
            </Button>
            
            <Button 
              onClick={() => navigateTo('/admin/static-pages')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiFileTextLine className="h-8 w-8 mb-2" />
                <span>Gérer les Pages</span>
              </div>
            </Button>
            
            <Button 
              onClick={() => navigateTo('/admin/contact-messages')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiMailLine className="h-8 w-8 mb-2" />
                <span>Messages de Contact</span>
              </div>
            </Button>
            
            <Button 
              onClick={() => navigateTo('/admin/settings')}
              className="h-auto py-6 bg-primary hover:bg-primary-dark"
            >
              <div className="flex flex-col items-center">
                <RiSettings4Line className="h-8 w-8 mb-2" />
                <span>Paramètres</span>
              </div>
            </Button>
          </div>
        </div>
        
        {/* Recent Activity */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Activité Récente</h2>
          <Tabs defaultValue="orders">
            <TabsList className="mb-4">
              <TabsTrigger value="orders">Commandes</TabsTrigger>
              <TabsTrigger value="products">Produits</TabsTrigger>
              <TabsTrigger value="users">Utilisateurs</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="pages">Pages</TabsTrigger>
              <TabsTrigger value="messages">Messages</TabsTrigger>
            </TabsList>
            
            <TabsContent value="orders">
              <Card>
                <CardContent className="p-0">
                  <div className="rounded-md border">
                    {recentOrders && recentOrders.length > 0 ? (
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-3">Référence</th>
                            <th className="text-left p-3">Statut</th>
                            <th className="text-left p-3">Date</th>
                            <th className="text-left p-3">Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {recentOrders.map(order => (
                            <tr key={order.id} className="border-b hover:bg-gray-50">
                              <td className="p-3">{order.reference}</td>
                              <td className="p-3">
                                <span className={`px-2 py-1 rounded text-xs ${getStatusColor(order.status)}`}>
                                  {getStatusLabel(order.status)}
                                </span>
                              </td>
                              <td className="p-3">{new Date(order.createdAt).toLocaleDateString()}</td>
                              <td className="p-3">
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  onClick={() => navigateTo(`/admin/orders?edit=${order.id}`)}
                                >
                                  Mettre à jour
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="p-4">
                        <p className="text-gray-500">Aucune commande récente trouvée</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="products">
              <Card>
                <CardContent className="p-0">
                  <div className="rounded-md border">
                    <div className="p-4">
                      <p className="text-gray-500">L'activité récente des produits apparaîtra ici</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="users">
              <Card>
                <CardContent className="p-0">
                  <div className="rounded-md border">
                    {users && users.length > 0 ? (
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-3">ID</th>
                            <th className="text-left p-3">Nom d'utilisateur</th>
                            <th className="text-left p-3">Email</th>
                            <th className="text-left p-3">Date d'inscription</th>
                            <th className="text-left p-3">Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {users.slice(0, 5).map(user => (
                            <tr key={user.id} className="border-b hover:bg-gray-50">
                              <td className="p-3">{user.id}</td>
                              <td className="p-3">{user.username}</td>
                              <td className="p-3">{user.email || 'N/A'}</td>
                              <td className="p-3">{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</td>
                              <td className="p-3">
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  onClick={() => navigateTo(`/admin/users?view=${user.id}`)}
                                >
                                  Voir
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="p-4">
                        <p className="text-gray-500">Aucun utilisateur trouvé</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="notifications">
              <Card>
                <CardContent className="p-0">
                  <div className="rounded-md border">
                    <div className="p-4">
                      <p className="text-gray-500">L'activité récente des notifications apparaîtra ici</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="pages">
              <Card>
                <CardContent className="p-0">
                  <div className="rounded-md border">
                    <div className="flex justify-between items-center p-4 border-b">
                      <h3 className="font-medium">Pages Statiques</h3>
                      <Button 
                        size="sm" 
                        onClick={() => navigateTo('/admin/static-pages')}
                      >
                        Voir toutes les pages
                      </Button>
                    </div>
                    <div className="p-4">
                      <ul className="space-y-2">
                        <li className="border-b pb-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">À Propos</span>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => navigateTo('/admin/static-pages/edit/about')}
                            >
                              <RiEdit2Line className="h-4 w-4 mr-1" />
                              Modifier
                            </Button>
                          </div>
                        </li>
                        <li className="border-b pb-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">Conditions d'Utilisation</span>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => navigateTo('/admin/static-pages/edit/terms')}
                            >
                              <RiEdit2Line className="h-4 w-4 mr-1" />
                              Modifier
                            </Button>
                          </div>
                        </li>
                        <li className="border-b pb-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">Politique de Confidentialité</span>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => navigateTo('/admin/static-pages/edit/privacy')}
                            >
                              <RiEdit2Line className="h-4 w-4 mr-1" />
                              Modifier
                            </Button>
                          </div>
                        </li>
                        <li>
                          <div className="flex justify-between items-center">
                            <span className="font-medium">FAQ</span>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => navigateTo('/admin/static-pages/edit/faq')}
                            >
                              <RiEdit2Line className="h-4 w-4 mr-1" />
                              Modifier
                            </Button>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="messages">
              <Card>
                <CardContent className="p-0">
                  <div className="rounded-md border">
                    <div className="flex justify-between items-center p-4 border-b">
                      <div className="flex items-center">
                        <h3 className="font-medium">Messages de Contact</h3>
                        {unreadMessages && unreadMessages.length > 0 && (
                          <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                            {unreadMessages.length} non lu{unreadMessages.length > 1 ? 's' : ''}
                          </Badge>
                        )}
                      </div>
                      <Button 
                        size="sm" 
                        onClick={() => navigateTo('/admin/contact-messages')}
                      >
                        Voir tous les messages
                      </Button>
                    </div>
                    
                    {isLoadingMessages ? (
                      <div className="flex justify-center p-8">
                        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                      </div>
                    ) : unreadMessages && unreadMessages.length > 0 ? (
                      <div className="divide-y">
                        {unreadMessages.slice(0, 5).map(message => (
                          <div key={message.id} className="p-4 hover:bg-gray-50">
                            <div className="flex justify-between mb-2">
                              <div className="font-medium flex items-center">
                                <Badge variant="outline" className="mr-2 bg-blue-50 text-blue-700 border-blue-200">
                                  Nouveau
                                </Badge>
                                {message.firstName} {message.lastName}
                              </div>
                              <div className="text-sm text-gray-500">
                                {format(new Date(message.createdAt), "dd/MM/yyyy", { locale: fr })}
                              </div>
                            </div>
                            <div className="text-sm font-medium mb-1">{message.subject}</div>
                            <div className="text-sm text-gray-500 mb-2 line-clamp-2">
                              {message.message.substring(0, 120)}
                              {message.message.length > 120 ? "..." : ""}
                            </div>
                            <div className="flex justify-end">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-primary"
                                onClick={() => navigateTo(`/admin/contact-messages?view=${message.id}`)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                Lire le message
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <RiMailLine className="mx-auto h-12 w-12 text-muted-foreground" />
                        <h3 className="mt-4 text-lg font-semibold">Aucun nouveau message</h3>
                        <p className="text-muted-foreground mt-2">
                          Tous les messages ont été consultés.
                        </p>
                        <Button 
                          className="mt-4"
                          onClick={() => navigateTo('/admin/contact-messages')}
                        >
                          <RiMailLine className="mr-2 h-4 w-4" />
                          Voir tous les messages
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
