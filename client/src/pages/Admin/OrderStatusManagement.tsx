import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { FormDescription } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import Logo from '@/components/Logo';
import { 
  RiDashboardLine, 
  RiShoppingBag3Line, 
  RiBox3Line, 
  RiBellLine, 
  RiLogoutBoxLine, 
  RiAddLine,
  RiEdit2Line,
  RiDeleteBinLine,
  RiArrowUpLine,
  RiArrowDownLine
} from 'react-icons/ri';
import { apiRequest } from '@/lib/queryClient';

// Order status interface
interface OrderStatus {
  id: number;
  code: string;
  label: string;
  displayOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const OrderStatusManagement: React.FC = () => {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | null>(null);

  // Fetch order statuses
  const { data: statuses, isLoading, refetch: refetchStatuses } = useQuery<OrderStatus[]>({
    queryKey: ['/api/order-statuses'],
    queryFn: async () => {
      console.log('Fetching order statuses...');
      const response = await apiRequest('GET', '/api/order-statuses');
      console.log('Order statuses response:', response);
      // Ensure we always return an array, even if the API returns something else
      return Array.isArray(response) ? response : [];
    }
  });

  // Create order status form
  const createStatusSchema = z.object({
    code: z.string().min(2, 'Le code doit contenir au moins 2 caractères'),
    label: z.string().min(2, 'Le libellé doit contenir au moins 2 caractères'),
    displayOrder: z.number().min(1, 'L\'ordre d\'affichage doit être supérieur à 0'),
    isActive: z.boolean().default(true),
  });

  const createStatusForm = useForm<z.infer<typeof createStatusSchema>>({
    resolver: zodResolver(createStatusSchema),
    defaultValues: {
      code: '',
      label: '',
      displayOrder: 1,
      isActive: true,
    },
  });

  // Edit order status form
  const editStatusSchema = z.object({
    code: z.string().min(2, 'Le code doit contenir au moins 2 caractères'),
    label: z.string().min(2, 'Le libellé doit contenir au moins 2 caractères'),
    displayOrder: z.number().min(1, 'L\'ordre d\'affichage doit être supérieur à 0'),
    isActive: z.boolean(),
  });

  const editStatusForm = useForm<z.infer<typeof editStatusSchema>>({
    resolver: zodResolver(editStatusSchema),
    defaultValues: {
      code: '',
      label: '',
      displayOrder: 1,
      isActive: true,
    },
  });

  // Create order status mutation
  const createStatusMutation = useMutation({
    mutationFn: async (data: z.infer<typeof createStatusSchema>) => {
      console.log('Creating order status with payload:', data);
      const result = await apiRequest('POST', '/api/order-statuses', data);
      console.log('Create order status result:', result);
      return result;
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Statut de commande créé avec succès',
      });
      // Force refresh of statuses
      refetchStatuses();
      setCreateDialogOpen(false);
      createStatusForm.reset();
    },
    onError: (error) => {
      console.error('Error creating order status:', error);
      toast({
        title: 'Erreur',
        description: 'Échec de la création du statut de commande',
        variant: 'destructive',
      });
    },
  });

  // Update order status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async (data: z.infer<typeof editStatusSchema>) => {
      if (!selectedStatus) return null;
      
      console.log('Updating order status with ID:', selectedStatus.id, 'data:', data);
      const result = await apiRequest('PATCH', `/api/order-statuses/${selectedStatus.id}`, data);
      console.log('Update order status result:', result);
      return result;
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Statut de commande mis à jour avec succès',
      });
      // Force refresh of statuses
      refetchStatuses();
      setEditDialogOpen(false);
      editStatusForm.reset();
      setSelectedStatus(null);
    },
    onError: (error) => {
      console.error('Error updating order status:', error);
      toast({
        title: 'Erreur',
        description: 'Échec de la mise à jour du statut de commande',
        variant: 'destructive',
      });
    },
  });

  // Delete order status mutation
  const deleteStatusMutation = useMutation({
    mutationFn: async () => {
      if (!selectedStatus) return null;
      
      console.log('Deleting order status with ID:', selectedStatus.id);
      const result = await apiRequest('DELETE', `/api/order-statuses/${selectedStatus.id}`);
      console.log('Delete order status result:', result);
      return result;
    },
    onSuccess: () => {
      toast({
        title: 'Succès',
        description: 'Statut de commande supprimé avec succès',
      });
      // Force refresh of statuses
      refetchStatuses();
      setDeleteDialogOpen(false);
      setSelectedStatus(null);
    },
    onError: (error) => {
      console.error('Error deleting order status:', error);
      toast({
        title: 'Erreur',
        description: 'Échec de la suppression du statut de commande',
        variant: 'destructive',
      });
    },
  });

  const handleCreateSubmit = (data: z.infer<typeof createStatusSchema>) => {
    createStatusMutation.mutate(data);
  };

  const handleEditSubmit = (data: z.infer<typeof editStatusSchema>) => {
    updateStatusMutation.mutate(data);
  };

  const openEditDialog = (status: OrderStatus) => {
    setSelectedStatus(status);
    editStatusForm.reset({
      code: status.code,
      label: status.label,
      displayOrder: status.displayOrder,
      isActive: status.isActive,
    });
    setEditDialogOpen(true);
  };

  const openDeleteDialog = (status: OrderStatus) => {
    setSelectedStatus(status);
    setDeleteDialogOpen(true);
  };

  const navigateTo = (path: string) => {
    setLocation(path);
  };

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-secondary text-white p-6 flex flex-col">
        <div className="mb-8">
          <Logo className="mb-2" />
          <h1 className="text-xl font-bold">Panneau d'Administration</h1>
        </div>
        
        <nav className="flex-grow">
          <ul className="space-y-2">
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin')}
              >
                <RiDashboardLine className="mr-2 h-5 w-5" />
                Tableau de bord
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/orders')}
              >
                <RiBox3Line className="mr-2 h-5 w-5" />
                Commandes
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark bg-secondary-dark"
                onClick={() => navigateTo('/admin/order-statuses')}
              >
                <RiBox3Line className="mr-2 h-5 w-5" />
                Statuts de Commande
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/products')}
              >
                <RiShoppingBag3Line className="mr-2 h-5 w-5" />
                Produits
              </Button>
            </li>
            <li>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-white hover:bg-secondary-dark"
                onClick={() => navigateTo('/admin/notifications')}
              >
                <RiBellLine className="mr-2 h-5 w-5" />
                Notifications
              </Button>
            </li>
          </ul>
        </nav>
        
        <div className="mt-auto">
          <Button 
            variant="ghost" 
            className="w-full justify-start text-white hover:bg-secondary-dark"
            onClick={() => navigateTo('/')}
          >
            <RiLogoutBoxLine className="mr-2 h-5 w-5" />
            Déconnexion
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-grow bg-gray-50 p-6">
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des Statuts de Commande</h1>
            <p className="text-gray-500">Créer, modifier et supprimer les statuts de commande</p>
          </div>
          
          <Button 
            onClick={() => setCreateDialogOpen(true)}
            className="bg-primary hover:bg-primary-dark"
          >
            <RiAddLine className="mr-2 h-5 w-5" />
            Nouveau Statut
          </Button>
        </header>
        
        {/* Status List */}
        <Card>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="p-6 text-center">
                <p>Chargement des statuts de commande...</p>
              </div>
            ) : statuses && statuses.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ordre</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Libellé</TableHead>
                    <TableHead>Actif</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {statuses.map(status => (
                    <TableRow key={status.id}>
                      <TableCell>{status.displayOrder}</TableCell>
                      <TableCell>{status.code}</TableCell>
                      <TableCell>{status.label}</TableCell>
                      <TableCell>
                        {status.isActive ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Actif
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Inactif
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => openEditDialog(status)}
                          >
                            <RiEdit2Line className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => openDeleteDialog(status)}
                          >
                            <RiDeleteBinLine className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="p-6 text-center">
                <p className="text-gray-500">Aucun statut de commande trouvé. Créez un nouveau statut pour commencer.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Create Status Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Créer un nouveau statut de commande</DialogTitle>
            <DialogDescription>
              Ajouter un nouveau statut de commande pour suivre les commandes.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...createStatusForm}>
            <form onSubmit={createStatusForm.handleSubmit(handleCreateSubmit)} className="space-y-4">
              <FormField
                control={createStatusForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Code</FormLabel>
                    <FormControl>
                      <Input placeholder="ex. READY_TO_SHIP" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={createStatusForm.control}
                name="label"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Libellé</FormLabel>
                    <FormControl>
                      <Input placeholder="ex. Prêt à expédier" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={createStatusForm.control}
                name="displayOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ordre d'affichage</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1" 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={createStatusForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Actif</FormLabel>
                      <FormDescription>
                        Activer ou désactiver ce statut de commande.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={() => setCreateDialogOpen(false)}
                >
                  Annuler
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={createStatusMutation.isPending}
                >
                  {createStatusMutation.isPending ? 'Création...' : 'Créer le statut'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Edit Status Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Modifier le statut de commande</DialogTitle>
            <DialogDescription>
              Modifier les détails du statut de commande.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...editStatusForm}>
            <form onSubmit={editStatusForm.handleSubmit(handleEditSubmit)} className="space-y-4">
              <FormField
                control={editStatusForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Code</FormLabel>
                    <FormControl>
                      <Input placeholder="ex. READY_TO_SHIP" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editStatusForm.control}
                name="label"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Libellé</FormLabel>
                    <FormControl>
                      <Input placeholder="ex. Prêt à expédier" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editStatusForm.control}
                name="displayOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ordre d'affichage</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1" 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editStatusForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Actif</FormLabel>
                      <FormDescription>
                        Activer ou désactiver ce statut de commande.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={() => setEditDialogOpen(false)}
                >
                  Annuler
                </Button>
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary-dark"
                  disabled={updateStatusMutation.isPending}
                >
                  {updateStatusMutation.isPending ? 'Mise à jour...' : 'Mettre à jour'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Status Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Supprimer le statut de commande</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer ce statut de commande ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-4">
            {selectedStatus && (
              <div className="p-4 bg-gray-50 rounded">
                <p><span className="font-medium">Code:</span> {selectedStatus.code}</p>
                <p><span className="font-medium">Libellé:</span> {selectedStatus.label}</p>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)}
            >
              Annuler
            </Button>
            <Button 
              variant="destructive"
              onClick={() => deleteStatusMutation.mutate()}
              disabled={deleteStatusMutation.isPending}
            >
              {deleteStatusMutation.isPending ? 'Suppression...' : 'Supprimer'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderStatusManagement;