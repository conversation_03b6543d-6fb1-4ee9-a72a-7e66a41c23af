import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CheckCheck, Trash2, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import Layout from "@/components/Layout";

// Type pour un message de contact
interface ContactMessage {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  subject: string;
  message: string;
  isRead: boolean;
  createdAt: string;
}

export default function ContactMessagesPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [location] = useLocation();
  const [selectedMessage, setSelectedMessage] = useState<ContactMessage | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("all");

  // Requête pour récupérer tous les messages
  const { data: allMessages = [], isLoading: isLoadingAll } = useQuery<ContactMessage[]>({
    queryKey: ['/api/contact-messages'],
  });

  // Requête pour récupérer les messages non lus
  const { data: unreadMessages = [], isLoading: isLoadingUnread } = useQuery<ContactMessage[]>({
    queryKey: ['/api/contact-messages/unread'],
  });

  // Définir les messages à afficher en fonction de l'onglet actif
  const messagesToDisplay = activeTab === "all" ? allMessages : unreadMessages;
  const isLoading = activeTab === "all" ? isLoadingAll : isLoadingUnread;
  
  // Traiter l'URL pour ouvrir un message spécifique si un paramètre "view" est présent
  useEffect(() => {
    // Fonction pour extraire les paramètres de l'URL
    const getQueryParams = () => {
      const searchParams = new URLSearchParams(window.location.search);
      return Object.fromEntries(searchParams.entries());
    };
    
    const params = getQueryParams();
    const messageId = params.view ? parseInt(params.view, 10) : null;
    
    // Si un ID de message est spécifié dans l'URL et que les messages sont chargés
    if (messageId && !isLoading && allMessages.length > 0) {
      // Rechercher le message correspondant
      const message = allMessages.find(m => m.id === messageId);
      
      if (message) {
        // Ouvrir automatiquement le message
        handleViewMessage(message);
        
        // Si le message est non lu, basculer vers l'onglet "non lus"
        if (!message.isRead) {
          setActiveTab("unread");
        }
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allMessages, isLoading]);
  
  // Mutation pour marquer un message comme lu
  const markAsReadMutation = useMutation({
    mutationFn: (id: number) => apiRequest("PATCH", `/api/contact-messages/${id}/read`),
    onSuccess: () => {
      // Invalider les requêtes pour mettre à jour les données
      queryClient.invalidateQueries({ queryKey: ['/api/contact-messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contact-messages/unread'] });
      
      toast({
        title: "Message marqué comme lu",
        description: "Le statut du message a été mis à jour avec succès.",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de marquer le message comme lu. Veuillez réessayer.",
        variant: "destructive",
      });
    },
  });

  // Mutation pour supprimer un message
  const deleteMessageMutation = useMutation({
    mutationFn: (id: number) => apiRequest("DELETE", `/api/contact-messages/${id}`),
    onSuccess: () => {
      // Fermer la boîte de dialogue et invalider les requêtes
      setIsDeleteDialogOpen(false);
      setSelectedMessage(null);
      queryClient.invalidateQueries({ queryKey: ['/api/contact-messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contact-messages/unread'] });
      
      toast({
        title: "Message supprimé",
        description: "Le message a été supprimé avec succès.",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le message. Veuillez réessayer.",
        variant: "destructive",
      });
    },
  });

  // Gérer l'ouverture d'un message
  const handleViewMessage = (message: ContactMessage) => {
    setSelectedMessage(message);
    setIsViewOpen(true);
    
    // Si le message n'est pas encore lu, le marquer comme lu
    if (!message.isRead) {
      markAsReadMutation.mutate(message.id);
    }
  };

  // Gérer la confirmation de suppression
  const handleDeleteConfirm = () => {
    if (selectedMessage) {
      deleteMessageMutation.mutate(selectedMessage.id);
    }
  };

  // Gérer la suppression d'un message
  const handleDeleteMessage = (message: ContactMessage) => {
    setSelectedMessage(message);
    setIsDeleteDialogOpen(true);
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd MMMM yyyy à HH:mm", { locale: fr });
  };

  return (
    <Layout>
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">Messages de contact</CardTitle>
            <CardDescription>
              Gérez les messages envoyés via le formulaire de contact du site
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <div className="flex justify-between items-center mb-4">
                <TabsList>
                  <TabsTrigger value="all">
                    Tous les messages
                    <Badge variant="outline" className="ml-2">{allMessages.length}</Badge>
                  </TabsTrigger>
                  <TabsTrigger value="unread">
                    Non lus
                    <Badge variant="outline" className="ml-2">{unreadMessages.length}</Badge>
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="all" className="space-y-4">
                {renderMessagesTable(messagesToDisplay, isLoading)}
              </TabsContent>
              
              <TabsContent value="unread" className="space-y-4">
                {renderMessagesTable(messagesToDisplay, isLoading)}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Boîte de dialogue pour afficher un message */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>{selectedMessage?.subject}</DialogTitle>
            <DialogDescription>
              De: {selectedMessage?.firstName} {selectedMessage?.lastName} ({selectedMessage?.email})
              {selectedMessage?.phone && <span> • Tél: {selectedMessage?.phone}</span>}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Reçu le {selectedMessage && formatDate(selectedMessage.createdAt)}
            </div>
            <div className="bg-secondary/30 p-4 rounded-md whitespace-pre-wrap">
              {selectedMessage?.message}
            </div>
          </div>
          <DialogFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setIsViewOpen(false)}
            >
              Fermer
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                setIsViewOpen(false);
                if (selectedMessage) {
                  handleDeleteMessage(selectedMessage);
                }
              }}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Supprimer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Boîte de dialogue de confirmation de suppression */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer définitivement ce message ? Cette action ne peut pas être annulée.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Annuler
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteMessageMutation.isPending}
            >
              {deleteMessageMutation.isPending ? "Suppression..." : "Supprimer"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Layout>
  );

  // Fonction pour afficher le tableau des messages
  function renderMessagesTable(messages: ContactMessage[], isLoading: boolean) {
    if (isLoading) {
      return (
        <div className="flex justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      );
    }

    if (messages.length === 0) {
      return (
        <div className="text-center py-8">
          <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">Aucun message</h3>
          <p className="text-muted-foreground">Il n'y a aucun message dans cette catégorie.</p>
        </div>
      );
    }

    return (
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">Expéditeur</TableHead>
              <TableHead>Sujet</TableHead>
              <TableHead className="w-[120px]">Date</TableHead>
              <TableHead className="w-[100px] text-center">Statut</TableHead>
              <TableHead className="w-[120px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {messages.map((message) => (
              <TableRow key={message.id} className={!message.isRead ? "bg-primary-foreground/30" : ""}>
                <TableCell className="font-medium">
                  {message.firstName} {message.lastName}
                  <div className="text-xs text-muted-foreground">{message.email}</div>
                </TableCell>
                <TableCell>
                  <div className="font-medium truncate max-w-[300px]">{message.subject}</div>
                  <div className="text-xs text-muted-foreground truncate max-w-[300px]">
                    {message.message.substring(0, 80)}
                    {message.message.length > 80 ? "..." : ""}
                  </div>
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {format(new Date(message.createdAt), "dd/MM/yyyy", { locale: fr })}
                </TableCell>
                <TableCell className="text-center">
                  {message.isRead ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      Lu
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      Non lu
                    </Badge>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleViewMessage(message)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    {!message.isRead && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => markAsReadMutation.mutate(message.id)}
                        disabled={markAsReadMutation.isPending}
                      >
                        <CheckCheck className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteMessage(message)}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }
}