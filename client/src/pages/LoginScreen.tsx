import React, { useState } from 'react';
import { useLocation, Link } from 'wouter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import Logo from '@/components/Logo';
import { 
  RiFacebookFill, 
  RiAppleFill, 
  RiWhatsappLine 
} from 'react-icons/ri';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

const loginSchema = z.object({
  email: z.string().min(1, 'Identifiant requis'),
  password: z.string().min(6, 'Le mot de passe doit contenir au moins 6 caractères'),
  rememberMe: z.boolean().optional(),
});

type LoginFormValues = z.infer<typeof loginSchema>;

const LoginScreen: React.FC = () => {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { loginMutation } = useAuth();

  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '', // Ce champ contient en réalité soit l'email soit le nom d'utilisateur
      password: '',
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    try {
      await loginMutation.mutateAsync(data);
      setLocation('/home');
    } catch (error) {
      // Les erreurs sont gérées dans loginMutation.onError
    }
  };

  const handleSocialLogin = async (provider: string) => {
    if (provider === 'Google') {
      try {
        const response = await fetch('/api/auth/google');
        const { url } = await response.json();
        window.location.href = url;
      } catch (error) {
        toast({
          title: 'Erreur',
          description: 'Impossible de se connecter avec Google',
          variant: 'destructive',
        });
      }
    } else {
      toast({
        title: `Connexion via ${provider}`,
        description: 'Cette fonctionnalité sera bientôt disponible',
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-10">
          <div className="mx-auto mb-4">
            <Logo size="large" linkTo="/auth" />
          </div>
          <h1 className="text-2xl font-bold text-neutral-800">Bienvenue sur Waabo</h1>
          <p className="text-neutral-600 mt-2">Connectez-vous pour suivre vos colis</p>
        </div>

        <div className="space-y-4">
          <Button
            type="button"
            className="w-full flex items-center justify-center bg-[#1877F2] hover:bg-[#166FE5] text-white"
            onClick={() => handleSocialLogin('Facebook')}
          >
            <RiFacebookFill className="mr-3 text-xl" />
            <span>Continuer avec Facebook</span>
          </Button>
          
          <Button
            type="button"
            className="w-full flex items-center justify-center bg-black hover:bg-gray-800 text-white"
            onClick={() => handleSocialLogin('Apple')}
          >
            <RiAppleFill className="mr-3 text-xl" />
            <span>Continuer avec Apple</span>
          </Button>
          
          <Button
            type="button"
            className="w-full flex items-center justify-center bg-[#25D366] hover:bg-[#128C7E] text-white"
            onClick={() => handleSocialLogin('WhatsApp')}
          >
            <RiWhatsappLine className="mr-3 text-xl" />
            <span>Continuer avec WhatsApp</span>
          </Button>
          
          <div className="relative flex items-center my-6">
            <div className="flex-grow border-t border-gray-300"></div>
            <span className="flex-shrink mx-4 text-neutral-600">ou</span>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-1">
              <Label htmlFor="email">Email ou Nom d'utilisateur</Label>
              <Input
                id="email"
                type="text"
                placeholder="<EMAIL> ou nom d'utilisateur"
                {...register('email')}
                autoComplete="username"
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>
            
            <div className="space-y-1">
              <Label htmlFor="password">Mot de passe</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                {...register('password')}
              />
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password.message}</p>
              )}
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox id="remember-me" {...register('rememberMe')} />
                <Label htmlFor="remember-me" className="text-sm text-neutral-700">
                  Se souvenir de moi
                </Label>
              </div>
              <Link href="/forgot-password" className="text-sm font-medium text-[#004d25] hover:text-[#003d1e]">
                Mot de passe oublié?
              </Link>
            </div>
            
            <Button 
              type="submit" 
              className="w-full bg-[#004d25] hover:bg-[#003d1e] text-white" 
              disabled={loginMutation.isPending}
            >
              {loginMutation.isPending ? 'Connexion en cours...' : 'Se connecter'}
            </Button>
          </form>
          
          <p className="text-center text-sm text-neutral-600 mt-6">
            Pas de compte? <Link href="/signup" className="font-medium text-[#004d25] hover:text-[#003d1e]">S'inscrire</Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginScreen;
