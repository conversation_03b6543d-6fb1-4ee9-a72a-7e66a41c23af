import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { Order } from "@/lib/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  RiDeleteBin6Line,
  RiArrowDownSLine,
  RiArrowUpSLine,
} from "react-icons/ri";
import { Link, useLocation } from "wouter";
import { Badge } from "@/components/ui/badge";
import Logo from "@/components/Logo";

const formatDate = (date: Date | string | undefined) => {
  if (!date) return "";
  const d = new Date(date);
  // Format en GMT+0/UTC
  const day = d.getUTCDate();
  const month = d.getUTCMonth();
  const year = d.getUTCFullYear();

  // Noms des mois en français
  const monthNames = [
    "janvier",
    "février",
    "mars",
    "avril",
    "mai",
    "juin",
    "juillet",
    "août",
    "septembre",
    "octobre",
    "novembre",
    "décembre",
  ];

  return `${day} ${monthNames[month]} ${year}`;
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case "ORDER_CONFIRMED":
      return "Commande Confirmée";
    case "PROCESSING":
      return "En Traitement";
    case "IN_TRANSIT":
      return "En Transit";
    case "ARRIVED":
      return "Arrivée au Burkina";
    case "DELIVERED":
      return "Livrée";
    default:
      return status.replace("_", " ");
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "ORDER_CONFIRMED":
      return "bg-blue-100 text-blue-800";
    case "PROCESSING":
      return "bg-yellow-100 text-yellow-800";
    case "IN_TRANSIT":
      return "bg-purple-100 text-purple-800";
    case "ARRIVED":
      return "bg-indigo-100 text-indigo-800";
    case "DELIVERED":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const SavedOrdersPage: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [openOrderId, setOpenOrderId] = useState<number | null>(null);
  const [, setLocation] = useLocation();

  // Récupérer les commandes sauvegardées
  const { data: savedOrders, isLoading } = useQuery<Order[]>({
    queryKey: ["/api/orders/saved", user?.id],
    queryFn: async () => {
      if (!user) return [];
      const res = await fetch(`/api/orders/saved/${user.id}`);
      if (!res.ok) return [];
      return res.json();
    },
    enabled: !!user,
  });

  // Supprimer une commande sauvegardée
  const deleteMutation = useMutation({
    mutationFn: async (orderId: number) => {
      const res = await fetch(`/api/orders/saved/${orderId}`, {
        method: "DELETE",
      });

      if (!res.ok) {
        throw new Error("Échec de la suppression de la commande");
      }

      return orderId;
    },
    onSuccess: () => {
      // Invalider le cache pour recharger les commandes sauvegardées
      queryClient.invalidateQueries({
        queryKey: ["/api/orders/saved", user?.id],
      });
      toast({
        title: "Commande supprimée",
        description: "La commande a été retirée de vos favoris",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de supprimer la commande",
        variant: "destructive",
      });
    },
  });

  // Gérer l'ouverture/fermeture des détails
  const toggleOrderDetails = (orderId: number) => {
    if (openOrderId === orderId) {
      setOpenOrderId(null);
    } else {
      setOpenOrderId(orderId);
    }
  };

  // Supprimer une commande
  const handleDelete = (orderId: number) => {
    deleteMutation.mutate(orderId);
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header avec position fixe */}
      <header className="bg-white text-[#004d25] shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button
                  variant="ghost"
                  className="text-[#004d25] hover:bg-gray-100 flex items-center"
                >
                  <svg
                    className="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Retour
                </Button>
              </Link>
            </div>
            <div className="w-[100px]">
              {/* Élément vide pour équilibrer la mise en page */}
            </div>
          </div>
        </div>
      </header>
      
      {/* Espace pour compenser la hauteur du header fixe */}
      <div className="h-16"></div>
      
      {/* Main Content */}
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <Card className="rounded-2xl shadow-md">
            <CardContent>
              <CardHeader>
                <CardTitle className="text-lg font-bold text-gray-900">
                  Mes commandes sauvegardées
                </CardTitle>
                <CardDescription>
                  Consultez et gérez les commandes que vous avez enregistrées
                  pour un suivi facile
                </CardDescription>
              </CardHeader>
              {isLoading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-pulse w-full max-w-md">
                    <div className="h-14 bg-gray-200 rounded-md mb-4"></div>
                    <div className="h-14 bg-gray-200 rounded-md mb-4"></div>
                    <div className="h-14 bg-gray-200 rounded-md"></div>
                  </div>
                </div>
              ) : savedOrders && savedOrders.length > 0 ? (
                <div className="space-y-4">
                  {savedOrders.map((order) => (
                    <Collapsible
                      key={order.id}
                      open={openOrderId === order.id}
                      className="border border-gray-200 rounded-xl overflow-hidden"
                    >
                      <div
                        className="flex justify-between items-center p-4 bg-white hover:bg-gray-50 cursor-pointer"
                        onClick={() => toggleOrderDetails(order.id)}
                      >
                        <div className="flex-1">
                          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                            <div className="flex flex-col">
                              <span className="font-semibold text-gray-900">
                                #{order.reference}
                              </span>
                              <span className="text-sm text-gray-500">
                                {formatDate(order.estimatedDelivery)}
                              </span>
                            </div>
                            <div className="flex items-center space-x-3">
                              <Badge
                                className={`${getStatusColor(order.status)} px-3 py-1 text-xs font-medium`}
                              >
                                {getStatusLabel(order.status)}
                              </Badge>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-[#004d25] border-[#004d25] hover:bg-[#004d25] hover:text-white w-10 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Utiliser window.history.pushState pour ne pas recharger la page
                                  window.history.pushState(
                                    {},
                                    "",
                                    `/?tracking=${order.reference}`,
                                  );
                                  // Rediriger vers la page d'accueil avec l'état
                                  setLocation(`/?tracking=${order.reference}`);
                                }}
                                title="Voir détails"
                                aria-label="Voir détails"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
                                  <circle cx="12" cy="12" r="3" />
                                </svg>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDelete(order.id);
                                }}
                                className="text-red-500 hover:bg-red-50 w-10 p-0"
                                title="Supprimer"
                                aria-label="Supprimer"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <path d="M3 6h18" />
                                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                                </svg>
                              </Button>
                            </div>
                          </div>
                        </div>
                        <div className="hidden">
                          <CollapsibleTrigger
                            asChild
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-gray-500 hidden"
                            >
                              <RiArrowDownSLine className="h-5 w-5" />
                            </Button>
                          </CollapsibleTrigger>
                        </div>
                      </div>
                      <CollapsibleContent>
                        <div className="p-4 border-t border-gray-100 bg-gray-50">
                          <div className="grid md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-500">
                                Dernière mise à jour
                              </p>
                              <p className="text-sm text-gray-900">
                                {order.latestUpdate ||
                                  "Aucune mise à jour disponible"}
                              </p>
                              {order.latestUpdateTime && (
                                <p className="text-xs text-gray-500 mt-1">
                                  {formatDate(order.latestUpdateTime)}
                                </p>
                              )}
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-500">
                                Date de création
                              </p>
                              <p className="text-sm text-gray-900">
                                {formatDate(order.createdAt)}
                              </p>
                            </div>
                          </div>
                          <div className="mt-4 flex justify-end">
                            <Button
                              size="sm"
                              className="bg-[#004d25] hover:bg-[#003d1e] text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Utiliser window.history.pushState pour ne pas recharger la page
                                window.history.pushState(
                                  {},
                                  "",
                                  `/?tracking=${order.reference}`,
                                );
                                // Rediriger vers la page d'accueil avec l'état
                                setLocation(`/?tracking=${order.reference}`);
                              }}
                            >
                              Voir les détails complets
                            </Button>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  ))}
                </div>
              ) : (
                <div className="py-12 text-center">
                  <div className="mx-auto w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                    <svg
                      className="h-8 w-8 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                      />
                    </svg>
                  </div>
                  <h3 className="text-sm font-medium text-gray-900">
                    Aucune commande sauvegardée
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Vous n'avez pas encore sauvegardé de commandes pour un suivi
                    facile.
                  </p>
                  <div className="mt-6">
                    <Link href="/">
                      <Button className="bg-[#004d25] hover:bg-[#003d1e] text-white">
                        Suivre une commande
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default SavedOrdersPage;
