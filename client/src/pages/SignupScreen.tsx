import React from 'react';
import { useLocation, Link } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Logo from '@/components/Logo';
import { 
  RiFacebookFill, 
  RiAppleFill, 
  RiWhatsappLine 
} from 'react-icons/ri';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/hooks/use-auth';

const signupSchema = z.object({
  username: z.string().min(3, 'Le nom d\'utilisateur doit contenir au moins 3 caractères'),
  email: z.string().email('Veuillez entrer un email valide').min(1, 'Email requis'),
  password: z.string().min(6, 'Le mot de passe doit contenir au moins 6 caractères'),
  confirmPassword: z.string().min(1, 'Veuillez confirmer votre mot de passe'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword'],
});

type SignupFormValues = z.infer<typeof signupSchema>;

const SignupScreen: React.FC = () => {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { registerMutation } = useAuth();

  const { register, handleSubmit, formState: { errors } } = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: SignupFormValues) => {
    try {
      const { confirmPassword, ...signupData } = data;
      await registerMutation.mutateAsync(signupData);
      
      // Après une inscription réussie, rediriger vers la page d'accueil
      setLocation('/home');
    } catch (error) {
      // Les erreurs sont gérées dans registerMutation.onError
    }
  };

  const handleSocialLogin = (provider: string) => {
    toast({
      title: `Inscription via ${provider}`,
      description: 'Cette fonctionnalité sera bientôt disponible',
    });
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-10">
          <div className="mx-auto mb-4">
            <Logo size="large" linkTo="/auth" />
          </div>
          <h1 className="text-2xl font-bold text-neutral-800">Rejoignez Waabo</h1>
          <p className="text-neutral-600 mt-2">Créez un compte pour suivre vos colis</p>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm p-8">
          <div className="space-y-4">
            <Button
              type="button"
              className="w-full flex items-center justify-center bg-[#1877F2] hover:bg-[#166FE5] text-white"
              onClick={() => handleSocialLogin('Facebook')}
            >
              <RiFacebookFill className="mr-3 text-xl" />
              <span>Continuer avec Facebook</span>
            </Button>
            
            <Button
              type="button"
              className="w-full flex items-center justify-center bg-black hover:bg-gray-800 text-white"
              onClick={() => handleSocialLogin('Apple')}
            >
              <RiAppleFill className="mr-3 text-xl" />
              <span>Continuer avec Apple</span>
            </Button>
            
            <Button
              type="button"
              className="w-full flex items-center justify-center bg-[#25D366] hover:bg-[#128C7E] text-white"
              onClick={() => handleSocialLogin('WhatsApp')}
            >
              <RiWhatsappLine className="mr-3 text-xl" />
              <span>Continuer avec WhatsApp</span>
            </Button>
            
            <div className="relative flex items-center my-6">
              <div className="flex-grow border-t border-gray-300"></div>
              <span className="flex-shrink mx-4 text-neutral-600">ou</span>
              <div className="flex-grow border-t border-gray-300"></div>
            </div>
            
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Nom d'utilisateur</Label>
                <Input 
                  id="username" 
                  type="text" 
                  placeholder="Votre nom d'utilisateur" 
                  {...register('username')} 
                />
                {errors.username && (
                  <p className="text-sm text-red-500">{errors.username.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>" 
                  {...register('email')} 
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Mot de passe</Label>
                <Input 
                  id="password" 
                  type="password" 
                  placeholder="••••••••" 
                  {...register('password')} 
                />
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirmer le mot de passe</Label>
                <Input 
                  id="confirmPassword" 
                  type="password" 
                  placeholder="••••••••" 
                  {...register('confirmPassword')} 
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>
                )}
              </div>
              
              <Button 
                type="submit" 
                className="w-full bg-[#004d25] hover:bg-[#003d1e] text-white" 
                disabled={registerMutation.isPending}
              >
                {registerMutation.isPending ? 'Inscription en cours...' : 'S\'inscrire'}
              </Button>
            </form>
            
            <p className="text-center text-sm text-neutral-600 mt-6">
              Déjà un compte? <Link href="/login" className="font-medium text-[#004d25] hover:text-[#003d1e]">Se connecter</Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignupScreen;