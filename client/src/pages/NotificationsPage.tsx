import React from "react";
import { useNotifications } from "@/hooks/use-notifications";
import { Notification } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "wouter";
import { RiArrowLeftLine, RiArrowLeftSLine } from "react-icons/ri";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { useAuth } from "@/hooks/use-auth";

const NotificationsPage: React.FC = () => {
  const { user } = useAuth();
  const { notifications, allNotifications, isLoading, markAsRead } =
    useNotifications();

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);
  };

  const formatNotificationDate = (date: Date | string | undefined) => {
    if (!date) return "";

    try {
      const notifDate = new Date(date);
      return formatDistanceToNow(notifDate, { addSuffix: true, locale: fr });
    } catch (error) {
      console.error("Date format error:", error);
      return "";
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header fixe */}
      <header className="bg-white text-[#004d25] shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <Link
              href="/"
              className="flex items-center text-[#004d25] hover:text-[#003d1e]"
            >
              <RiArrowLeftSLine className="h-6 w-6 mr-1" />
              <span>Retour</span>
            </Link>
            <div className="flex items-center space-x-4">
              {user && user.username === "admin" && (
                <Link href="/admin">
                  <Button
                    variant="outline"
                    className="text-[#004d25] border-[#004d25] hover:bg-[#004d25] hover:text-white"
                  >
                    Administration
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>
      
      {/* Espace pour compenser la hauteur du header fixe */}
      <div className="h-16"></div>

      {/* Main Content */}
      <main className="flex-grow bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 sm:px-0">
            <div className="flex items-center gap-2 mb-4"></div>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Notifications</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="py-8 text-center">
                    <p>Chargement des notifications...</p>
                  </div>
                ) : !allNotifications || allNotifications.length === 0 ? (
                  <div className="py-8 text-center">
                    <p>Vous n'avez pas de notifications.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {allNotifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`border rounded-lg p-4 shadow-sm ${!notification.isRead ? "bg-green-50 border-l-4 border-l-green-500 cursor-pointer" : "bg-white"} hover:bg-gray-50 transition-colors duration-200`}
                        onClick={() =>
                          !notification.isRead
                            ? handleNotificationClick(notification)
                            : null
                        }
                      >
                        <div className="flex flex-col gap-2">
                          <div className="flex justify-between items-start">
                            <h3
                              className={`font-medium ${!notification.isRead ? "text-[#004d25] font-semibold" : "text-gray-700"}`}
                            >
                              {notification.title}
                              {!notification.isRead && (
                                <span className="ml-2 inline-flex items-center justify-center rounded-full bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-600">
                                  Nouveau
                                </span>
                              )}
                            </h3>
                            <span className="text-xs text-gray-500">
                              {formatNotificationDate(notification.sentAt)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700">
                            {notification.body}
                          </p>
                          {!notification.isRead && (
                            <div className="mt-2 text-xs text-gray-500 italic">
                              Cliquez pour marquer comme lue
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white text-[#004d25] border-t border-gray-200">
        <div className="mt-6 text-center text-sm text-[#004d25] py-4">
          <p>
            &copy; {new Date().getFullYear()} Waabo Express. Tous droits
            réservés.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default NotificationsPage;
