import { useState, useEffect } from 'react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import Logo from '@/components/Logo';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { LockKeyhole, Lock, ArrowLeft, Check } from 'lucide-react';

// Schéma de validation du formulaire
const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(8, { message: 'Le mot de passe doit contenir au moins 8 caractères' })
    .regex(/[A-Z]/, { message: 'Le mot de passe doit contenir au moins une lettre majuscule' })
    .regex(/[a-z]/, { message: 'Le mot de passe doit contenir au moins une lettre minuscule' })
    .regex(/[0-9]/, { message: 'Le mot de passe doit contenir au moins un chiffre' })
    .regex(/[^A-Za-z0-9]/, { 
      message: 'Le mot de passe doit contenir au moins un caractère spécial (ex: !@#$%^&*)'
    }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword'],
});

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

const ResetPasswordScreen = () => {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);
  const [isResetting, setIsResetting] = useState(false);
  const [resetComplete, setResetComplete] = useState(false);

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  // Extraire le token de l'URL
  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const tokenParam = queryParams.get('token');
    setToken(tokenParam);

    // Vérifier la validité du token
    if (tokenParam) {
      verifyToken(tokenParam);
    } else {
      setIsTokenValid(false);
    }
  }, []);

  // Vérifier la validité du token
  const verifyToken = async (token: string) => {
    try {
      const response = await fetch(`/api/auth/verify-reset-token?token=${token}`);
      const data = await response.json();
      setIsTokenValid(data.valid);
    } catch (error) {
      console.error('Erreur lors de la vérification du token:', error);
      setIsTokenValid(false);
    }
  };

  const onSubmit = async (data: ResetPasswordFormValues) => {
    if (!token) return;

    setIsResetting(true);
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password: data.password,
        }),
      });

      if (response.ok) {
        setResetComplete(true);
        toast({
          title: 'Succès',
          description: 'Votre mot de passe a été réinitialisé avec succès.',
        });
      } else {
        const errorData = await response.json();
        toast({
          title: 'Erreur',
          description: errorData.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Erreur lors de la réinitialisation du mot de passe:', error);
      toast({
        title: 'Erreur',
        description: 'Une erreur est survenue lors de la réinitialisation du mot de passe.',
        variant: 'destructive',
      });
    } finally {
      setIsResetting(false);
    }
  };

  // Afficher un message d'erreur si le token est invalide
  if (isTokenValid === false) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-6">
        <div className="w-full max-w-md">
          <div className="text-center mb-10">
            <div className="flex justify-center mb-4">
              <Logo size="large" withText linkTo="/auth" />
            </div>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-center text-red-600">Lien invalide</CardTitle>
              <CardDescription className="text-center">
                Ce lien de réinitialisation est invalide ou a expiré.
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex justify-center">
              <Button onClick={() => setLocation('/forgot-password')}>
                Demander un nouveau lien
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  // Afficher un indicateur de chargement pendant la vérification du token
  if (isTokenValid === null) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-6">
        <div className="w-full max-w-md text-center">
          <Logo size="large" withText noLink />
          <div className="mt-8">Vérification du lien...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            <Logo size="large" withText linkTo="/auth" />
          </div>
        </div>
        
        <Card className="border-0 shadow-lg overflow-hidden">
          <div className="h-2 bg-gradient-to-r from-[#004d25] to-[#1e6e42]"></div>
          
          <CardHeader className="pt-8">
            {resetComplete ? (
              <>
                <div className="flex justify-center mb-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <Check className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold text-center text-green-600">
                  Mot de passe réinitialisé
                </CardTitle>
                <CardDescription className="text-center max-w-xs mx-auto">
                  La réinitialisation de votre mot de passe a été effectuée avec succès
                </CardDescription>
              </>
            ) : (
              <>
                <div className="flex justify-center mb-4">
                  <div className="bg-[#004d25]/10 p-3 rounded-full">
                    <LockKeyhole className="h-8 w-8 text-[#004d25]" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold text-center text-[#004d25]">
                  Réinitialiser votre mot de passe
                </CardTitle>
                <CardDescription className="text-center max-w-xs mx-auto">
                  Créez un nouveau mot de passe sécurisé pour votre compte
                </CardDescription>
              </>
            )}
          </CardHeader>
          
          <CardContent className="pb-8 px-8">
            {resetComplete ? (
              <div className="text-center space-y-4">
                <div className="bg-green-50 border border-green-100 text-green-800 p-4 rounded-lg mb-2">
                  <p className="text-sm">Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.</p>
                </div>
              </div>
            ) : (
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-neutral-700">Nouveau mot de passe</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      id="password"
                      type="password"
                      className="pl-10 bg-white border-neutral-200"
                      {...form.register('password')}
                    />
                  </div>
                  {form.formState.errors.password && (
                    <p className="text-sm text-red-500 font-medium">{form.formState.errors.password.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-neutral-700">Confirmer le mot de passe</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <LockKeyhole className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      id="confirmPassword"
                      type="password"
                      className="pl-10 bg-white border-neutral-200"
                      {...form.register('confirmPassword')}
                    />
                  </div>
                  {form.formState.errors.confirmPassword && (
                    <p className="text-sm text-red-500 font-medium">
                      {form.formState.errors.confirmPassword.message}
                    </p>
                  )}
                </div>
                
                <div className="bg-blue-50 border border-blue-100 text-blue-800 p-4 rounded-lg mb-2 text-sm">
                  <div className="font-semibold mb-1">Conseils pour un mot de passe sécurisé :</div>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Au moins 8 caractères</li>
                    <li>Mélangez lettres majuscules et minuscules</li>
                    <li>Utilisez des chiffres (0-9)</li>
                    <li>Ajoutez des caractères spéciaux (!@#$%^&*)</li>
                  </ul>
                </div>
                
                <Button 
                  type="submit"
                  className="w-full bg-[#004d25] hover:bg-[#003d1e] text-white rounded-md transition-all py-6 h-auto font-semibold shadow-md hover:shadow-lg"
                  disabled={isResetting}
                >
                  {isResetting ? 'Réinitialisation en cours...' : 'Réinitialiser le mot de passe'}
                </Button>
              </form>
            )}
          </CardContent>
          
          <CardFooter className="flex justify-center pb-8 px-8">
            <Button 
              variant="outline"
              onClick={() => setLocation('/auth')} 
              className="w-full border-[#004d25] text-[#004d25] hover:bg-[#004d25]/5 group"
            >
              <ArrowLeft className="mr-2 h-4 w-4 group-hover:translate-x-[-2px] transition-transform" />
              Retour à la connexion
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ResetPasswordScreen;