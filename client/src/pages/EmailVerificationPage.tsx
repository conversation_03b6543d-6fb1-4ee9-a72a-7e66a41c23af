import React, { useEffect, useState } from 'react';
import { useLocation, useRoute } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

const EmailVerificationPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const { user, refreshUserData } = useAuth();
  const { toast } = useToast();
  const [match, params] = useRoute('/verify-email');
  const searchParams = new URLSearchParams(window.location.search);
  const token = searchParams.get('token');
  
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error' | 'invalid'>('loading');
  const [message, setMessage] = useState<string>('Vérification de votre email...');

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token) {
        setVerificationStatus('invalid');
        setMessage('Jeton de vérification manquant ou invalide');
        return;
      }

      try {
        const response = await fetch(`/api/email/verify?token=${token}`);
        const data = await response.json();
        
        console.log('Réponse de vérification email:', { status: response.status, data });

        if (response.ok) {
          setVerificationStatus('success');
          setMessage(data.message || 'Email vérifié avec succès');
          
          // Ne pas afficher de toast pour éviter les popups en boucle
          console.log("Statut de vérification:", data.alreadyVerified ? "Email déjà vérifié" : "Email vérifié avec succès");
          
          // Mettre à jour les données de l'utilisateur
          if (refreshUserData) {
            await refreshUserData();
          }
        } else {
          setVerificationStatus('error');
          setMessage(data.message || 'Échec de la vérification de l\'email');
          
          // Afficher un toast d'erreur avec plus de détails
          toast({
            title: "Échec de la vérification",
            description: data.message || "Une erreur s'est produite lors de la vérification",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Erreur lors de la vérification de l\'email:', error);
        setVerificationStatus('error');
        setMessage('Une erreur est survenue lors de la vérification de l\'email');
      }
    };

    verifyEmail();
  }, [token, refreshUserData]);

  const handleGoToProfile = () => {
    setLocation('/profile?verificationStatus=' + verificationStatus);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Vérification d'Email</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center">
          {verificationStatus === 'loading' && (
            <div className="flex flex-col items-center gap-4 py-8">
              <Loader2 className="h-12 w-12 animate-spin text-[#004d25]" />
              <p className="text-lg text-center">{message}</p>
            </div>
          )}
          
          {verificationStatus === 'success' && (
            <div className="flex flex-col items-center gap-4 py-8">
              <CheckCircle className="h-12 w-12 text-green-500" />
              <p className="text-lg text-center font-medium text-green-600">Vérification réussie</p>
              <p className="text-center">{message}</p>
            </div>
          )}
          
          {(verificationStatus === 'error' || verificationStatus === 'invalid') && (
            <div className="flex flex-col items-center gap-4 py-8">
              <AlertCircle className="h-12 w-12 text-red-500" />
              <p className="text-lg text-center font-medium text-red-600">
                {verificationStatus === 'invalid' ? 'Lien invalide' : 'Échec de la vérification'}
              </p>
              <p className="text-center">{message}</p>
            </div>
          )}
          
          <div className="mt-6 w-full">
            <Button 
              onClick={handleGoToProfile} 
              className="w-full bg-[#004d25] hover:bg-[#003d1e] text-white"
            >
              Aller à mon profil
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailVerificationPage;