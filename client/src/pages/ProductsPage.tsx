import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import ProductCard from '@/components/ProductCard';
import { Product } from '@/lib/types';
import { RiArrowLeftLine } from 'react-icons/ri';
import Logo from '@/components/Logo';

const ProductsPage: React.FC = () => {
  const [, setLocation] = useLocation();

  // Fetch all products
  const { data: products, isLoading, error } = useQuery<Product[]>({
    queryKey: ['/api/products'],
  });

  const handleBackClick = () => {
    setLocation('/');
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                onClick={handleBackClick}
                className="flex items-center mr-2 text-neutral-600 hover:text-primary"
              >
                <RiArrowLeftLine className="h-6 w-6 mr-1" />
                <span>Return</span>
              </Button>
            </div>
            <div></div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <p className="text-gray-500">Chargement des produits...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center py-20">
              <p className="text-red-500">Erreur lors du chargement des produits. Veuillez réessayer.</p>
            </div>
          ) : (
            <>
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-neutral-800">Catalogue de Produits</h2>
                <p className="text-neutral-600 mt-1">
                  Découvrez notre sélection de produits de qualité, disponibles à Ouagadougou et livrable partout au Burkina Faso.
                </p>
              </div>

              {products && products.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {products.map(product => (
                    <ProductCard key={product.id} product={product} />
                  ))}
                </div>
              ) : (
                <div className="flex justify-center items-center py-20">
                  <p className="text-gray-500">Aucun produit disponible actuellement.</p>
                </div>
              )}
            </>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-sm text-gray-500">
            <p>© {new Date().getFullYear()} Waabo Express. Tous droits réservés.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ProductsPage;