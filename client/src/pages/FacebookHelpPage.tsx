import React from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Info } from 'lucide-react';

export default function FacebookHelpPage() {
  // URL de l'application
  const appUrl = 'https://waabo-app.com';
  const callbackUrl = `${appUrl}/api/auth/facebook/callback`;

  return (
    <Layout>
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl">Configuration de l'authentification Facebook</CardTitle>
            <CardDescription>
              Guide pour résoudre les problèmes de connexion avec Facebook
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Problème de connexion Facebook</AlertTitle>
              <AlertDescription>
                Si vous rencontrez le message "www.facebook.com refused to connect", cela est généralement lié à la configuration de votre application Facebook.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">1. Configuration de la console développeur Facebook</h3>
              <p>Assurez-vous que les paramètres suivants sont configurés dans votre application Facebook :</p>
              
              <div className="pl-4 space-y-2">
                <p className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-500" />
                  <span>URL de l'application configurée dans les paramètres :</span>
                </p>
                <code className="block bg-slate-100 p-2 rounded">{appUrl}</code>
                
                <p className="flex items-center gap-2 mt-4">
                  <Info className="h-4 w-4 text-blue-500" />
                  <span>URL de redirection OAuth ajoutée à la liste des redirections valides :</span>
                </p>
                <code className="block bg-slate-100 p-2 rounded">{callbackUrl}</code>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">2. Statut de l'application</h3>
              <p>Vérifiez que votre application est en mode "Live" ou que votre compte est ajouté comme testeur :</p>
              
              <div className="pl-4 space-y-2">
                <p className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-500 mt-1" />
                  <span>
                    Une application en mode "Development" ne permet qu'aux comptes de testeurs de se connecter. 
                    Allez dans les paramètres de l'application &gt; Rôles &gt; Testeurs et ajoutez votre compte Facebook.
                  </span>
                </p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">3. Paramètres des domaines</h3>
              <p>Dans les paramètres de l'application Facebook, vérifiez :</p>
              
              <div className="pl-4 space-y-4">
                <div>
                  <p className="flex items-center gap-2">
                    <Info className="h-4 w-4 text-blue-500" />
                    <span>Domaines de l'application :</span>
                  </p>
                  <code className="block bg-slate-100 p-2 rounded">waabo-app.com</code>
                </div>
                
                <div>
                  <p className="flex items-center gap-2">
                    <Info className="h-4 w-4 text-blue-500" />
                    <span>Domaines d'authentification OAuth :</span>
                  </p>
                  <code className="block bg-slate-100 p-2 rounded">waabo-app.com</code>
                </div>
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Remarque importante</AlertTitle>
              <AlertDescription>
                Après avoir modifié ces paramètres, il peut être nécessaire de patienter quelques minutes avant qu'ils soient pris en compte par Facebook.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}