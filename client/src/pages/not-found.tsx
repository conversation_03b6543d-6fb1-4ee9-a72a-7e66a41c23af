import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { AlertCircle, Home } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";

export default function NotFound() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="pt-6">
          <div className="flex mb-4 gap-2">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <h1 className="text-2xl font-bold text-gray-900">404 Page Non Trouvée</h1>
          </div>

          <p className="mt-4 text-sm text-gray-600">
            La page que vous recherchez n'existe pas.
          </p>
        </CardContent>
        <CardFooter>
          <Link href="/">
            <Button className="w-full bg-[#004d25] hover:bg-[#003d1e] text-white">
              <Home className="mr-2 h-4 w-4" /> Retourner à l'accueil
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
