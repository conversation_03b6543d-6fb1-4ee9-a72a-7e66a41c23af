import React from 'react';
import { useRoute, useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Product } from '@/lib/types';
import { RiArrowLeftLine, RiCheckLine, RiWhatsappLine } from 'react-icons/ri';
import ProductImageCarousel from '@/components/ui/product-image-carousel';
import { Capacitor } from '@capacitor/core';

const ProductDetail: React.FC = () => {
  const [match, params] = useRoute('/product/:id');
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Fetch product details
  const { data: product, isLoading, error } = useQuery<Product>({
    queryKey: [`/api/products/${params?.id}`],
    enabled: !!params?.id,
  });

  const handleBackClick = () => {
    setLocation('/home');
  };

  // Fetch WhatsApp number
  const { data: whatsappData, isLoading: isWhatsappLoading } = useQuery<{ whatsappNumber: string }>({
    queryKey: ['/api/whatsapp-number'],
  });

  const handleContactUs = () => {
    // Use the dynamically fetched WhatsApp number or fall back to default
    const phoneNumber = whatsappData?.whatsappNumber || "+22670000000";
    const message = `Bonjour, je suis intéressé(e) par le produit: ${product?.name}`;

    if (Capacitor.isNativePlatform()) {
      // On native app, use deep link to open WhatsApp app directly
      const whatsappUrl = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
      window.location.href = whatsappUrl;
    } else {
      // On web, open WhatsApp web in new tab
      const webWhatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
      window.open(webWhatsappUrl, '_blank');
    }

    toast({
      title: 'Redirection WhatsApp',
      description: 'Vous allez être redirigé vers WhatsApp pour nous contacter',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Loading product details...</p>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Error loading product details. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header avec position fixe */}
      <header className="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleBackClick}
                className="mr-2 text-neutral-600 hover:text-primary"
              >
                <RiArrowLeftLine className="h-6 w-6" />
              </Button>
              <h1 className="text-lg font-medium text-neutral-900">Détails du Produit</h1>
            </div>
            {/* No icons in the header as requested */}
          </div>
        </div>
      </header>

      {/* Espace pour compenser la hauteur du header fixe */}
      <div className="h-16"></div>

      {/* Main Content */}
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 sm:px-0">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              {/* Product Images Carousel */}
              <ProductImageCarousel
                images={product.imageUrls && product.imageUrls.length > 0
                  ? product.imageUrls as string[]
                  : [product.imageUrl]}
                alt={product.name}
              />

              {/* Product Info */}
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-2xl font-bold text-neutral-800">{product.name}</h2>
                    <p className="text-neutral-600 mt-1">{product.description}</p>
                  </div>
                  <span className="text-2xl font-bold text-[hsl(var(--waabo-green))]">{product.price}</span>
                </div>

                {/* Removed reviews section as requested */}

                {/* Description */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-neutral-800 mb-2">Description</h3>
                  <p className="text-neutral-600">
                    Cette montre connectée avancée offre un suivi cardiaque 24h/24, analyse du sommeil et plus de 20 modes sportifs.
                    Avec son écran AMOLED de 1,3" et jusqu'à 14 jours d'autonomie, c'est le compagnon fitness idéal.
                    La montre est étanche jusqu'à 50m et compatible avec les appareils Android et iOS.
                  </p>
                </div>

                {/* Features */}
                {product.features && product.features.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-neutral-800 mb-2">Caractéristiques Principales</h3>
                    <ul className="space-y-2">
                      {product.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <RiCheckLine className="text-green-500 mt-1 mr-2" />
                          <span className="text-neutral-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Specifications */}
                {product.specifications && Object.keys(product.specifications).length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-neutral-800 mb-2">Spécifications</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(product.specifications).map(([key, value], index) => (
                        <div key={index}>
                          <p className="text-sm text-neutral-500">{key}</p>
                          <p className="text-neutral-800">{value}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Contact Us Button */}
                <div className="flex">
                  <Button
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white flex items-center justify-center"
                    onClick={handleContactUs}
                  >
                    <RiWhatsappLine className="w-5 h-5 mr-2" />
                    Nous contacter par WhatsApp
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ProductDetail;
