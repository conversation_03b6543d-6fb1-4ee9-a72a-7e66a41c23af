import { useQuery } from "@tanstack/react-query";
import { useParams, useLocation, Link } from "wouter";
import { Loader2 } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import PublicLayout from "@/components/PublicLayout";
import { useCallback, useMemo } from "react";
import ContactForm from "@/components/forms/ContactForm";

export interface StaticPage {
  id: number;
  slug: string;
  title: string;
  content: string;
  lastUpdated: string;
}

// Composant pour gérer l'affichage du contenu HTML avec des liens Wouter
const RichContentRenderer = ({ content }: { content: string }) => {
  // Utilise useLocation pour pouvoir utiliser navigate
  const [_, navigate] = useLocation();
  
  // Crée un conteneur temporaire pour le DOM
  const container = document.createElement("div");
  container.innerHTML = content;
  
  // Remplace les liens internes par des liens Wouter
  const processNode = useCallback((node: HTMLElement) => {
    // Trouve tous les liens dans le contenu
    const links = node.querySelectorAll("a");
    
    // Pour chaque lien trouvé
    links.forEach(link => {
      const href = link.getAttribute("href");
      
      // Si c'est un lien interne (commence par '/')
      if (href && href.startsWith("/")) {
        // Remplace le comportement onclick
        link.addEventListener("click", (e) => {
          e.preventDefault();
          // Navigue vers la route sans recharger la page
          navigate(href);
        });
      }
    });
  }, [navigate]);
  
  // Utilise useCallback pour mémoriser la fonction de rendu
  const renderContent = useCallback(() => {
    // Crée un conteneur temporaire pour manipuler le DOM
    const tempContainer = document.createElement("div");
    tempContainer.innerHTML = content;
    
    // Traite tous les nœuds
    const processAllNodes = (element: HTMLElement) => {
      Array.from(element.querySelectorAll("a")).forEach(link => {
        const href = link.getAttribute("href");
        if (href && href.startsWith("/")) {
          link.setAttribute("data-internal-link", "true");
          // Conserve la classe originale
          const originalClass = link.getAttribute("class") || "";
          link.setAttribute("class", `${originalClass} wouter-link`);
        }
      });
    };
    
    processAllNodes(tempContainer);
    
    return tempContainer.innerHTML;
  }, [content]);
  
  // Mémorise le contenu rendu pour éviter des calculs inutiles
  const processedContent = useMemo(() => renderContent(), [renderContent]);
  
  return (
    <div 
      className="prose max-w-none"
      dangerouslySetInnerHTML={{ __html: processedContent }}
      ref={(node) => {
        if (node) {
          processNode(node);
        }
      }}
    />
  );
};

export default function StaticPage() {
  const [location, navigate] = useLocation();
  // Utiliser le chemin actuel comme slug si aucun slug n'est fourni
  let slug = location.slice(1); // Enlever le "/" initial
  const { user } = useAuth();
  
  const { data: page, isLoading, error } = useQuery<StaticPage>({
    queryKey: [`/api/static-pages/${slug}`],
    enabled: !!slug,
    retry: 3,
    staleTime: 60000 // 1 minute
  });

  if (isLoading) {
    return (
      <PublicLayout>
        <div className="flex justify-center items-center min-h-[60vh]">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
        </div>
      </PublicLayout>
    );
  }

  if (error || !page) {
    return (
      <PublicLayout>
        <div className="text-center py-16">
          <h1 className="text-2xl font-bold mb-4">Page non trouvée</h1>
          <p className="mb-6">La page que vous recherchez n'existe pas ou a été déplacée.</p>
          <Button onClick={() => navigate("/")}>Retour à l'accueil</Button>
        </div>
      </PublicLayout>
    );
  }

  // Détermine si nous sommes sur la page de contact
  const isContactPage = slug === 'contact';

  return (
    <PublicLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold mb-4">{page.title}</h1>
          <Separator className="mb-6" />
          
          {/* Utilise le nouveau composant de rendu de contenu enrichi */}
          <RichContentRenderer content={page.content} />
          
          {/* Afficher le formulaire de contact uniquement sur la page de contact */}
          {isContactPage && (
            <div className="mt-12">
              <div className="flex flex-col items-center">
                <ContactForm />
              </div>
            </div>
          )}
          
          <div className="mt-8 text-sm text-muted-foreground">
            Dernière mise à jour: {new Date(page.lastUpdated).toLocaleDateString('fr-FR')}
          </div>

          {user && user.email === "<EMAIL>" && (
            <div className="mt-6">
              <Link href={`/admin/static-pages/edit/${page.slug}`}>
                <Button variant="outline">Modifier cette page</Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </PublicLayout>
  );
}
