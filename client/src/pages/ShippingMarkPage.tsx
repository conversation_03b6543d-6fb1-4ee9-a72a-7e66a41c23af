import React from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Helmet } from 'react-helmet';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import ShippingMark from '@/components/ShippingMark';
import { RiArrowLeftLine, RiInformationLine } from 'react-icons/ri';

const ShippingMarkPage: React.FC = () => {
  const { user } = useAuth();
  const [, setLocation] = useLocation();

  return (
    <>
      <Helmet>
        <title>Ma Marque d'Expédition | Waabo Express</title>
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* En-tête de la page */}
        <header className="bg-white border-b border-gray-200 py-4 px-4 sticky top-0 z-10">
          <div className="max-w-4xl mx-auto flex justify-between items-center">
            <Button 
              variant="ghost" 
              className="flex items-center gap-1 text-[#004d25]"
              onClick={() => setLocation('/profile')}
            >
              <RiArrowLeftLine className="h-5 w-5" />
              <span>Retour</span>
            </Button>
            <h1 className="text-xl font-semibold text-gray-800">Ma Marque d'Expédition</h1>
            <div className="w-10"></div> {/* Spacer pour équilibrer */}
          </div>
        </header>

        <main className="py-8 px-4">
          <div className="max-w-4xl mx-auto">
            {/* Alerte informative */}
            <div className="mb-6 bg-blue-50 border-l-4 border-blue-400 p-4 rounded-md">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <RiInformationLine className="h-6 w-6 text-blue-500" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    Cette marque d'expédition facilite l'identification de vos colis lors du transport entre la Chine et le Burkina Faso. 
                    Téléchargez-la et partagez-la avec vos fournisseurs en Chine. 
                    L'adresse de l'entrepôt indiquée est notre centre de consolidation en Chine.
                  </p>
                </div>
              </div>
            </div>

            {user ? (
              <ShippingMark className="mt-4" />
            ) : (
              <div className="text-center py-10 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-8">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Connexion requise
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Veuillez vous connecter pour accéder à votre marque d'expédition personnalisée.
                  </p>
                  <Button
                    onClick={() => setLocation('/auth')}
                    className="bg-[#004d25] hover:bg-[#003d1e]"
                  >
                    Se connecter
                  </Button>
                </div>
              </div>
            )}

            {/* Instructions supplémentaires */}
            <div className="mt-10 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Comment utiliser votre marque d'expédition</h2>
              
              <ol className="list-decimal pl-5 space-y-3 text-gray-700">
                <li>
                  <span className="font-medium">Téléchargez l'image</span> de votre marque d'expédition sur votre appareil.
                </li>
                <li>
                  <span className="font-medium">Partagez-la avec vos fournisseurs en Chine</span> pour qu'ils puissent l'apposer sur vos colis.
                </li>
                <li>
                  <span className="font-medium">Imprimez-la</span> et collez-la sur vos colis si vous êtes en Chine.
                </li>
                <li>
                  <span className="font-medium">Assurez-vous que le code-barres est clairement visible</span> et non endommagé pour faciliter le suivi.
                </li>
              </ol>
              
              <div className="mt-6 p-4 bg-yellow-50 rounded-md border border-yellow-100">
                <p className="text-sm text-yellow-800 flex items-center gap-2">
                  <RiInformationLine className="h-5 w-5 text-yellow-500" />
                  <span>
                    L'adresse de l'entrepôt peut être mise à jour périodiquement. Vérifiez toujours votre marque d'expédition avant de l'utiliser.
                  </span>
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
};

export default ShippingMarkPage;