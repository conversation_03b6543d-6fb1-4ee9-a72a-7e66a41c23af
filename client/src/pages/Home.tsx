import React, { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ProgressSteps } from "@/components/ui/progress-steps";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import Logo from "@/components/Logo";
import ProductCard from "@/components/ProductCard";
import { Order, TrackingStep, Product, AppSetting } from "@/lib/types";
import {
  RiNotification3Line,
  RiUserLine,
  RiShareForwardLine,
  RiBookmark3Line,
  RiBookmarkLine,
} from "react-icons/ri";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import TestNotificationBadge from "@/components/TestNotificationBadge";

const orderStatusToSteps = (order: Order): TrackingStep[] => {
  const { status, createdAt, updatedAt, latestUpdateTime } = order;

  // Calculer les horodatages approximatifs pour chaque étape
  // Note: Nous utilisons des estimations basées sur les dates disponibles
  // Dans un système réel, ces dates viendraient d'une base de données qui stockerait
  // l'horodatage exact de chaque changement de statut
  const orderDate = createdAt;

  // On estime que chaque étape prend environ un certain pourcentage du temps total écoulé
  const currentDate = latestUpdateTime || updatedAt || new Date();
  const totalDuration =
    new Date(currentDate).getTime() - new Date(orderDate).getTime();

  // Date simulée pour chaque étape basée sur le pourcentage du temps écoulé total
  // Dans une application réelle, ces dates viendraient de la base de données
  const confirmedDate = new Date(orderDate);
  const processingDate = new Date(
    new Date(orderDate).getTime() + totalDuration * 0.2,
  );
  const transitDate = new Date(
    new Date(orderDate).getTime() + totalDuration * 0.4,
  );
  const arrivedDate = new Date(
    new Date(orderDate).getTime() + totalDuration * 0.7,
  );
  const deliveredDate = new Date(
    new Date(orderDate).getTime() + totalDuration * 0.9,
  );

  const allSteps: TrackingStep[] = [
    {
      number: 1,
      label: "Commande Confirmée",
      status: "pending",
      timestamp: confirmedDate,
    },
    {
      number: 2,
      label: "En Traitement",
      status: "pending",
      timestamp: processingDate,
    },
    {
      number: 3,
      label: "En Transit",
      status: "pending",
      timestamp: transitDate,
    },
    {
      number: 4,
      label: "Arrivée au Burkina",
      status: "pending",
      timestamp: arrivedDate,
    },
    { number: 5, label: "Livrée", status: "pending", timestamp: deliveredDate },
  ];

  const statusIndex = {
    ORDER_CONFIRMED: 0,
    PROCESSING: 1,
    IN_TRANSIT: 2,
    ARRIVED: 3,
    DELIVERED: 4,
  };

  const currentIndex = statusIndex[status as keyof typeof statusIndex] || 0;

  return allSteps.map((step, index) => ({
    ...step,
    status:
      index < currentIndex
        ? "completed"
        : index === currentIndex
          ? "active"
          : "pending",
    // Pour les étapes futures (après l'étape actuelle), on n'affiche pas de date
    timestamp: index <= currentIndex ? step.timestamp : undefined,
  }));
};

const formatDate = (date: Date | string | undefined) => {
  if (!date) return "";
  const d = new Date(date);
  // Format en GMT+0/UTC
  const day = d.getUTCDate();
  const month = d.getUTCMonth();
  const year = d.getUTCFullYear();

  // Noms des mois en français
  const monthNames = [
    "janvier",
    "février",
    "mars",
    "avril",
    "mai",
    "juin",
    "juillet",
    "août",
    "septembre",
    "octobre",
    "novembre",
    "décembre",
  ];

  return `${day} ${monthNames[month]} ${year}`;
};

const formatTime = (date: Date | string | undefined) => {
  if (!date) return "";
  const d = new Date(date);
  // Format en GMT+0/UTC
  const hours = String(d.getUTCHours()).padStart(2, "0");
  const minutes = String(d.getUTCMinutes()).padStart(2, "0");
  return `${hours}:${minutes} GMT+0`;
};

const Home: React.FC = () => {
  const [trackingNumber, setTrackingNumber] = useState("");
  const [order, setOrder] = useState<Order | null>(null);
  const [isOrderSaved, setIsOrderSaved] = useState(false);
  const { toast } = useToast();
  const { user, logoutMutation } = useAuth();
  const [, navigate] = useLocation();
  const menuRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  // Fermer le menu quand on clique à l'extérieur
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        const menu = document.getElementById("profile-menu");
        if (menu && !menu.classList.contains("hidden")) {
          menu.classList.add("hidden");
        }
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Vérifier le paramètre de tracking dans l'URL
  useEffect(() => {
    const checkTrackingParam = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const trackingParam = urlParams.get("tracking");

      if (trackingParam) {
        setTrackingNumber(trackingParam);
        // Utiliser une fonction anonyme pour passer le paramètre
        const fetchOrder = async () => {
          await handleSearch(trackingParam);
        };
        fetchOrder();
      }
    };

    // Exécuter au chargement initial
    checkTrackingParam();

    // Ajouter un écouteur d'événements pour les changements d'URL via history.pushState
    const handlePopState = () => {
      checkTrackingParam();
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  // Vérifier si la commande est sauvegardée lorsqu'elle est chargée
  useEffect(() => {
    if (order && user) {
      checkIfOrderIsSaved(order.id);
    }
  }, [order, user]);

  // Récupérer le numéro WhatsApp depuis les paramètres de l'application
  const { data: whatsappSetting } = useQuery<AppSetting>({
    queryKey: ["/api/app-settings/whatsapp_number"],
  });

  // Fetch products
  const { data: products, isLoading: productsLoading } = useQuery<Product[]>({
    queryKey: ["/api/products"],
  });

  // Mutation pour sauvegarder une commande
  const saveOrderMutation = useMutation({
    mutationFn: async (orderId: number) => {
      const response = await fetch("/api/orders/save", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ orderId }),
      });

      if (!response.ok) {
        throw new Error("Échec de la sauvegarde");
      }

      return await response.json();
    },
    onSuccess: () => {
      setIsOrderSaved(true);
      queryClient.invalidateQueries({
        queryKey: ["/api/orders/saved", user?.id],
      });
      toast({
        title: "Commande sauvegardée",
        description: "Cette commande a été ajoutée à vos favoris",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de sauvegarder cette commande",
        variant: "destructive",
      });
    },
  });

  // Mutation pour supprimer une commande sauvegardée
  const removeOrderMutation = useMutation({
    mutationFn: async (orderId: number) => {
      const response = await fetch(`/api/orders/saved/${orderId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Échec de la suppression");
      }

      return orderId;
    },
    onSuccess: () => {
      setIsOrderSaved(false);
      queryClient.invalidateQueries({
        queryKey: ["/api/orders/saved", user?.id],
      });
      toast({
        title: "Commande retirée",
        description: "Cette commande a été retirée de vos favoris",
      });
    },
    onError: () => {
      toast({
        title: "Erreur",
        description: "Impossible de retirer cette commande des favoris",
        variant: "destructive",
      });
    },
  });

  // Vérifier si la commande est sauvegardée
  const checkIfOrderIsSaved = async (orderId: number) => {
    if (!user) return;

    try {
      const response = await fetch(`/api/orders/saved/check/${orderId}`);
      if (response.ok) {
        const data = await response.json();
        setIsOrderSaved(data.isSaved);
      }
    } catch (error) {
      console.error("Error checking if order is saved:", error);
    }
  };

  // Sauvegarder ou retirer des favoris
  const toggleSaveOrder = (e?: React.MouseEvent) => {
    // Empêcher le comportement par défaut qui pourrait causer un déplacement de la page
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!user || !order) {
      toast({
        title: "Non authentifié",
        description: "Veuillez vous connecter pour sauvegarder cette commande",
        variant: "destructive",
      });
      return;
    }

    if (isOrderSaved) {
      removeOrderMutation.mutate(order.id);
    } else {
      saveOrderMutation.mutate(order.id);
    }
  };

  const handleSearch = async (reference?: string) => {
    const searchReference = reference || trackingNumber;

    if (!searchReference.trim()) {
      toast({
        title: "Erreur",
        description: "Veuillez saisir un numéro de suivi",
        variant: "destructive",
      });
      return;
    }

    // Vérifier si l'utilisateur est connecté et a une méthode de contact vérifiée
    if (user && !user.emailVerified && !user.phoneVerified) {
      toast({
        title: "Vérification requise",
        description: "Pour suivre vos commandes, veuillez vérifier votre email ou votre numéro de téléphone dans votre profil.",
        variant: "destructive",
      });

      // Rediriger vers la page de profil pour qu'ils puissent vérifier leurs informations
      setTimeout(() => {
        navigate("/profile");
      }, 2000);

      return;
    }

    try {
      const response = await fetch(`/api/orders/${searchReference}`);
      if (!response.ok) {
        throw new Error("Commande non trouvée");
      }

      const data = await response.json();
      setOrder(data);
    } catch (error) {
      toast({
        title: "Erreur",
        description:
          "Impossible de trouver une commande avec ce numéro de référence",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col safe-area-layout">
      {/* Header */}
      <header className="bg-white text-[#004d25] shadow-sm safe-area-header">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <Logo />
            <div className="flex items-center space-x-4">
              {user && (
                <>
                  <div className="relative">
                    <Link href="/notifications">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))]"
                      >
                        <RiNotification3Line className="h-5 w-5" />
                      </Button>
                    </Link>
                  </div>

                  <div className="relative">
                    <Link href="/saved-orders">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))]"
                      >
                        <RiBookmark3Line className="h-5 w-5" />
                      </Button>
                    </Link>
                  </div>

                  <div className="relative" ref={menuRef}>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))]"
                      onClick={(e) => {
                        // Afficher le menu du profil
                        const menu = document.getElementById("profile-menu");
                        if (menu) {
                          menu.classList.toggle("hidden");
                        }
                      }}
                    >
                      <RiUserLine className="h-5 w-5" />
                    </Button>

                    <div
                      id="profile-menu"
                      className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden"
                    >
                      <Link
                        href="/profile"
                        className="block px-4 py-2 text-sm text-[#004d25] hover:bg-gray-100"
                      >
                        Mon Profil
                      </Link>
                      <button
                        onClick={() => {
                          // Fonctionnalité de partage
                          if (navigator.share) {
                            navigator
                              .share({
                                title: "Waabo - Suivez vos colis",
                                text: "Découvrez Waabo, le meilleur service de suivi des colis entre la Chine et le Burkina Faso!",
                                url: window.location.origin,
                              })
                              .then(() => {
                                toast({
                                  title: "Merci!",
                                  description: "Merci d'avoir partagé Waabo!",
                                });
                              })
                              .catch((error) => {
                                console.error("Erreur de partage:", error);
                              });
                          } else {
                            // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
                            toast({
                              title: "Partager Waabo",
                              description:
                                "Copiez ce lien et partagez-le: " +
                                window.location.origin,
                            });
                          }

                          // Fermer le menu
                          const menu = document.getElementById("profile-menu");
                          if (menu) {
                            menu.classList.add("hidden");
                          }
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-[#004d25] hover:bg-gray-100"
                      >
                        Partager cette app
                      </button>
                      <Link
                        href="/shipping-mark"
                        className="block px-4 py-2 text-sm text-[#004d25] hover:bg-gray-100"
                        onClick={() => {
                          // Fermer le menu
                          const menu = document.getElementById("profile-menu");
                          if (menu) {
                            menu.classList.add("hidden");
                          }
                        }}
                      >
                        Marque d'expédition
                      </Link>
                      <button
                        onClick={() => {
                          // Déconnexion
                          logoutMutation.mutate();

                          // Fermer le menu
                          const menu = document.getElementById("profile-menu");
                          if (menu) {
                            menu.classList.add("hidden");
                          }
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                      >
                        Déconnexion
                      </button>
                    </div>
                  </div>
                </>
              )}
              {user && user.username === "admin" && (
                <Link href="/admin">
                  <Button
                    variant="outline"
                    className="text-[#004d25] border-[#004d25] hover:bg-[#004d25] hover:text-white"
                  >
                    Administration
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {/* Badge invisible pour fonctionnalité de notification */}
          <TestNotificationBadge />

          {/* Tracking Section */}
          <div className="px-4 sm:px-0">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-neutral-800 mb-4">
                Suivre Votre Colis
              </h2>
              <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-3">
                <Input
                  type="text"
                  placeholder="Entrez votre numéro de suivi"
                  value={trackingNumber}
                  onChange={(e) => setTrackingNumber(e.target.value)}
                />
                <Button
                  onClick={() => handleSearch()}
                  className="bg-[#004d25] hover:bg-[#003d1e] text-white"
                >
                  Suivre
                </Button>
              </div>

              {/* Tracking Results */}
              {order && (
                <div className="mt-8">
                  <div className="border-0 rounded-xl shadow-lg overflow-hidden bg-white">
                    {/* Header with background and premium look */}
                    <div className="bg-gradient-to-r from-[#004d25] to-[#1e6e42] text-white p-5 md:p-6 relative">
                      <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                        <svg
                          viewBox="0 0 24 24"
                          fill="none"
                          className="w-full h-full"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="1"
                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                          />
                        </svg>
                      </div>
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between relative z-10">
                        <div>
                          <div className="flex items-center">
                            <div className="bg-white/20 p-2 rounded-lg mr-3">
                              <svg
                                className="h-6 w-6"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                                />
                              </svg>
                            </div>
                            <div>
                              <div className="flex items-center">
                                <h3 className="text-2xl font-bold mr-2">
                                  Colis #{order.reference}
                                </h3>
                                <button
                                  onClick={(e) => toggleSaveOrder(e)}
                                  className="text-white hover:text-white/80 focus:outline-none"
                                  title={
                                    isOrderSaved
                                      ? "Retirer des favoris"
                                      : "Ajouter aux favoris"
                                  }
                                >
                                  {isOrderSaved ? (
                                    <RiBookmark3Line className="h-6 w-6 fill-current" />
                                  ) : (
                                    <RiBookmarkLine className="h-6 w-6" />
                                  )}
                                </button>
                              </div>
                              <div className="flex items-center text-sm text-white/80 mt-1">
                                <svg
                                  className="h-4 w-4 mr-1"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                                  />
                                </svg>
                                Chine
                                <svg
                                  className="h-4 w-4 mx-1"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                                  />
                                </svg>
                                <svg
                                  className="h-4 w-4 mr-1"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"
                                  />
                                </svg>
                                Burkina Faso
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4 md:mt-0">
                          <span className="inline-flex items-center px-4 py-1.5 rounded-full text-sm font-semibold bg-white text-[#004d25]">
                            <span className="relative flex h-2 w-2 mr-2">
                              <span
                                className={`animate-ping absolute inline-flex h-full w-full rounded-full ${order.status === "DELIVERED"
                                    ? "bg-green-400"
                                    : "bg-[#004d25]"
                                  } opacity-75`}
                              ></span>
                              <span
                                className={`relative inline-flex rounded-full h-2 w-2 ${order.status === "DELIVERED"
                                    ? "bg-green-500"
                                    : "bg-[#004d25]"
                                  }`}
                              ></span>
                            </span>
                            {order.status === "ORDER_CONFIRMED"
                              ? "Commande Confirmée"
                              : order.status === "PROCESSING"
                                ? "En Traitement"
                                : order.status === "IN_TRANSIT"
                                  ? "En Transit"
                                  : order.status === "ARRIVED"
                                    ? "Arrivée au Burkina"
                                    : order.status === "DELIVERED"
                                      ? "Livrée"
                                      : String(order.status).replace("_", " ")}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Main tracking content */}
                    <div className="bg-white p-5 md:p-6">
                      {/* Delivery info section with icons */}
                      <div className="grid md:grid-cols-2 gap-6 p-4 bg-gray-50 rounded-xl border border-gray-100 mb-6">
                        {/* Estimated delivery */}
                        <div className="flex items-start">
                          <div className="bg-[#004d25]/10 p-2 rounded-lg mr-3">
                            <svg
                              className="h-5 w-5 text-[#004d25]"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                              />
                            </svg>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">
                              Date de livraison estimée
                            </p>
                            <p className="text-base font-semibold text-gray-900">
                              {formatDate(order.estimatedDelivery)}
                            </p>
                          </div>
                        </div>

                        {/* Tracking number */}
                        <div className="flex items-start">
                          <div className="bg-[#004d25]/10 p-2 rounded-lg mr-3">
                            <svg
                              className="h-5 w-5 text-[#004d25]"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
                              />
                            </svg>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">
                              Numéro de suivi
                            </p>
                            <p className="text-base font-semibold text-gray-900">
                              {order.reference}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Save Order Button */}
                      {user && (
                        <div className="mt-6 mb-4 flex justify-end">
                          <Button
                            variant={isOrderSaved ? "default" : "outline"}
                            className={
                              isOrderSaved
                                ? "bg-[#004d25] hover:bg-[#003d1e] text-white flex items-center space-x-1"
                                : "border-[#004d25] text-[#004d25] hover:bg-[#004d25] hover:text-white flex items-center space-x-1"
                            }
                            onClick={(e) => toggleSaveOrder(e)}
                          >
                            {isOrderSaved ? (
                              <>
                                <RiBookmark3Line className="h-4 w-4 mr-2" />
                                <span>Commande sauvegardée</span>
                              </>
                            ) : (
                              <>
                                <RiBookmarkLine className="h-4 w-4 mr-2" />
                                <span>Sauvegarder cette commande</span>
                              </>
                            )}
                          </Button>
                        </div>
                      )}

                      {/* Status title */}
                      <div className="mb-2">
                        <h4 className="text-lg font-semibold text-gray-900">
                          Statut de l'expédition
                        </h4>
                        <p className="text-sm text-gray-500">
                          Suivez les étapes de votre commande en temps réel
                        </p>
                      </div>

                      {/* Tracking Timeline */}
                      <div className="my-8">
                        <ProgressSteps steps={orderStatusToSteps(order)} />
                      </div>

                      {/* Latest Update with improved styling */}
                      {order.latestUpdate && (
                        <div className="mt-6">
                          <h4 className="text-lg font-semibold text-gray-900 mb-3">
                            Dernière mise à jour
                          </h4>
                          <div className="bg-[#f8fcf9] border border-[#c7e6d3] rounded-xl p-4">
                            <div className="flex items-start">
                              <div className="mr-4 mt-0.5">
                                <div className="bg-[#004d25] rounded-full p-2 shadow-sm">
                                  <svg
                                    className="h-5 w-5 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                </div>
                              </div>
                              <div>
                                <p className="text-[#004d25] font-medium mb-2">
                                  {order.status === "ORDER_CONFIRMED"
                                    ? "Commande confirmée et en attente de traitement"
                                    : order.status === "PROCESSING"
                                      ? "Votre colis est en cours de préparation"
                                      : order.status === "IN_TRANSIT"
                                        ? "Votre colis est en route"
                                        : order.status === "ARRIVED"
                                          ? "Votre colis est arrivé au Burkina Faso"
                                          : order.status === "DELIVERED"
                                            ? "Votre colis a été livré avec succès"
                                            : "Mise à jour du statut"}
                                </p>
                                <p className="text-gray-700">
                                  {order.latestUpdate}
                                </p>
                                <div className="flex items-center mt-3 text-xs text-gray-500">
                                  <svg
                                    className="h-4 w-4 mr-1 text-gray-400"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  {formatDate(order.latestUpdateTime)} à{" "}
                                  {formatTime(order.latestUpdateTime)}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Assistance section */}
                      <div className="mt-8 border-t border-gray-100 pt-6">
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-500">
                            Besoin d'aide?
                          </p>
                          <a
                            href={`https://wa.me/${whatsappSetting?.value || "+33783928889"}`}
                            target="_blank"
                            className="inline-flex items-center text-[#004d25] font-medium text-sm hover:text-[#003d1e]"
                          >
                            <svg
                              className="h-4 w-4 mr-1"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                              />
                            </svg>
                            Contactez nous
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Product Section */}
          <div className={`px-4 sm:px-0 mt-6 ${order ? "mt-8" : ""}`}>
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-neutral-800">
                  Nos produits
                </h2>
                <Link
                  href="/products"
                  className="text-[#004d25] font-medium hover:text-[#003d1e]"
                >
                  Voir tout
                </Link>
              </div>

              {productsLoading ? (
                <div className="flex justify-center py-10">
                  <p>Chargement des produits...</p>
                </div>
              ) : (
                <>
                  {/* Products Grid - First Row */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    {products
                      ?.slice(0, 3)
                      .map((product) => (
                        <ProductCard key={product.id} product={product} />
                      ))}
                  </div>

                  {/* Products Grid - Second Row */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {products
                      ?.slice(3, 6)
                      .map((product) => (
                        <ProductCard key={product.id} product={product} />
                      ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white text-[#004d25] border-t border-gray-200">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col items-center justify-center">
            <div className="flex justify-center mb-6">
              <Logo size="medium" withText={true} />
            </div>
            <div className="flex justify-center space-x-6">
              <Link
                href="/about"
                className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))] text-sm"
              >
                À Propos
              </Link>
              <Link
                href="/support"
                className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))] text-sm"
              >
                Support
              </Link>
              <Link
                href="/contact"
                className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))] text-sm"
              >
                Contact
              </Link>
              <Link
                href="/privacy"
                className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))] text-sm"
              >
                Confidentialité
              </Link>
              {user && user.username === "admin" && (
                <Link
                  href="/admin"
                  className="text-[#004d25] hover:text-[hsl(var(--waabo-yellow))] text-sm"
                >
                  Admin
                </Link>
              )}
            </div>
          </div>
          <div className="mt-6 text-center text-sm text-[#004d25]">
            <p>
              &copy; {new Date().getFullYear()} Waabo Express. Tous droits
              réservés.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
