import { ReactNode } from "react";
import { Link, useLocation } from "wouter";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { RiArrowLeftSLine, RiLogoutBoxRLine } from "react-icons/ri";
import { useToast } from "@/hooks/use-toast";
import { Capacitor } from "@capacitor/core";

interface PublicLayoutProps {
  children: ReactNode;
}

export default function PublicLayout({ children }: PublicLayoutProps) {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Vérifier si l'utilisateur est connecté
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await fetch('/api/user');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        }
      } catch (error) {
        console.error("Erreur lors de la vérification de l'authentification:", error);
      } finally {
        setLoading(false);
      }
    }

    checkAuth();
  }, []);

  // Fonction de déconnexion
  const handleLogout = async (): Promise<void> => {
    try {
      // Logout from social auth services (mobile only)
      if (Capacitor.isNativePlatform()) {
        try {
          const { capacitorGoogleAuth } = await import('@/services/capacitorGoogleAuth');
          const { capacitorAppleAuth } = await import('@/services/capacitorAppleAuth');
          const { capacitorFacebookAuth } = await import('@/services/capacitorFacebookAuth');

          await Promise.allSettled([
            capacitorGoogleAuth.logout(),
            capacitorAppleAuth.logout(),
            capacitorFacebookAuth.logout()
          ]);

          console.log('Social auth services logged out');
        } catch (socialLogoutError) {
          console.warn('Error during social auth logout:', socialLogoutError);
        }
      }

      const response = await fetch('/api/auth/logout');
      if (response.ok) {
        setUser(null);

        // Clear ALL persistent storage items (mobile)
        localStorage.removeItem('waabo_user');
        localStorage.removeItem('waabo_jwt_token');
        localStorage.removeItem('authToken');

        // Clear Zustand auth storage
        localStorage.removeItem('waabo-auth-storage');

        console.log('All authentication data cleared from localStorage');

        toast({
          title: "Déconnexion réussie",
          description: "Vous avez été déconnecté avec succès",
          variant: "default",
        });
        setLocation('/');
      }
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
      toast({
        title: "Erreur de déconnexion",
        description: "Une erreur est survenue lors de la déconnexion",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header for mobile - avec position fixe */}
      <header className="md:hidden bg-white border-b py-3 fixed top-0 left-0 right-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Link href="/" className="flex items-center text-[#004d25] hover:text-[#003d1e]">
            <RiArrowLeftSLine className="h-6 w-6 mr-1" />
            <span>Retour</span>
          </Link>

          {/* Le menu latéral a été supprimé */}
        </div>
      </header>

      {/* Header for desktop - avec position fixe */}
      <header className="hidden md:block bg-white border-b py-4 fixed top-0 left-0 right-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Link href="/" className="flex items-center text-[#004d25] hover:text-[#003d1e]">
            <RiArrowLeftSLine className="h-6 w-6 mr-1" />
            <span>Retour</span>
          </Link>

          <div className="flex items-center gap-6">
            {/* La navigation a été supprimée */}

            {/* Bouton de connexion uniquement quand l'utilisateur n'est pas connecté */}
            {!loading && !user && (
              <Link href="/auth">
                <Button>Se connecter</Button>
              </Link>
            )}

            {/* Bouton de déconnexion quand l'utilisateur est connecté */}
            {!loading && user && (
              <Button
                variant="outline"
                onClick={handleLogout}
                className="flex items-center gap-2"
              >
                <RiLogoutBoxRLine className="h-4 w-4" />
                <span>Déconnexion</span>
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Espace pour compenser la hauteur du header fixe */}
      <div className="h-14 md:h-16"></div>

      {/* Main content */}
      <main className="flex-grow">
        {children}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
}