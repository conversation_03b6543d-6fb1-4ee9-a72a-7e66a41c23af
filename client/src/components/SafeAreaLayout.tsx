import React from 'react';

interface SafeAreaLayoutProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Layout component that ensures content is within safe area on mobile devices
 * Handles notch devices and provides consistent padding across all pages
 */
export const SafeAreaLayout: React.FC<SafeAreaLayoutProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`safe-area-layout ${className}`}>
      {children}
    </div>
  );
};

export default SafeAreaLayout;
