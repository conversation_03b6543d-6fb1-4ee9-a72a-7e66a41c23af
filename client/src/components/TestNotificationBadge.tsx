import React, { useEffect } from 'react';
import { useNotifications } from '@/hooks/use-notifications';

// Ce composant est uniquement destiné à tester l'affichage des badges de notification
export default function TestNotificationBadge() {
  const { unreadCount } = useNotifications();
  
  // Ajouter un style global dès que le composant est monté
  useEffect(() => {
    // Fonction pour positionner le badge par rapport à l'icône de notification
    const positionBadgeOnIcon = () => {
      // Essayer de trouver l'icône de la cloche (Bell) de plusieurs façons
      let bellIcon = document.querySelector('.md\\:block button svg'); // Pour le desktop header
      
      // Si on ne trouve pas avec le premier sélecteur, essayer un autre
      if (!bellIcon) {
        bellIcon = document.querySelector('header button svg');
      }
      
      // Si on ne trouve toujours pas, essayer spécifiquement par le path de l'icône Bell
      if (!bellIcon) {
        bellIcon = document.querySelector('svg[d*="M21 20H3a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1h1l2-4h12l2 4h1a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1Zm-11-9a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3Z"]');
      }
      
      const fixedBadge = document.getElementById('notification-fixed-badge');
      
      if (bellIcon && fixedBadge) {
        const bellRect = bellIcon.getBoundingClientRect();
        
        // Positionner le badge sur l'icône de notification
        fixedBadge.style.top = (bellRect.top - 8) + 'px';
        fixedBadge.style.left = (bellRect.right - 8) + 'px';
        
        console.log("Badge positionné sur l'icône:", bellRect);
      } else {
        console.log("Impossible de trouver l'icône Bell ou le badge", { bellIcon, fixedBadge });
      }
    };
    
    // Créer un élément style
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      /* Badge de notification fixe */
      .fixed-notification-badge {
        position: fixed;
        background-color: #ef4444;
        color: white;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        z-index: 9999;
        pointer-events: none;
      }
    `;
    
    // Ajouter le style au head du document
    document.head.appendChild(styleElement);
    
    // Créer un badge fixe seulement s'il y a des notifications non lues
    if (unreadCount > 0) {
      const fixedBadge = document.createElement('div');
      fixedBadge.id = 'notification-fixed-badge';
      fixedBadge.className = 'fixed-notification-badge';
      fixedBadge.textContent = unreadCount.toString();
      document.body.appendChild(fixedBadge);
      
      // Positionner le badge immédiatement
      positionBadgeOnIcon();
    }
    
    // Mettre à jour et repositionner le badge lors du défilement
    window.addEventListener('scroll', positionBadgeOnIcon);
    window.addEventListener('resize', positionBadgeOnIcon);
    
    // Ajouter un log pour vérifier que le composant est bien monté
    console.log("TestNotificationBadge monté - unreadCount:", unreadCount);
    
    // Mettre à jour le badge toutes les secondes et lors du défilement
    const intervalId = setInterval(() => {
      const fixedBadge = document.getElementById('notification-fixed-badge');
      
      // Si l'élément existe déjà, mettre à jour son contenu
      if (fixedBadge) {
        fixedBadge.textContent = unreadCount.toString();
        positionBadgeOnIcon();
        
        // Si plus de notifications, supprimer le badge
        if (unreadCount <= 0) {
          fixedBadge.remove();
        }
      } 
      // Si l'élément n'existe pas mais qu'il y a des notifications non lues, créer le badge
      else if (unreadCount > 0) {
        const newBadge = document.createElement('div');
        newBadge.id = 'notification-fixed-badge';
        newBadge.className = 'fixed-notification-badge';
        newBadge.textContent = unreadCount.toString();
        document.body.appendChild(newBadge);
        positionBadgeOnIcon();
      }
    }, 500);
    
    // Nettoyer lors du démontage du composant
    return () => {
      window.removeEventListener('scroll', positionBadgeOnIcon);
      window.removeEventListener('resize', positionBadgeOnIcon);
      
      const fixedBadge = document.getElementById('notification-fixed-badge');
      if (fixedBadge && fixedBadge.parentNode) {
        fixedBadge.parentNode.removeChild(fixedBadge);
      }
      
      if (styleElement && styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement);
      }
      
      clearInterval(intervalId);
    };
  }, [unreadCount]);
  
  return null; // Ce composant ne rend rien visuellement
}