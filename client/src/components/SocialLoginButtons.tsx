import { <PERSON><PERSON> } from "@/components/ui/button";
import { FaFacebook, FaWhatsapp, FaApple } from "react-icons/fa";
import { FcGoogle } from "react-icons/fc";
import { HelpCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Link, useLocation } from "wouter";
import { useEffect, useState } from "react";
import { Capacitor } from '@capacitor/core';
import AppFacebookLogin from './AppFacebookLogin';
import AppGoogleLogin from './AppGoogleLogin';
import { AppAppleLogin } from './AppAppleLogin';

// Déclarer la propriété startAuthCheck sur l'interface Window
declare global {
  interface Window {
    startAuthCheck: (authWindow?: Window | null) => void;
  }
}

export const SocialLoginButtons = () => {
  const { toast } = useToast();
  const [_, setLocation] = useLocation();
  // Vérifier si nous sommes sur une plateforme native (iOS/Android)
  const [isNativePlatform, setIsNativePlatform] = useState(false);

  // Détecter si nous sommes sur une plateforme native au chargement
  useEffect(() => {
    setIsNativePlatform(Capacitor.isNativePlatform());
  }, []);

  // Vérifier l'état de l'authentification toutes les 2 secondes après ouverture de la popup
  useEffect(() => {
    let authCheckInterval: number | null = null;
    let authPopupOpened = false;
    let popupWindow: Window | null = null;

    // Fonction pour vérifier si l'utilisateur est authentifié
    const checkAuthStatus = async () => {
      try {
        const response = await fetch('/api/user');
        const data = await response.json();

        // Si l'utilisateur est authentifié et que la popup a été ouverte
        if (response.ok && data && data.id && authPopupOpened) {
          // Arrêter les vérifications
          if (authCheckInterval) {
            window.clearInterval(authCheckInterval);
            authCheckInterval = null;
          }

          // Fermer la popup si elle existe toujours
          if (popupWindow && !popupWindow.closed) {
            popupWindow.close();
          }

          // Notification et redirection
          toast({
            title: 'Connexion réussie',
            description: `Bienvenue, ${data.firstName || data.username}!`,
          });

          // Rediriger EXPLICITEMENT vers la page d'accueil et forcer la navigation
          window.location.href = '/';
        }
      } catch (error) {
        console.error('Erreur lors de la vérification de l\'authentification:', error);
      }
    };

    // Fonction à exécuter lors de l'ouverture de la popup d'authentification
    window.startAuthCheck = (authWindow?: Window | null) => {
      authPopupOpened = true;

      // Stocker la référence de la fenêtre popup
      if (authWindow) {
        popupWindow = authWindow;
      }

      // Arrêter tout intervalle précédent
      if (authCheckInterval) {
        window.clearInterval(authCheckInterval);
      }

      // Commencer les vérifications toutes les 2 secondes
      authCheckInterval = window.setInterval(checkAuthStatus, 2000);

      // Arrêter après 2 minutes (120 secondes) si pas de connexion
      setTimeout(() => {
        if (authCheckInterval) {
          window.clearInterval(authCheckInterval);
          authCheckInterval = null;

          if (authPopupOpened) {
            // Fermer la popup si elle existe toujours
            if (popupWindow && !popupWindow.closed) {
              popupWindow.close();
            }

            toast({
              title: 'Délai expiré',
              description: 'La tentative de connexion a expiré. Veuillez réessayer.',
              variant: 'destructive',
            });
          }
        }
      }, 120000);
    };

    // Nettoyage à la destruction du composant
    return () => {
      if (authCheckInterval) {
        window.clearInterval(authCheckInterval);
      }
    };
  }, [toast, setLocation]);

  const handleSocialLogin = async (provider: string) => {
    if (provider === 'Facebook') {
      try {
        // Notification pour l'utilisateur
        toast({
          title: 'Connexion Facebook',
          description: 'Redirection vers Facebook pour authentification...',
        });

        // Ouvrir dans une nouvelle fenêtre plutôt que de rediriger
        console.log('Initialisation de l\'authentification Facebook');

        // Dans l'environnement Replit, ouvrir dans une nouvelle fenêtre
        const fbAuthWindow = window.open('/api/auth/facebook', '_blank', 'width=600,height=700');

        // Démarrer la vérification d'authentification si la fenêtre est ouverte
        if (fbAuthWindow) {
          window.startAuthCheck(fbAuthWindow);
        }

        // Si fenêtre bloquée, proposer la redirection directe
        if (!fbAuthWindow) {
          toast({
            title: 'Popup bloqué',
            description: 'Veuillez autoriser les popups ou cliquer sur "OK" pour rediriger directement',
            variant: 'destructive',
          });

          // Attendre confirmation pour rediriger
          if (confirm('Le popup a été bloqué. Voulez-vous être redirigé directement vers Facebook pour vous connecter?')) {
            window.location.href = '/api/auth/facebook';
          }
        }
      } catch (error) {
        console.error('Erreur auth Facebook:', error);
        toast({
          title: 'Erreur',
          description: 'Impossible de se connecter avec Facebook',
          variant: 'destructive',
        });
      }
    } else if (provider === 'Google') {
      try {
        const response = await fetch('/api/auth/google');
        const data = await response.json();
        console.log('URL Google Auth:', data.url);

        toast({
          title: 'Connexion Google',
          description: 'Redirection vers Google pour authentification...',
        });

        // Ouvrir dans une fenêtre popup plutôt qu'un nouvel onglet
        const googleAuthWindow = window.open(data.url, '_blank', 'width=600,height=700');

        // Démarrer la vérification d'authentification si la fenêtre est ouverte
        if (googleAuthWindow) {
          window.startAuthCheck(googleAuthWindow);
        }

        // Si fenêtre bloquée, proposer la redirection directe
        if (!googleAuthWindow) {
          toast({
            title: 'Popup bloqué',
            description: 'Veuillez autoriser les popups ou cliquer sur "OK" pour rediriger directement',
            variant: 'destructive',
          });

          // Attendre confirmation pour rediriger
          if (confirm('Le popup a été bloqué. Voulez-vous être redirigé directement vers Google pour vous connecter?')) {
            window.location.href = data.url;
          }
        }
      } catch (error) {
        console.error('Erreur auth Google:', error);
        toast({
          title: 'Erreur',
          description: 'Impossible de se connecter avec Google',
          variant: 'destructive',
        });
      }
    } else if (provider === 'Apple') {
      try {
        // Notification pour l'utilisateur
        toast({
          title: 'Connexion Apple',
          description: 'Redirection vers Apple pour authentification...',
        });

        // Ouvrir dans une nouvelle fenêtre plutôt que de rediriger
        console.log('Initialisation de l\'authentification Apple');

        // Dans l'environnement Replit, ouvrir dans une nouvelle fenêtre
        const appleAuthWindow = window.open('/api/auth/apple', '_blank', 'width=600,height=700');

        // Démarrer la vérification d'authentification si la fenêtre est ouverte
        if (appleAuthWindow) {
          window.startAuthCheck(appleAuthWindow);
        }

        // Si fenêtre bloquée, proposer la redirection directe
        if (!appleAuthWindow) {
          toast({
            title: 'Popup bloqué',
            description: 'Veuillez autoriser les popups ou cliquer sur "OK" pour rediriger directement',
            variant: 'destructive',
          });

          // Attendre confirmation pour rediriger
          if (confirm('Le popup a été bloqué. Voulez-vous être redirigé directement vers Apple pour vous connecter?')) {
            window.location.href = '/api/auth/apple';
          }
        }
      } catch (error) {
        console.error('Erreur auth Apple:', error);
        toast({
          title: 'Erreur',
          description: 'Impossible de se connecter avec Apple',
          variant: 'destructive',
        });
      }
    } else {
      toast({
        title: `Connexion via ${provider}`,
        description: 'Cette fonctionnalité sera bientôt disponible',
      });
    }
  };

  return (
    <div className="space-y-3 w-full">
      {/* Facebook Login Button - Conditionnel selon la plateforme */}
      {isNativePlatform ? (
        /* Bouton Facebook natif pour les applications mobiles */
        <AppFacebookLogin
          onLoginSuccess={(userData) => {
            console.log('Facebook login success in SocialLoginButtons:', userData);
            // Optionnel: Vous pouvez ajouter d'autres actions ici si nécessaire
          }}
        />
      ) : (
        /* Bouton Facebook standard pour le web */
        <Button
          type="button"
          variant="outline"
          className="w-full rounded-lg py-6 border bg-white border-gray-300 hover:bg-gray-50 transition-all h-[48px] group"
          onClick={() => handleSocialLogin('Facebook')}
        >
          <div className="flex items-center justify-center w-full">
            <div className="flex items-center justify-center mr-3">
              <FaFacebook className="h-6 w-6 text-[#1877F2]" />
            </div>
            <span className="text-gray-700 font-medium">
              Continuer avec Facebook
            </span>
          </div>
        </Button>
      )}

      {/* Google Login Button - Conditionnel selon la plateforme */}
      {isNativePlatform ? (
        /* Bouton Google natif pour les applications mobiles */
        <AppGoogleLogin
          onLoginSuccess={(userData) => {
            console.log('Google login success in SocialLoginButtons:', userData);
            // Optionnel: Vous pouvez ajouter d'autres actions ici si nécessaire
          }}
        />
      ) : (
        /* Bouton Google standard pour le web */
        <Button
          type="button"
          variant="outline"
          className="w-full rounded-lg py-6 border bg-white border-gray-300 hover:bg-gray-50 transition-all h-[48px] group"
          onClick={() => handleSocialLogin('Google')}
        >
          <div className="flex items-center justify-center w-full">
            <div className="flex items-center justify-center mr-3">
              <FcGoogle className="h-6 w-6" />
            </div>
            <span className="text-gray-700 font-medium">
              Continuer avec Google
            </span>
          </div>
        </Button>
      )}

      {/* Apple Login Button - Conditionnel selon la plateforme */}
      {isNativePlatform ? (
        /* Bouton Apple natif pour les applications mobiles */
        <AppAppleLogin
          className="w-full rounded-lg py-6 border bg-white border-gray-300 hover:bg-gray-50 transition-all h-[48px] group"
        />
      ) : (
        /* Bouton Apple standard pour le web */
        <Button
          type="button"
          variant="outline"
          className="w-full rounded-lg py-6 border bg-white border-gray-300 hover:bg-gray-50 transition-all h-[48px] group"
          onClick={() => handleSocialLogin('Apple')}
        >
          <div className="flex items-center justify-center w-full">
            <div className="flex items-center justify-center mr-3">
              <FaApple className="h-6 w-6 text-black" />
            </div>
            <span className="text-gray-700 font-medium">
              Continuer avec Apple
            </span>
          </div>
        </Button>
      )}

      {/* WhatsApp Login Button - Déplacé en dernier */}
      <Button
        type="button"
        variant="outline"
        className="w-full rounded-lg py-6 border bg-white border-gray-300 hover:bg-gray-50 transition-all h-[48px] group"
        onClick={() => handleSocialLogin('WhatsApp')}
      >
        <div className="flex items-center justify-center w-full">
          <div className="flex items-center justify-center mr-3">
            <FaWhatsapp className="h-6 w-6 text-[#25D366]" />
          </div>
          <span className="text-gray-700 font-medium">
            Continuer avec WhatsApp
          </span>
        </div>
      </Button>
    </div>
  );
};

export default SocialLoginButtons;