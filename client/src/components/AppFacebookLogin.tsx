import React, { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';
import { Button } from '@/components/ui/button';
import { RiFacebookFill } from 'react-icons/ri';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { capacitorFacebookAuth } from '@/services/capacitorFacebookAuth';
import { queryClient } from '@/lib/queryClient';

interface AppFacebookLoginProps {
  onLoginStart?: () => void;
  onLoginSuccess?: (userData: any) => void;
  onLoginFailure?: (error: Error) => void;
}

const AppFacebookLogin: React.FC<AppFacebookLoginProps> = ({
  onLoginStart,
  onLoginSuccess,
  onLoginFailure
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [, setLocation] = useLocation();
  const isNative = Capacitor.isNativePlatform();

  // Initialiser le SDK Facebook lors du chargement du composant
  useEffect(() => {
    if (isNative) {
      capacitorFacebookAuth.initialize()
        .catch(error => {
          console.error('Erreur lors de l\'initialisation de Facebook SDK:', error);
        });
    }
  }, []);

  // Gérer la connexion Facebook via l'application mobile
  const handleAppFacebookLogin = async () => {
    if (!isNative) {
      // Rediriger vers la connexion web standard si nous ne sommes pas sur une plateforme native
      handleWebFacebookLogin();
      return;
    }

    setIsLoading(true);
    onLoginStart?.();

    try {
      toast({
        title: 'Connexion Facebook',
        description: 'Connexion en cours via l\'application Facebook...',
      });

      // Effectuer la connexion via le plugin Capacitor
      const result = await capacitorFacebookAuth.login();

      console.log('=== RÉSULTAT COMPLET DE FACEBOOK LOGIN ===');
      console.log('Result:', result);
      console.log('Result type:', typeof result);
      console.log('Result keys:', result ? Object.keys(result) : 'null');

      if (result) {
        console.log('=== FACEBOOK LOGIN RESULT DEBUG ===');
        console.log('Full result:', JSON.stringify(result, null, 2));
        console.log('Access Token:', result.accessToken);
        console.log('Access Token type:', typeof result.accessToken);
        console.log('Access Token keys:', Object.keys(result.accessToken || {}));
        console.log('=== END FACEBOOK DEBUG ===');
      }

      if (!result || !result.accessToken || !result.userInfo) {
        throw new Error('Échec de la connexion Facebook');
      }

      // Facebook Login returns userInfo directly in result.userInfo
      const userInfo = {
        id: result.userInfo.id,
        name: result.userInfo.name,
        email: result.userInfo.email || '', // May not be available
        first_name: result.userInfo.firstName || result.userInfo.name?.split(' ')[0] || '',
        last_name: result.userInfo.lastName || result.userInfo.name?.split(' ').slice(1).join(' ') || ''
      };

      // Envoyer le token et les informations utilisateur au serveur
      const tokenToSend = result.accessToken; // Direct string token
      console.log('Envoi des données Facebook au serveur:', {
        accessToken: typeof tokenToSend === 'string' ? tokenToSend.substring(0, 20) + '...' : tokenToSend,
        userInfo: userInfo
      });

      // Use environment-based API URL
      const apiUrl = `${import.meta.env.VITE_API_URL || 'https://waabo-app.com'}/api/auth/facebook-app`;

      console.log('API URL:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Origin': 'http://10.0.2.2:5174',
        },
        mode: 'cors',
        credentials: 'omit',
        body: JSON.stringify({
          accessToken: tokenToSend,
          userInfo: userInfo,
        }),
      });

      console.log('Réponse du serveur:', response.status, response.statusText);


      if (!response.ok) {
        const errorText = await response.text();
        console.error('Erreur serveur:', errorText);
        throw new Error(`Échec de l\'authentification côté serveur: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log("=== RESPONSE DETAILS ===");
      console.log("Data:", data);
      console.log("Data type:", typeof data);
      console.log("Data keys:", data ? Object.keys(data) : 'null');
      console.log("Data stringified:", JSON.stringify(data, null, 2));
      console.log("=== END RESPONSE DETAILS ===");

      // Stocker le token JWT dans le localStorage
      if (data.token) {
        console.log('Storing JWT token in localStorage...');
        localStorage.setItem('authToken', data.token);
        console.log('JWT token stored successfully');
      }

      // Mettre à jour le cache React Query avec les données utilisateur
      if (data.user) {
        console.log('Updating React Query cache with user data...');
        queryClient.setQueryData(["/api/user"], data.user);
        console.log('React Query cache updated successfully');
      }

      toast({
        title: 'Connexion réussie',
        description: 'Vous êtes maintenant connecté avec Facebook',
      });

      // Si un callback est fourni, l'appeler avec les données
      console.log('Calling onLoginSuccess callback...');
      onLoginSuccess?.(data);

      // Rediriger vers la page d'accueil
      console.log('Redirecting to home page...');
      setLocation('/');
      console.log('setLocation called successfully');
    } catch (error) {
      console.error('Erreur lors de la connexion Facebook:', error);
      console.error('Error type:', typeof error);
      console.error('Error name:', (error as Error)?.name);
      console.error('Error message:', (error as Error)?.message);
      console.error('Error stack:', (error as Error)?.stack);

      let errorMessage = 'Impossible de se connecter avec Facebook';
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Erreur réseau: Impossible de contacter le serveur';
      }

      toast({
        title: 'Erreur',
        description: errorMessage,
        variant: 'destructive',
      });

      onLoginFailure?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  };

  // Gérer la connexion Facebook via le navigateur web
  const handleWebFacebookLogin = () => {
    try {
      toast({
        title: 'Connexion Facebook',
        description: 'Redirection vers Facebook pour authentification...',
      });

      // Ouvrir dans une nouvelle fenêtre plutôt que de rediriger
      console.log('Initialisation de l\'authentification Facebook web');

      // Dans l'environnement Replit, ouvrir dans une nouvelle fenêtre
      const fbAuthWindow = window.open('/api/auth/facebook', '_blank', 'width=600,height=700');

      // Démarrer la vérification d'authentification si la fenêtre est ouverte
      if (fbAuthWindow && window.startAuthCheck) {
        window.startAuthCheck(fbAuthWindow);
      }

      // Si fenêtre bloquée, proposer la redirection directe
      if (!fbAuthWindow) {
        toast({
          title: 'Popup bloqué',
          description: 'Veuillez autoriser les popups ou cliquer sur "OK" pour rediriger directement',
          variant: 'destructive',
        });

        // Attendre confirmation pour rediriger
        if (confirm('Le popup a été bloqué. Voulez-vous être redirigé directement vers Facebook pour vous connecter?')) {
          window.location.href = '/api/auth/facebook';
        }
      }
    } catch (error) {
      console.error('Erreur auth Facebook:', error);

      toast({
        title: 'Erreur',
        description: 'Impossible de se connecter avec Facebook',
        variant: 'destructive',
      });

      onLoginFailure?.(error as Error);
    }
  };

  return (
    <Button
      variant="outline"
      className="w-full flex items-center justify-center gap-2 bg-[#1877F2] hover:bg-[#166FE5] text-white hover:text-white border-0"
      onClick={handleAppFacebookLogin}
      disabled={isLoading}
    >
      <RiFacebookFill className="h-5 w-5" />
      <span>Continuer avec Facebook</span>
    </Button>
  );
};

export default AppFacebookLogin;