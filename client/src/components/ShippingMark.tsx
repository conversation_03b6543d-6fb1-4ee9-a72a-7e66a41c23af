import React, { useRef, useEffect, useState } from 'react';
import { toPng } from 'html-to-image';
import { saveAs } from 'file-saver';
import JsBarcode from 'jsbarcode';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Loader2, Download, Share2 } from 'lucide-react';
import ReactCountryFlag from 'react-country-flag';
import waaboLogo from '@assets/waabologo.v2.png';

interface ShippingMarkProps {
  className?: string;
}

const ShippingMark = ({ className }: ShippingMarkProps) => {
  const { user } = useAuth();
  const [warehouseAddress, setWarehouseAddress] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userCode, setUserCode] = useState<string>('');
  const [barcode1Url, setBarcode1Url] = useState<string>('');
  const [barcode2Url, setBarcode2Url] = useState<string>('');
  const [isGeneratingImage, setIsGeneratingImage] = useState<boolean>(false);
  const markRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Récupérer l'adresse de l'entrepôt en Chine
    const fetchWarehouseAddress = async () => {
      try {
        const response = await fetch('/api/app-settings/warehouse_address');
        if (response.ok) {
          const data = await response.json();
          setWarehouseAddress(data.value);
        } else {
          setWarehouseAddress('Adresse non disponible');
        }
      } catch (error) {
        console.error('Erreur lors de la récupération de l\'adresse de l\'entrepôt:', error);
        setWarehouseAddress('Adresse non disponible');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWarehouseAddress();
  }, []);

  // Fonction pour générer une URL d'image de code-barres
  const generateBarcodeURL = (value: string, height: number, fontSize: number, displayValue: boolean = true): string => {
    try {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, value, {
        format: 'CODE128',
        displayValue: displayValue,
        fontSize: fontSize,
        height: height,
        margin: 5,
        background: '#FFFFFF',
        lineColor: '#000000',
      });
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('Erreur lors de la génération du code-barres:', error);
      return '';
    }
  };

  useEffect(() => {
    // Générer le code utilisateur unique et les codes-barres associés
    if (user) {
      try {
        // Générer un code incluant l'ID utilisateur et un timestamp pour l'unicité
        const timestamp = Date.now().toString().slice(-6);
        const generatedUserCode = `WAABO-${user.id}-${timestamp}`;
        console.log("Code utilisateur généré:", generatedUserCode);
        setUserCode(generatedUserCode);

        // Générer les URLs des codes-barres
        const url1 = generateBarcodeURL(generatedUserCode, 70, 14);
        const url2 = generateBarcodeURL(generatedUserCode, 50, 12);

        console.log("URL du code-barres 1 générée:", url1 ? "Succès" : "Échec");
        console.log("URL du code-barres 2 générée:", url2 ? "Succès" : "Échec");

        setBarcode1Url(url1);
        setBarcode2Url(url2);
      } catch (error) {
        console.error("Erreur lors de la génération des codes-barres:", error);
      }
    }
  }, [user]);

  // Fonction HTML vers image - convertir un élément HTML en image PNG avec DOM-to-image
  const createFullImage = async () => {
    if (!markRef.current) return null;

    try {
      console.log("Génération de l'image de la marque d'expédition...");

      // Préparer l'élément à capturer
      const originalParent = markRef.current.parentNode;
      const originalStyles = {
        width: markRef.current.style.width,
        position: markRef.current.style.position,
        zIndex: markRef.current.style.zIndex,
        margin: markRef.current.style.margin
      };

      // Créer un conteneur temporaire pour la capture
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.top = '-9999px';
      tempContainer.style.left = '-9999px';
      tempContainer.style.width = '600px';  // Largeur fixe assez grande
      document.body.appendChild(tempContainer);

      // Modifier temporairement l'élément pour la capture
      markRef.current.style.width = '100%';
      markRef.current.style.position = 'static';
      markRef.current.style.margin = '0 auto';

      // Déplacer l'élément dans le conteneur temporaire
      tempContainer.appendChild(markRef.current);

      // Attendre le rendu complet
      await new Promise(resolve => setTimeout(resolve, 100));

      // Capturer l'image
      const dataUrl = await toPng(markRef.current, {
        width: 550,
        height: 900,
        pixelRatio: 2,
        quality: 1,
        style: {
          padding: '20px',
          boxSizing: 'border-box',
          backgroundColor: '#FFFFFF',
        }
      });

      // Remettre l'élément à sa place
      if (originalParent) {
        originalParent.appendChild(markRef.current);
      }

      // Restaurer les styles originaux
      markRef.current.style.width = originalStyles.width;
      markRef.current.style.position = originalStyles.position;
      markRef.current.style.zIndex = originalStyles.zIndex;
      markRef.current.style.margin = originalStyles.margin;

      // Nettoyer
      document.body.removeChild(tempContainer);

      console.log("Génération d'image terminée");
      return dataUrl;
    } catch (error) {
      console.error('Erreur lors de la génération de l\'image:', error);
      return null;
    }
  };

  const handleDownload = async () => {
    setIsGeneratingImage(true);
    try {
      const dataUrl = await createFullImage();
      if (dataUrl) {
        saveAs(dataUrl, `waabo-shipping-mark-${user?.id || 'user'}.png`);
      } else {
        alert('Erreur lors de la génération de l\'image');
      }
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      alert('Erreur lors du téléchargement de l\'image');
    } finally {
      setIsGeneratingImage(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      setIsGeneratingImage(true);
      try {
        const dataUrl = await createFullImage();

        if (!dataUrl) {
          alert('Erreur lors de la génération de l\'image');
          return;
        }

        const blob = await (await fetch(dataUrl)).blob();
        const file = new File([blob], `waabo-shipping-mark-${user?.id || 'user'}.png`, {
          type: 'image/png',
        });

        await navigator.share({
          title: 'Ma marque d\'expédition Waabo',
          text: 'Voici ma marque d\'expédition pour Waabo Express',
          files: [file],
        });
      } catch (error) {
        console.error('Erreur lors du partage:', error);
        alert('Erreur lors du partage de l\'image');
      } finally {
        setIsGeneratingImage(false);
      }
    } else {
      alert('Le partage n\'est pas pris en charge sur votre appareil');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="w-8 h-8 text-[#004d25] animate-spin" />
      </div>
    );
  }

  // Vérifier si les codes-barres ont été générés
  if (userCode && (!barcode1Url || !barcode2Url)) {
    console.error("Échec de la génération des codes-barres");
  }

  return (
    <div className={className}>
      <div className="flex flex-col items-center">
        {/* Conteneur pour la marque d'expédition */}
        <div 
          ref={markRef} 
          className="bg-white border-2 border-gray-300 rounded-lg p-6 w-full max-w-md mx-auto shadow-md overflow-visible mb-8"
          style={{ 
            pageBreakInside: 'avoid', 
            breakInside: 'avoid',
            boxSizing: 'border-box',
            margin: '0 auto'
          }}
        >
          {/* En-tête avec logo */}
          <div className="flex justify-between items-center mb-6 border-b pb-4 w-full">
            {/* Logo direct pour un meilleur rendu lors du partage */}
            <div className="h-10">
              <img src={waaboLogo} alt="Waabo Logo" className="h-full" />
            </div>
            <div className="text-md font-semibold text-gray-700">
              Marque d'Expédition
            </div>
          </div>

          {/* Code-barres principal */}
          <div className="flex justify-center mb-6 w-full">
            {barcode1Url ? (
              <img 
                src={barcode1Url} 
                alt="Code-barres utilisateur" 
                className="max-w-full h-auto"
                style={{ margin: '0 auto' }}
              />
            ) : (
              <div className="bg-gray-100 h-16 w-64 flex items-center justify-center text-gray-500 mx-auto">
                Génération du code-barres...
              </div>
            )}
          </div>

          {/* Informations de l'utilisateur */}
          <div className="mb-6 w-full">
            <h3 className="text-md font-bold text-gray-800 uppercase mb-2">Informations du Destinataire:</h3>
            <div className="bg-gray-50 p-4 rounded-md border border-gray-200 w-full">
              <p className="text-lg font-semibold">
                {user?.firstName} {user?.lastName || user?.username}
              </p>
              <p className="text-md mt-1">
                {user?.email}
              </p>
              {user?.phone && (
                <p className="text-md">
                  {user.phone}
                </p>
              )}

              {/* Code-barres utilisateur sous les informations du destinataire */}
              
            </div>
          </div>

          {/* Adresse d'entrepôt */}
          <div className="mb-6 w-full">
            <h3 className="text-md font-bold text-gray-800 uppercase mb-2">Adresse d'Entrepôt en Chine:</h3>
            <div className="bg-gray-50 p-4 rounded-md border border-gray-200 w-full">
              <p className="text-md whitespace-pre-line leading-relaxed">{warehouseAddress}</p>
            </div>
          </div>

          {/* Pied de page */}
          <div className="mt-6 pt-4 border-t border-gray-200 text-center w-full">
            <p className="text-sm text-gray-600">© 2025 Waabo Express. Tous droits réservés.</p>

            {/* Drapeau de Burkina Faso avec le composant ReactCountryFlag */}
            <div className="mt-3 flex justify-center items-center gap-2">
              <ReactCountryFlag 
                countryCode="BF" 
                svg 
                style={{
                  width: '60px',
                  height: '40px',
                  border: '1px solid #e5e7eb'
                }}
                title="Burkina Faso"
              />
              <span className="text-sm font-medium text-gray-700">Burkina Faso</span>
            </div>
          </div>
        </div>

        {/* Boutons d'action - complètement séparés du contenu de l'image */}
        <div className="flex justify-center gap-4 mt-2 mb-6 w-full max-w-md">
          <Button
            onClick={handleDownload}
            className="flex items-center gap-2 bg-[#004d25] hover:bg-[#003d1e]"
            disabled={isGeneratingImage}
          >
            {isGeneratingImage ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Download className="w-4 h-4" />
            )}
            {isGeneratingImage ? "Préparation..." : "Télécharger"}
          </Button>
          <Button
            onClick={handleShare}
            variant="outline"
            className="flex items-center gap-2 border-[#004d25] text-[#004d25] hover:bg-[#e9f5ef]"
            disabled={isGeneratingImage}
          >
            {isGeneratingImage ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Share2 className="w-4 h-4" />
            )}
            {isGeneratingImage ? "Préparation..." : "Partager"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ShippingMark;