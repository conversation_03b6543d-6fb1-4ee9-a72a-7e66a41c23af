import { useEffect, useState } from 'react';
import { pushNotificationService } from '@/services/pushNotificationService';
import { useToast } from '@/hooks/use-toast';
import { PushNotificationSchema } from '@capacitor/push-notifications';
import { useAuth } from '@/stores/authStore';

interface PushNotificationManagerProps {
  children?: React.ReactNode;
}

/**
 * Composant qui gère l'initialisation et la configuration des notifications push
 * Ce composant doit être placé près de la racine de l'application pour initialiser
 * les notifications push dès le chargement de l'application
 */
export const PushNotificationManager: React.FC<PushNotificationManagerProps> = ({ children }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [initialized, setInitialized] = useState(false);

  // Gérer l'initialisation des notifications push
  useEffect(() => {
    const initializePushNotifications = async () => {
      // Ne pas initialiser si ce n'est pas un appareil mobile ou si déjà initialisé
      if (!pushNotificationService.isPushAvailable() || initialized) {
        return;
      }
      
      // Vérifier si l'utilisateur est connecté
      if (!user) {
        return;
      }

      try {
        console.log('Initialisation des notifications push...');
        const success = await pushNotificationService.initialize();
        
        if (success) {
          console.log('Notifications push initialisées avec succès');
          setInitialized(true);
          
          // Configurer les gestionnaires d'événements
          setupNotificationHandlers();
        } else {
          console.warn('Échec de l\'initialisation des notifications push');
        }
      } catch (error) {
        console.error('Erreur lors de l\'initialisation des notifications push:', error);
      }
    };

    // Configurer les gestionnaires pour les événements de notifications
    const setupNotificationHandlers = () => {
      // Gestionnaire pour les notifications reçues en premier plan
      pushNotificationService.onNotificationReceived((notification: PushNotificationSchema) => {
        console.log('Notification reçue en premier plan:', notification);
        
        // Afficher une notification toast pour l'utilisateur
        toast({
          title: notification.title || 'Nouvelle notification',
          description: notification.body || '',
          duration: 5000,
        });
      });

      // Gestionnaire pour les actions sur les notifications
      pushNotificationService.onNotificationActionPerformed((action) => {
        console.log('Action effectuée sur une notification:', action);
        
        // Traiter les actions spéciales si nécessaire
        const notification = action.notification;
        const data = notification.data;
        
        if (data?.type === 'order') {
          // Redirection vers la page de détail de commande
          window.location.href = `/orders/${data.orderId}`;
        } else if (data?.type === 'notification') {
          // Redirection vers la page des notifications
          window.location.href = '/notifications';
        }
      });
    };

    initializePushNotifications();
    
    // Nettoyer lors du démontage du composant
    return () => {
      // Rien à nettoyer pour l'instant car les écouteurs sont gérés par le service
    };
  }, [toast, user, initialized]);

  // Ce composant ne rend rien, il initialise simplement les notifications
  return <>{children}</>;
};