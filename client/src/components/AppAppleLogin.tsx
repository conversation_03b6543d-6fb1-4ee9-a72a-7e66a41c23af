import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { BsApple } from 'react-icons/bs';
import { capacitorAppleAuth } from '@/services/capacitorAppleAuth';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import axios from 'axios';
import { Capacitor } from '@capacitor/core';

// Propriétés du composant de connexion Apple pour l'application mobile
interface AppAppleLoginProps {
  onLoginSuccess?: (user: any) => void;
  className?: string;
  buttonText?: string;
  iconOnly?: boolean;
  redirect?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive' | null;
}

export function AppAppleLogin({
  onLoginSuccess,
  className = '',
  buttonText = 'Continuer avec Apple',
  iconOnly = false,
  redirect = '/',
  variant = 'outline',
}: AppAppleLoginProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [, setLocation] = useLocation();

  // Vérifier si nous sommes sur une plateforme native
  const isNative = Capacitor.isNativePlatform();

  // Si nous ne sommes pas sur une plateforme native, ne pas afficher le bouton Apple
  if (!isNative) {
    return null;
  }

  // Gestionnaire de connexion Apple
  const handleAppleLogin = async () => {
    setIsLoading(true);

    try {
      console.log('=== APPLE LOGIN STARTED ===');
      console.log('Is native platform:', isNative);

      // Use web Apple OAuth instead of Firebase (since Firebase needs Apple Developer config)
      const apiUrl = import.meta.env.VITE_API_URL || '';
      const appleAuthUrl = `${apiUrl}/api/auth/apple`;

      console.log('Redirecting to web Apple OAuth:', appleAuthUrl);
      window.location.href = appleAuthUrl;
      return;
    } catch (error) {
      console.error('Erreur de connexion Apple:', error);
      toast({
        title: 'Erreur de connexion',
        description: 'Une erreur est survenue lors de la connexion avec Apple ID',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      onClick={handleAppleLogin}
      disabled={isLoading}
      className={`${className} relative flex items-center justify-center gap-2`}
      type="button"
      aria-label="Se connecter avec Apple"
    >
      <BsApple className="h-5 w-5" />
      {!iconOnly && <span>{buttonText}</span>}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 dark:bg-black dark:bg-opacity-50 rounded-md">
          <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      )}
    </Button>
  );
}