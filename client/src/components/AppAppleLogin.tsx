import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { BsApple } from 'react-icons/bs';
import { capacitorAppleAuth } from '@/services/capacitorAppleAuth';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import axios from 'axios';
import { Capacitor } from '@capacitor/core';
import { queryClient } from '@/lib/queryClient';
// Propriétés du composant de connexion Apple pour l'application mobile
interface AppAppleLoginProps {
  onLoginSuccess?: (user: any) => void;
  className?: string;
  buttonText?: string;
  iconOnly?: boolean;
  redirect?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive' | null;
}

export function AppAppleLogin({
  onLoginSuccess,
  className = '',
  buttonText = 'Continuer avec Apple',
  iconOnly = false,
  redirect = '/',
  variant = 'outline',
}: AppAppleLoginProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [, setLocation] = useLocation();

  // Vérifier si nous sommes sur une plateforme native
  const isNative = Capacitor.isNativePlatform();

  // Si nous ne sommes pas sur une plateforme native, ne pas afficher le bouton Apple
  if (!isNative) {
    return null;
  }

  // Gestionnaire de connexion Apple
  const handleAppleLogin = async () => {
    setIsLoading(true);

    try {
      // Initialiser le service d'authentification Apple si nécessaire
      await capacitorAppleAuth.initialize();

      // Effectuer la connexion Apple
      const result = await capacitorAppleAuth.login();

      if (!result || !result.idToken) {
        toast({
          title: 'Erreur de connexion',
          description: 'Impossible de se connecter avec Apple ID',
          variant: 'destructive',
        });
        setIsLoading(false);
        return;
      }

      // Envoyer le token ID au serveur pour vérification et authentification
      const response = await axios.post('/api/auth/apple-app', {
        idToken: result.idToken,
        userInfo: result.userInfo
      }).then(res => res.data);

      // Si la connexion a réussi, mettre à jour l'état et rediriger
      if (response.success) {
        // Stocker le token JWT dans le localStorage
        const data = await response.json();
        // localStorage.setItem('authToken', response.token);

        // // Appeler le callback de réussite si fourni
        // if (onLoginSuccess) {
        //   onLoginSuccess(response.user);
        // }

        // toast({
        //   title: 'Connexion réussie',
        //   description: 'Vous êtes maintenant connecté avec Apple ID',
        // });

        // // Rediriger vers la page spécifiée
        // setLocation(redirect);

        localStorage.setItem('waabo_jwt_token', data.token);
        console.log('JWT token saved to localStorage');

        // Lưu user data vào React Query cache
        queryClient.setQueryData(["/api/user"], data.user);
        console.log('React Query cache updated successfully');

        // Lưu user data vào persistent storage (backup)
        localStorage.setItem('waabo_user', JSON.stringify(data.user));
        console.log('User data saved to persistent storage');

        toast({
          title: 'Connexion réussie',
          description: 'Vous êtes maintenant connecté avec Google',
        });

        // Si un callback est fourni, l'appeler avec les données
        onLoginSuccess?.(data);

        // Rediriger vers la page d'accueil
        setLocation('/');
      } else {
        toast({
          title: 'Erreur de connexion',
          description: response.message || 'Erreur lors de la connexion avec Apple ID',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Erreur de connexion Apple:', error);
      toast({
        title: 'Erreur de connexion',
        description: 'Une erreur est survenue lors de la connexion avec Apple ID',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      onClick={handleAppleLogin}
      disabled={isLoading}
      className={`${className} relative flex items-center justify-center gap-2`}
      type="button"
      aria-label="Se connecter avec Apple"
    >
      <BsApple className="h-5 w-5" />
      {!iconOnly && <span>{buttonText}</span>}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 dark:bg-black dark:bg-opacity-50 rounded-md">
          <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      )}
    </Button>
  );
}