import React from 'react';
import waaboLogo from '@assets/waabologo.v2.png';
import { Link } from 'wouter';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  withText?: boolean;
  className?: string;
  noLink?: boolean;
  linkTo?: string; // Destination de navigation personnalisée
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'medium', 
  withText = false,
  className = '',
  noLink = false,
  linkTo = '/' // Par défaut, rediriger vers la page d'accueil
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'h-10';
      case 'large':
        return 'h-32';
      case 'medium':
      default:
        return 'h-16';
    }
  };

  const logoContent = (
    <div className={`flex items-center ${className} cursor-pointer`}>
      <img 
        src={waaboLogo} 
        alt="Waabo Logo" 
        className={`${getSizeClasses()} object-contain`} 
      />
    </div>
  );

  if (noLink) {
    return logoContent;
  }
  
  // Utiliser Link pour une navigation côté client sans rechargement de la page
  return (
    <Link href={linkTo}>
      {logoContent}
    </Link>
  );
};

export default Logo;
