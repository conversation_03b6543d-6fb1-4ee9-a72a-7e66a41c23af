import React, { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';
import { Button } from '@/components/ui/button';
import { FcGoogle } from 'react-icons/fc';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { capacitorGoogleAuth } from '@/services/capacitorGoogleAuth';
import { queryClient } from '@/lib/queryClient';

interface AppGoogleLoginProps {
onLoginStart?: () => void;
onLoginSuccess?: (userData: any) => void;
onLoginFailure?: (error: Error) => void;
}

const AppGoogleLogin: React.FC<AppGoogleLoginProps> = ({
onLoginStart,
onLoginSuccess,
onLoginFailure
}) => {
const { toast } = useToast();
const [isLoading, setIsLoading] = useState(false);
const [, setLocation] = useLocation();
const isNative = Capacitor.isNativePlatform();

// Initialiser le SDK Firebase lors du chargement du composant
useEffect(() => {
  if (isNative) {
    capacitorGoogleAuth.initialize()
      .catch(error => {
        console.error('Erreur lors de l\'initialisation de Firebase SDK:', error);
      });
  }
}, []);

// Gérer la connexion Google via l'application mobile
const handleAppGoogleLogin = async () => {
  if (!isNative) {
    // Rediriger vers la connexion web standard si nous ne sommes pas sur une plateforme native
    handleWebGoogleLogin();
    return;
  }

  setIsLoading(true);
  onLoginStart?.();

  try {
    toast({
      title: 'Connexion Google',
      description: 'Connexion en cours via Google...',
    });

    // Effectuer la connexion via le plugin Capacitor
    const result = await capacitorGoogleAuth.login();

    if (!result || !result.idToken) {
      throw new Error('Échec de la connexion Google');
    }

    // Envoyer le token et les informations utilisateur au serveur
    const requestBody = {
      idToken: result.idToken,
      userInfo: result.userInfo,
    };



    const apiUrl = import.meta.env.VITE_API_URL || 'https://waabo-app.com';


    const response = await fetch(`${apiUrl}/api/auth/google-app`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      credentials: 'include', // Include cookies in cross-origin requests
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Server error response:', errorText);
      throw new Error('Échec de l\'authentification côté serveur');
    }

    const data = await response.json();

    console.log('=== GOOGLE LOGIN SUCCESS ===');
    console.log('Response data:', data);

    if (data.success && data.user && data.token) {
      // Lưu JWT token vào localStorage
      localStorage.setItem('waabo_jwt_token', data.token);
      console.log('JWT token saved to localStorage');

      // Lưu user data vào React Query cache
      queryClient.setQueryData(["/api/user"], data.user);
      console.log('React Query cache updated successfully');

      // Lưu user data vào persistent storage (backup)
      localStorage.setItem('waabo_user', JSON.stringify(data.user));
      console.log('User data saved to persistent storage');

      toast({
        title: 'Connexion réussie',
        description: 'Vous êtes maintenant connecté avec Google',
      });

      // Si un callback est fourni, l'appeler avec les données
      onLoginSuccess?.(data);

      // Rediriger vers la page d'accueil
      setLocation('/');
    } else {
      throw new Error('Invalid response format from server');
    }
  } catch (error) {
    console.error('Erreur lors de la connexion Google:', error);

    toast({
      title: 'Erreur',
      description: 'Impossible de se connecter avec Google',
      variant: 'destructive',
    });

    onLoginFailure?.(error as Error);
  } finally {
    setIsLoading(false);
  }
};

// Gérer la connexion Google via le navigateur web
const handleWebGoogleLogin = async () => {
  try {
    const response = await fetch('/api/auth/google');
    const data = await response.json();

    toast({
      title: 'Connexion Google',
      description: 'Redirection vers Google pour authentification...',
    });

    // Ouvrir dans une fenêtre popup plutôt qu'un nouvel onglet
    const googleAuthWindow = window.open(data.url, '_blank', 'width=600,height=700');

    // Démarrer la vérification d'authentification si la fenêtre est ouverte
    if (googleAuthWindow && window.startAuthCheck) {
      window.startAuthCheck(googleAuthWindow);
    }

    // Si fenêtre bloquée, proposer la redirection directe
    if (!googleAuthWindow) {
      toast({
        title: 'Popup bloqué',
        description: 'Veuillez autoriser les popups ou cliquer sur "OK" pour rediriger directement',
        variant: 'destructive',
      });

      // Attendre confirmation pour rediriger
      if (confirm('Le popup a été bloqué. Voulez-vous être redirigé directement vers Google pour vous connecter?')) {
        window.location.href = data.url;
      }
    }
  } catch (error) {
    console.error('Erreur auth Google:', error);

    toast({
      title: 'Erreur',
      description: 'Impossible de se connecter avec Google',
      variant: 'destructive',
    });

    onLoginFailure?.(error as Error);
  }
};

return (
  <Button
    variant="outline"
    className="w-full flex items-center justify-center gap-2 bg-white hover:bg-gray-50 text-gray-700 hover:text-gray-900 border-gray-300"
    onClick={handleAppGoogleLogin}
    disabled={isLoading}
  >
    <FcGoogle className="h-5 w-5" />
    <span>Continuer avec Google</span>
  </Button>
);
};

export default AppGoogleLogin;