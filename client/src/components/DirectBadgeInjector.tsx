import React, { useEffect } from 'react';
import { useNotifications } from '@/hooks/use-notifications';

// Ce composant n'affiche rien visuellement mais injecte directement un badge dans le DOM
export default function DirectBadgeInjector() {
  const { unreadCount } = useNotifications();
  
  useEffect(() => {
    console.log("DirectBadgeInjector monté avec unreadCount:", unreadCount);
    
    // Fonction pour injecter le badge de notification
    const injectBadge = () => {
      console.log("Tentative d'injection du badge, unreadCount =", unreadCount);
      if (unreadCount <= 0) {
        // Supprimer le badge s'il existe
        const existingBadge = document.getElementById('direct-notification-badge');
        if (existingBadge) {
          existingBadge.remove();
        }
        return;
      }
      
      // Définir une position approximative pour le badge
      // Trouver un élément de référence pour positionner le badge
      const headerElement = document.querySelector('header');
      const bellIcon = headerElement?.querySelector('svg[d*="M21"]'); // Attribut d contenant souvent le chemin d'un icône
      
      if (!headerElement) {
        console.log("Header non trouvé, impossible d'injecter le badge");
        return;
      }
      
      // Supprimer l'ancien badge s'il existe
      const existingBadge = document.getElementById('direct-notification-badge');
      if (existingBadge) {
        existingBadge.remove();
      }
      
      // Créer le badge
      const badge = document.createElement('div');
      badge.id = 'direct-notification-badge';
      badge.textContent = unreadCount.toString();
      
      // Styler le badge
      badge.style.position = 'fixed';
      badge.style.zIndex = '9999';
      badge.style.top = '16px';  
      badge.style.right = '150px';
      badge.style.backgroundColor = '#ef4444';
      badge.style.color = 'white';
      badge.style.width = '22px';
      badge.style.height = '22px';
      badge.style.borderRadius = '50%';
      badge.style.display = 'flex';
      badge.style.alignItems = 'center';
      badge.style.justifyContent = 'center';
      badge.style.fontSize = '12px';
      badge.style.fontWeight = 'bold';
      badge.style.border = '2px solid white';
      badge.style.boxShadow = '0 1px 3px rgba(0,0,0,0.2)';
      
      // Ajouter le badge au DOM
      document.body.appendChild(badge);
      console.log("Badge injecté avec succès, contenu:", unreadCount);
    };
    
    // Injecter le badge immédiatement et toutes les 2 secondes pour s'assurer qu'il reste visible
    injectBadge();
    const intervalId = setInterval(injectBadge, 2000);
    
    return () => {
      clearInterval(intervalId);
      const existingBadge = document.getElementById('direct-notification-badge');
      if (existingBadge) {
        existingBadge.remove();
      }
    };
  }, [unreadCount]);
  
  // Ce composant ne rend rien visuellement
  return null;
}