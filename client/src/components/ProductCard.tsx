import React from 'react';
import { useLocation } from 'wouter';
import { Product } from '@/lib/types';

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [_, setLocation] = useLocation();

  const handleClick = () => {
    setLocation(`/product/${product.id}`);
  };

  return (
    <div 
      onClick={handleClick}
      className="border border-gray-200 rounded-lg overflow-hidden cursor-pointer hover:shadow-md transition"
    >
      <img 
        src={product.imageUrls && product.imageUrls.length > 0 
          ? product.imageUrls[0] // Utilise la première image du tableau si disponible
          : product.imageUrl}  // Fallback sur l'image principale
        alt={product.name} 
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <h3 className="font-medium text-neutral-800">{product.name}</h3>
        <p className="text-sm text-neutral-600 mt-1">{product.description}</p>
        <div className="mt-2 flex items-center justify-between">
          <span className="font-bold text-[#004d25]">{product.price}</span>
          {product.hasFreeshipping && (
            <span className="text-xs bg-[hsl(var(--waabo-yellow))] text-[#004d25] px-2 py-0.5 rounded-full">Livraison gratuite</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
