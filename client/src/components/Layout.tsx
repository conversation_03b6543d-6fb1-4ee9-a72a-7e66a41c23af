import { ReactNode, useState } from "react";
import { Link, useLocation } from "wouter";
import { User, LogOut, <PERSON>u, <PERSON>, <PERSON> } from "lucide-react";
import { Capacitor } from "@capacitor/core";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { useNotifications } from "@/hooks/use-notifications";
import { usePopupNotifications } from "../hooks/use-popup-notifications";
import Logo from "@/components/Logo";
import Footer from "@/components/Footer";
import DynamicNotificationBadge from "@/components/DynamicNotificationBadge";
import DirectBadgeInjector from "@/components/DirectBadgeInjector";
import TestNotificationBadge from "@/components/TestNotificationBadge";
import { PopupNotification } from "./ui/popup-notification";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet";

// Composant pour afficher le badge de notification dans les menus
function NotificationIconBadge({ position = "menu" }: { position?: "menu" | "dropdown" }) {
  const { unreadCount } = useNotifications();

  // Afficher uniquement s'il y a des notifications non lues
  if (!unreadCount || unreadCount <= 0) return null;

  // Styles variables selon la position
  const positionStyles = position === "dropdown"
    ? "top-0 right-2" // Pour le badge dans le dropdown
    : "-top-2 -right-2"; // Pour le badge à côté de l'icône

  const sizeStyles = position === "dropdown"
    ? "h-5 w-5 text-xs font-bold"
    : "h-6 w-6 text-xs font-bold";

  return (
    <span
      className={`
        absolute ${positionStyles} flex ${sizeStyles} items-center 
        justify-center rounded-full font-medium text-white shadow-sm
        bg-red-500
      `}
      style={{
        pointerEvents: 'none',
        zIndex: 999,
        border: '2px solid white'
      }}
    >
      {unreadCount}
    </span>
  );
}

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const [_, navigate] = useLocation();
  const { user, logoutMutation } = useAuth();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const { currentPopup, isPopupOpen, closePopup } = usePopupNotifications();
  const { unreadCount } = useNotifications();

  const handleLogout = async () => {
    try {
      // Logout from social auth services (mobile only)
      if (Capacitor.isNativePlatform()) {
        try {
          const { capacitorGoogleAuth } = await import('@/services/capacitorGoogleAuth');
          const { capacitorAppleAuth } = await import('@/services/capacitorAppleAuth');
          const { capacitorFacebookAuth } = await import('@/services/capacitorFacebookAuth');

          await Promise.allSettled([
            capacitorGoogleAuth.logout(),
            capacitorAppleAuth.logout(),
            capacitorFacebookAuth.logout()
          ]);

          console.log('Social auth services logged out');
        } catch (socialLogoutError) {
          console.warn('Error during social auth logout:', socialLogoutError);
        }
      }

      await logoutMutation.mutateAsync();
      navigate("/auth");
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de se déconnecter",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header for mobile - avec position fixe */}
      <header className="md:hidden bg-white border-b py-3 fixed top-0 left-0 right-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Logo size="small" withText linkTo="/" />

          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="flex flex-col gap-5 mt-8">
                <div className="flex items-center justify-between">
                  <Logo size="small" withText noLink={true} />
                  <Button variant="ghost" size="icon" onClick={() => setOpen(false)}>
                    <X className="h-5 w-5" />
                  </Button>
                </div>
                <nav className="flex flex-col space-y-4 pt-4">
                  <Link href="/" onClick={() => setOpen(false)}>
                    <div className="py-2 hover:text-primary">Accueil</div>
                  </Link>
                  <div className="flex items-center">
                    <Link href="/notifications" onClick={() => setOpen(false)}>
                      <div className="py-2 hover:text-primary flex items-center">
                        <span style={{ position: 'relative', marginRight: '8px' }}>
                          <Bell className="h-4 w-4" />
                          {unreadCount > 0 && (
                            <div style={{
                              position: 'absolute',
                              top: '-6px',
                              right: '-6px',
                              width: '16px',
                              height: '16px',
                              borderRadius: '50%',
                              backgroundColor: '#ef4444',
                              color: 'white',
                              fontSize: '9px',
                              fontWeight: 'bold',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              zIndex: 50,
                              border: '1px solid white'
                            }}>
                              {unreadCount}
                            </div>
                          )}
                        </span>
                        <span>Notifications</span>
                      </div>
                    </Link>
                  </div>
                  <Link href="/profile" onClick={() => setOpen(false)}>
                    <div className="py-2 hover:text-primary">Mon Profil</div>
                  </Link>
                  <Link href="/shipping-mark" onClick={() => setOpen(false)}>
                    <div className="py-2 hover:text-primary">Marque d'expédition</div>
                  </Link>
                  {user ? (
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => {
                        handleLogout();
                        setOpen(false);
                      }}
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Déconnexion
                    </Button>
                  ) : (
                    <Link href="/auth" onClick={() => setOpen(false)}>
                      <Button className="w-full">Se connecter</Button>
                    </Link>
                  )}
                </nav>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      {/* Header for desktop - position fixe */}
      <header className="hidden md:block bg-white border-b py-4 fixed top-0 left-0 right-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Logo size="small" withText linkTo="/" />

          {user ? (
            <div className="flex items-center space-x-4">
              {/* DynamicNotificationBadge - un composant qui génère directement le HTML avec le badge */}
              <DynamicNotificationBadge />

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2">
                    <User className="h-5 w-5" />
                    <span className="hidden lg:inline">{user.username || 'Mon Compte'}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Mon Compte</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate("/profile")}>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profil</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate("/notifications")} style={{ position: 'relative' }}>
                    <Bell className="mr-2 h-4 w-4" />
                    <span>Notifications</span>
                    {/* Indicateur de notification dans le menu dropdown */}
                    {unreadCount > 0 && (
                      <div style={{
                        position: 'absolute',
                        top: '50%',
                        right: '15px',
                        transform: 'translateY(-50%)',
                        width: '18px',
                        height: '18px',
                        borderRadius: '50%',
                        backgroundColor: '#ef4444',
                        color: 'white',
                        fontSize: '10px',
                        fontWeight: 'bold',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 60,
                        pointerEvents: 'none',
                        boxShadow: '0 0 0 2px white'
                      }}>
                        {unreadCount}
                      </div>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {user.email === "<EMAIL>" && (
                    <>
                      <DropdownMenuItem onClick={() => navigate("/admin")}>
                        <span>Administration</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                    </>
                  )}
                  <DropdownMenuItem onClick={() => navigate("/shipping-mark")}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect width="16" height="16" x="4" y="4" rx="2" />
                      <path d="M9 9h6" />
                      <path d="M9 13h6" />
                      <path d="M9 17h6" />
                    </svg>
                    <span>Marque d'expédition</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Déconnexion</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ) : (
            <Link href="/auth">
              <Button>Se connecter</Button>
            </Link>
          )}
        </div>
      </header>

      {/* Espace pour compenser la hauteur du header fixe */}
      <div className="h-16"></div>

      {/* Main content */}
      <main className="flex-grow">
        {children}
      </main>

      {/* Footer */}
      <Footer />

      {/* Badge Injector - force l'affichage d'un badge de notification */}
      {user && <DirectBadgeInjector />}

      {/* Test Badge - utilise une approche plus radicale */}
      {user && <TestNotificationBadge />}

      {/* Popup Notification */}
      {isPopupOpen && currentPopup && (
        <PopupNotification
          id={currentPopup.id}
          title={currentPopup.title}
          content={currentPopup.content}
          imageUrl={currentPopup.imageUrl}
          backgroundColor={currentPopup.backgroundColor}
          textColor={currentPopup.textColor}
          buttonColor={currentPopup.buttonColor}
          buttonTextColor={currentPopup.buttonTextColor}
          titleFontSize={currentPopup.titleFontSize}
          contentFontSize={currentPopup.contentFontSize}
          buttonText={currentPopup.buttonText}
          buttonLink={currentPopup.buttonLink}
          borderRadius={currentPopup.borderRadius}
          width={currentPopup.width.toString()}
          onClose={closePopup}
        />
      )}
    </div>
  );
}