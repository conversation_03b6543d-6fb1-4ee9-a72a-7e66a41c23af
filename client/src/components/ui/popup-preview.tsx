import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface PopupPreviewProps {
  title: string;
  content: string;
  imageUrl?: string;
  backgroundColor?: string;
  textColor?: string;
  buttonColor?: string;
  buttonTextColor?: string;
  buttonText?: string;
  titleFontSize?: string;
  contentFontSize?: string;
  borderRadius?: string;
  width?: number | string;
  height?: number | string;
}

export function PopupPreview({
  title,
  content,
  imageUrl,
  backgroundColor = "#ffffff",
  textColor = "#000000",
  buttonColor = "#3b82f6",
  buttonTextColor = "#ffffff",
  buttonText = "OK",
  titleFontSize = "24px",
  contentFontSize = "16px",
  borderRadius = "8px",
  width = 400,
  height,
}: PopupPreviewProps) {
  // Calcul de la valeur CSS finale pour la largeur, gérant à la fois string et number
  const finalWidth = typeof width === 'string' ? width : `${width}px`;
  
  // Si height est défini, l'utiliser, sinon auto
  const finalHeight = height ? (typeof height === 'string' ? height : `${height}px`) : 'auto';
  
  return (
    <div className="flex items-center justify-center w-full p-4 bg-gray-100 rounded-lg">
      <Card 
        className="overflow-hidden relative shadow-lg"
        style={{ 
          backgroundColor, 
          color: textColor,
          borderRadius,
          width: finalWidth,
          height: finalHeight,
        }}
      >
        <button 
          className="absolute top-2 right-2 rounded-full w-6 h-6 flex items-center justify-center bg-gray-200 hover:bg-gray-300 transition-colors"
          style={{ color: "#000" }}
        >
          <X className="h-4 w-4" />
        </button>
        
        {imageUrl && (
          <div className="w-full h-48 overflow-hidden">
            <img 
              src={imageUrl} 
              alt={title} 
              className="w-full h-full object-cover" 
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          </div>
        )}
        
        <CardHeader className="p-4">
          <CardTitle 
            className="font-bold" 
            style={{ fontSize: titleFontSize }}
          >
            {title || "Titre de la notification"}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="p-4 pt-0">
          <p 
            className="whitespace-pre-line" 
            style={{ fontSize: contentFontSize }}
          >
            {content || "Contenu de la notification..."}
          </p>
        </CardContent>
        
        <CardFooter className="p-4 pt-0 justify-end">
          <Button
            className="px-4 py-2 transition-colors"
            style={{ 
              backgroundColor: buttonColor,
              color: buttonTextColor,
            }}
          >
            {buttonText}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}