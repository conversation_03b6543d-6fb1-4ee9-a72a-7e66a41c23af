import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { RiUpload2Line, RiDeleteBin6Line } from "react-icons/ri";
import { useFormContext } from "react-hook-form";

export function ImageUploader() {
  const form = useFormContext();
  const [isUploading, setIsUploading] = useState(false);
  
  // Convertir l'image en base64
  const convertToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };
  
  const handleImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Vérifier le type de fichier
    if (!file.type.includes('image/')) {
      alert('Veuillez sélectionner une image valide.');
      return;
    }
    
    // Vérifier la taille du fichier (max 2 MB)
    if (file.size > 2 * 1024 * 1024) {
      alert('La taille de l\'image ne doit pas dépasser 2 MB.');
      return;
    }
    
    try {
      setIsUploading(true);
      const base64 = await convertToBase64(file);
      form.setValue("imageUrl", base64);
    } catch (error) {
      console.error('Erreur lors de la conversion de l\'image:', error);
      alert('Une erreur s\'est produite lors du traitement de l\'image.');
    } finally {
      setIsUploading(false);
    }
  }, [form]);
  
  const clearImage = () => {
    form.setValue("imageUrl", "");
  };
  
  const imageUrl = form.watch("imageUrl");
  
  return (
    <FormField
      control={form.control}
      name="imageUrl"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Image</FormLabel>
          <div className="space-y-4">
            <FormControl>
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                  disabled={isUploading}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('image-upload')?.click()}
                  disabled={isUploading}
                >
                  <RiUpload2Line className="mr-2" />
                  {isUploading ? 'Téléchargement...' : 'Télécharger une image'}
                </Button>
                {imageUrl && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={clearImage}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <RiDeleteBin6Line className="mr-2" />
                    Supprimer
                  </Button>
                )}
              </div>
            </FormControl>
            
            {imageUrl && (
              <div className="mt-4 border rounded-md overflow-hidden">
                <img
                  src={imageUrl}
                  alt="Aperçu de l'image"
                  className="max-h-48 w-full object-contain"
                />
              </div>
            )}
            
            <FormDescription>
              Formats acceptés: JPG, PNG, GIF. Taille max: 2 MB
            </FormDescription>
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  );
}