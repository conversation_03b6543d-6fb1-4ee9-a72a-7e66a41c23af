import React from 'react';
import { cn } from '@/lib/utils';
import { TrackingStep } from '@/lib/types';
import { CheckIcon, TruckIcon, PackageIcon, MailOpenIcon, HomeIcon, ClipboardCheckIcon } from 'lucide-react';

// Fonction pour formater une date en GMT+0/UTC
const formatDateGMT = (date: Date | string) => {
  const d = new Date(date);
  const day = String(d.getUTCDate()).padStart(2, '0');
  const month = String(d.getUTCMonth() + 1).padStart(2, '0');
  const year = String(d.getUTCFullYear()).slice(-2);
  return `${day}/${month}/${year}`;
};

// Fonction pour formater une heure en GMT+0/UTC
const formatTimeGMT = (date: Date | string) => {
  const d = new Date(date);
  const hours = String(d.getUTCHours()).padStart(2, '0');
  const minutes = String(d.getUTCMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

interface ProgressStepsProps {
  steps: TrackingStep[];
  className?: string;
}

export function ProgressSteps({ steps, className }: ProgressStepsProps) {
  // Find the index of the active step
  const activeIndex = steps.findIndex(step => step.status === 'active');
  
  // Fonction pour déterminer si une étape doit avoir un style "complété" ou "actif"
  const shouldBeHighlighted = (index: number) => {
    // Une étape est mise en évidence uniquement si elle est active ou complétée
    // Les étapes avec statut 'pending' ne doivent pas être mises en évidence
    return steps[index].status === 'active' || steps[index].status === 'completed';
  };
  
  // Calculate how far to extend the progress line - all the way to the active circle
  const calculateProgressWidth = () => {
    if (activeIndex >= 0) {
      // Calculate the exact width to reach the active circle
      return `${(activeIndex / (steps.length - 1)) * 100}%`;
    } else {
      // If no step is active but some are completed
      const lastCompletedIndex = steps.reduce((lastIndex, step, currentIndex) => 
        step.status === 'completed' ? currentIndex : lastIndex, -1);
      return `${lastCompletedIndex >= 0 ? (lastCompletedIndex / (steps.length - 1)) * 100 : 0}%`;
    }
  };

  // Get the appropriate icon for each step
  const getStepIcon = (stepNumber: number) => {
    switch(stepNumber) {
      case 1: return <ClipboardCheckIcon className="h-4 w-4" />;
      case 2: return <PackageIcon className="h-4 w-4" />;
      case 3: return <TruckIcon className="h-4 w-4" />;
      case 4: return <MailOpenIcon className="h-4 w-4" />;
      case 5: return <HomeIcon className="h-4 w-4" />;
      default: return <CheckIcon className="h-4 w-4" />;
    }
  };
  
  return (
    <div className={cn('relative py-8 px-3', className)}>
      {/* Container for the line and circles */}
      <div className="relative flex items-center" style={{ height: "80px" }}>
        {/* The background line (gray) */}
        <div className="absolute inset-0 flex items-center">
          <div className="h-2 w-full bg-gray-200 rounded-full"></div>
        </div>
        
        {/* The colored progress line with gradient effect */}
        <div className="absolute inset-0 flex items-center">
          <div 
            className="h-2 rounded-full bg-gradient-to-r from-[#004d25] to-[#1e6e42]"
            style={{ 
              width: calculateProgressWidth(),
              transition: "width 0.7s ease-in-out",
              boxShadow: "0 2px 5px rgba(0, 77, 37, 0.2)"
            }}
          ></div>
        </div>
        
        {/* Circles positioned at specific points */}
        {steps.map((step, index) => {
          // Calculate horizontal position for each circle
          const leftPosition = index === 0 ? '0%' : 
                            index === steps.length - 1 ? '100%' : 
                            `${(index / (steps.length - 1)) * 100}%`;
          
          return (
            <div 
              key={step.number}
              className="absolute transform -translate-x-1/2"
              style={{ 
                left: leftPosition,
                top: "0",
                zIndex: 10
              }}
            >
              <div 
                className={cn(
                  'tracking-step relative flex flex-col items-center',
                  step.status === 'active' && 'active',
                  step.status === 'completed' && 'completed',
                  index === steps.length - 1 && 'last-step'
                )}
              >
                <div 
                  className={cn(
                    "tracking-step-number rounded-full transition-all duration-300 flex items-center justify-center border-2",
                    shouldBeHighlighted(index) ? 'border-[#004d25] bg-[#004d25] text-white' : 
                    'border-gray-300 bg-gray-300 text-white'
                  )}
                  style={{ 
                    width: '36px', 
                    height: '36px',
                    transform: step.status === 'active' ? 'scale(1.1)' : 'scale(1)',
                    boxShadow: step.status === 'active' ? '0 0 0 4px rgba(0, 77, 37, 0.15)' : 'none',
                    backgroundColor: shouldBeHighlighted(index) ? '#004d25' : '#d1d5db',
                    borderColor: shouldBeHighlighted(index) ? '#004d25' : '#d1d5db'
                  }}
                >
                  {/* Toujours afficher l'icône spécifique de l'étape, peu importe son statut */}
                  <div className="text-white">
                    {getStepIcon(step.number)}
                  </div>
                </div>
                <div className="flex flex-col items-center">
                  <div className={cn(
                    "tracking-step-label mt-3 text-center mx-auto max-w-[100px] text-sm font-medium",
                    shouldBeHighlighted(index) ? 'text-[#004d25]' : 'text-gray-400'
                  )}>
                    {step.label}
                  </div>
                  {step.timestamp && shouldBeHighlighted(index) && (
                    <div className="mt-1 text-xs text-gray-500 text-center">
                      {formatDateGMT(step.timestamp)}
                      <br />
                      {formatTimeGMT(step.timestamp)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
