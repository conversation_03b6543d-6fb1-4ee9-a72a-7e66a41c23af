import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RiArrowLeftSLine, RiArrowRightSLine } from 'react-icons/ri';

interface ProductImageCarouselProps {
  images: string[];
  alt: string;
}

const ProductImageCarousel: React.FC<ProductImageCarouselProps> = ({ images, alt }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Si aucune image n'est fournie, retourner null
  if (!images || images.length === 0) return null;

  // Utiliser au moins la première image si disponible
  const imagesList = images.length > 0 ? images : [images[0]];

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % imagesList.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? imagesList.length - 1 : prevIndex - 1
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <div className="relative w-full overflow-hidden">
      {/* Image principale */}
      <div className="relative h-64 sm:h-96 overflow-hidden">
        <img 
          src={imagesList[currentIndex]} 
          alt={`${alt} - Image ${currentIndex + 1}`}
          className="w-full h-full object-cover transition-opacity duration-300"
        />
        
        {/* Overlay pour les boutons de navigation sur mobile */}
        <div className="absolute inset-0 flex items-center justify-between p-4">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={prevSlide}
            className="bg-white bg-opacity-40 hover:bg-white hover:bg-opacity-60 rounded-full h-8 w-8 p-1"
          >
            <RiArrowLeftSLine className="h-6 w-6 text-neutral-800" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={nextSlide}
            className="bg-white bg-opacity-40 hover:bg-white hover:bg-opacity-60 rounded-full h-8 w-8 p-1"
          >
            <RiArrowRightSLine className="h-6 w-6 text-neutral-800" />
          </Button>
        </div>
      </div>
      
      {/* Indicateurs / Miniatures */}
      <div className="flex justify-center mt-4 space-x-2">
        {imagesList.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? 'bg-[#004d25] w-3 h-3'
                : 'bg-neutral-300'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
      
      {/* Miniatures des images (visible uniquement sur desktop) */}
      <div className="hidden md:flex justify-center mt-4 space-x-2">
        {imagesList.map((image, index) => (
          <div
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-16 h-16 rounded overflow-hidden cursor-pointer border-2 transition-all ${
              index === currentIndex
                ? 'border-[#004d25]'
                : 'border-transparent'
            }`}
          >
            <img 
              src={image} 
              alt={`${alt} thumbnail ${index + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProductImageCarousel;