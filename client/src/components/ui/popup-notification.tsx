import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface PopupNotificationProps {
  id: number;
  title: string;
  content: string;
  imageUrl?: string;
  backgroundColor?: string;
  textColor?: string;
  buttonColor?: string;
  buttonTextColor?: string;
  titleFontSize?: string;
  contentFontSize?: string;
  buttonText?: string;
  buttonLink?: string;
  borderRadius?: string;
  width?: string | number;
  onClose: () => void;
  onSeen?: (id: number) => void;
}

export function PopupNotification({
  id,
  title,
  content,
  imageUrl,
  backgroundColor = '#ffffff',
  textColor = '#000000',
  buttonColor = '#3b82f6',
  buttonTextColor = '#ffffff',
  titleFontSize = '24px',
  contentFontSize = '16px',
  buttonText = 'OK',
  buttonLink,
  borderRadius = '8px',
  width = '500px',
  onClose,
  onSeen
}: PopupNotificationProps) {
  const [show, setShow] = useState(true);

  // Fonction qui va gérer toute la logique de fermeture
  const handleCloseAnimation = () => {
    console.log("Animation de fermeture démarrée");
    setShow(false);
    // Attendre que l'animation soit terminée avant d'appeler onClose
    setTimeout(() => {
      console.log("Animation terminée, appel de onClose");
      onClose();
    }, 300);
  };
  
  useEffect(() => {
    // Notifier que l'utilisateur a vu cette notification
    if (onSeen) {
      onSeen(id);
    }

    // Ajouter un gestionnaire d'événement pour la touche Echap
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleCloseAnimation();
      }
    };
    
    document.addEventListener('keydown', handleEscKey);
    
    // Nettoyer l'écouteur d'événement lors du démontage
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [id, onSeen]);

  // Gestion du clic sur le bouton
  const handleButtonClick = () => {
    if (buttonLink) {
      window.open(buttonLink, '_blank');
    }
    handleCloseAnimation();
  };

  // Gestion du clic sur l'overlay
  const handleOverlayClick = (e: React.MouseEvent) => {
    // S'assurer que c'est bien l'overlay qui est cliqué
    if (e.target === e.currentTarget) {
      handleCloseAnimation();
    }
  };

  return (
    <AnimatePresence>
      {show && (
        <>
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
            onClick={handleOverlayClick}
          >
            {/* Popup content */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="relative overflow-hidden z-50 max-h-[90vh] overflow-y-auto"
              style={{
                backgroundColor,
                color: textColor,
                borderRadius,
                width,
                maxWidth: '90vw',
              }}
            >
              {/* Close button */}
              <button
                onClick={handleCloseAnimation}
                className="absolute top-2 right-2 p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors z-[100]"
                aria-label="Fermer"
                type="button"
              >
                <X size={20} color={textColor} />
              </button>

              {/* Content */}
              <div className="p-6">
                {imageUrl && (
                  <div className="mb-4 rounded overflow-hidden">
                    <img
                      src={imageUrl}
                      alt=""
                      className="w-full h-auto object-cover"
                      onError={(e) => {
                        // Supprimer l'image en cas d'erreur
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  </div>
                )}

                <h2
                  className="font-bold mb-4"
                  style={{ fontSize: titleFontSize }}
                >
                  {title}
                </h2>

                <div
                  className="mb-6 whitespace-pre-line"
                  style={{ fontSize: contentFontSize }}
                >
                  {content}
                </div>

                <div className="flex justify-center">
                  <Button
                    onClick={handleButtonClick}
                    style={{
                      backgroundColor: buttonColor,
                      color: buttonTextColor,
                    }}
                    type="button"
                  >
                    {buttonText}
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}