import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Liste des codes pays avec Burkina Faso en premier
const countryCodes = [
  { code: "+226", country: "Burkina Faso", flag: "🇧🇫" },
  { code: "+225", country: "Côte d'Ivoire", flag: "🇨🇮" },
  { code: "+221", country: "Sénégal", flag: "🇸🇳" },
  { code: "+223", country: "Mali", flag: "🇲🇱" },
  { code: "+227", country: "Niger", flag: "🇳🇪" },
  { code: "+228", country: "Togo", flag: "🇹🇬" },
  { code: "+229", country: "Bénin", flag: "🇧🇯" },
  { code: "+233", country: "Ghana", flag: "🇬🇭" },
  { code: "+86", country: "Chine", flag: "🇨🇳" },
];

interface CountryCodeSelectProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function CountryCodeSelect({
  value,
  onChange,
  className,
}: CountryCodeSelectProps) {
  // Définir la valeur par défaut (Burkina Faso)
  const [selectedCode, setSelectedCode] = useState(value || "+226");

  const handleValueChange = (newValue: string) => {
    setSelectedCode(newValue);
    onChange(newValue);
  };

  return (
    <Select value={selectedCode} onValueChange={handleValueChange}>
      <SelectTrigger className={`min-w-[120px] ${className}`}>
        <SelectValue placeholder="Code pays" />
      </SelectTrigger>
      <SelectContent className="min-w-[250px]">
        <SelectGroup>
          {countryCodes.map((country) => (
            <SelectItem key={country.code} value={country.code}>
              <div className="flex items-center">
                <span className="mr-2">{country.flag}</span>
                <span className="whitespace-nowrap">{country.country}</span>
                <span className="ml-1 text-gray-500">{country.code}</span>
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}

export default CountryCodeSelect;
