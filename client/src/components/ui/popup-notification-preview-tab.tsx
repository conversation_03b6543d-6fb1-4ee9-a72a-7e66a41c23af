import React from 'react';
import { useFormContext } from "react-hook-form";
import { PopupPreview } from "@/components/ui/popup-preview";

export function PopupNotificationPreviewTab() {
  const form = useFormContext();
  
  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      <PopupPreview 
        title={form.watch("title")}
        content={form.watch("content")}
        imageUrl={form.watch("imageUrl")}
        backgroundColor={form.watch("backgroundColor")}
        textColor={form.watch("textColor")}
        buttonColor={form.watch("buttonColor")}
        buttonTextColor={form.watch("buttonTextColor")}
        buttonText={form.watch("buttonText")}
        titleFontSize={form.watch("titleFontSize")}
        contentFontSize={form.watch("contentFontSize")}
        borderRadius={form.watch("borderRadius")}
        width={form.watch("width")}
      />
      <p className="text-sm text-gray-500 mt-4">
        Cette prévisualisation montre l'apparence de la notification popup telle qu'elle sera affichée aux utilisateurs.
      </p>
    </div>
  );
}