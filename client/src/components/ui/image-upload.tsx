import React, { useState, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { UploadCloudIcon, ImageIcon, XIcon } from "lucide-react";

interface ImageUploadProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  accept?: string;
  maxSizeMB?: number;
}

export function ImageUpload({
  value,
  onChange,
  label = "Image",
  accept = "image/*",
  maxSizeMB = 5
}: ImageUploadProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(value || null);

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Vérifier la taille du fichier
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      toast({
        title: "Fichier trop volumineux",
        description: `La taille maximum est de ${maxSizeMB}MB. Votre fichier fait ${(file.size / 1024 / 1024).toFixed(2)}MB.`,
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      // Créer une URL temporaire pour la prévisualisation
      const objectUrl = URL.createObjectURL(file);
      setPreview(objectUrl);

      // Simuler l'upload - à remplacer par un vrai upload côté serveur
      // Pour l'instant, nous pouvons utiliser l'encodage en base64 pour les démonstrations
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        onChange(reader.result as string);
        setIsUploading(false);
      };
      reader.onerror = () => {
        toast({
          title: "Erreur de lecture du fichier",
          description: "Impossible de lire le fichier sélectionné.",
          variant: "destructive",
        });
        setIsUploading(false);
      };

    } catch (error) {
      toast({
        title: "Erreur d'upload",
        description: "Une erreur est survenue lors de l'upload de l'image.",
        variant: "destructive",
      });
      setIsUploading(false);
    }
  };

  const handleClear = () => {
    if (preview) {
      URL.revokeObjectURL(preview);
    }
    setPreview(null);
    onChange("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="image-upload">{label}</Label>
      
      <div className="grid gap-4">
        <Input 
          id="image-upload"
          type="file" 
          accept={accept}
          onChange={handleFileSelect}
          ref={fileInputRef}
          className="hidden"
        />
        
        {!preview ? (
          <div 
            className="border-2 border-dashed border-gray-300 rounded-md p-8 text-center cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <UploadCloudIcon className="mx-auto h-10 w-10 text-gray-400 mb-3" />
            <p className="text-sm font-medium text-gray-700">Cliquez pour uploader une image</p>
            <p className="text-xs text-gray-500 mt-1">ou glissez-déposez un fichier image</p>
            <p className="text-xs text-gray-500 mt-1">PNG, JPG, GIF jusqu'à {maxSizeMB}MB</p>
          </div>
        ) : (
          <div className="relative w-full h-48 bg-gray-100 rounded-md overflow-hidden">
            <img 
              src={preview} 
              alt="Image preview" 
              className="w-full h-full object-contain"
              onError={() => {
                toast({
                  title: "Erreur d'affichage",
                  description: "Impossible d'afficher l'image.",
                  variant: "destructive",
                });
                handleClear();
              }}
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
              <Button 
                variant="secondary"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
              >
                <ImageIcon className="h-4 w-4 mr-1" />
                Changer
              </Button>
              <Button 
                variant="destructive"
                size="sm"
                onClick={handleClear}
              >
                <XIcon className="h-4 w-4 mr-1" />
                Supprimer
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}