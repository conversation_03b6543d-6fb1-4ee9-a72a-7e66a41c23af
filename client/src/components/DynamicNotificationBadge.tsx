import React from 'react';
import { <PERSON> } from 'wouter';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNotifications } from '@/hooks/use-notifications';

const DynamicNotificationBadge = () => {
  const { unreadCount } = useNotifications();

  return (
    <Link href="/notifications">
      <Button variant="ghost" size="icon" className="relative">
        <Bell size={24} color="#004d25" />
        
        {/* Le badge de notification n'apparaît que s'il y a des notifications non lues */}
        {unreadCount > 0 && (
          <div 
            style={{
              position: 'absolute',
              top: '-8px',
              right: '-8px',
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: '50%',
              width: '22px',
              height: '22px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              fontWeight: 'bold',
              zIndex: 9999,
              border: '2px solid white',
              pointerEvents: 'none'
            }}
          >
            {unreadCount}
          </div>
        )}
      </Button>
    </Link>
  );
};

export default DynamicNotificationBadge;