import React, { useEffect, useState } from 'react';
import { <PERSON> } from 'wouter';
import { useNotifications } from '@/hooks/use-notifications';
import { Button } from "@/components/ui/button";
import { Bell } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface NotificationIconProps {
  className?: string;
  iconClassName?: string;
  variant?: 'default' | 'large';
  forceBadge?: boolean; // Pour tester l'affichage du badge
}

const NotificationIcon: React.FC<NotificationIconProps> = ({ 
  className = "relative",
  iconClassName = "h-5 w-5",
  variant = 'default',
  forceBadge = false
}) => {
  const { unreadCount, isLoading } = useNotifications();
  const [isNewNotification, setIsNewNotification] = useState(false);
  const [prevCount, setPrevCount] = useState(0);
  const [localCount, setLocalCount] = useState(0);
  
  // Synchroniser le compteur local avec les données du hook
  useEffect(() => {
    if (!isLoading) {
      setLocalCount(unreadCount);
      
      // Si le nombre de notifications a augmenté, déclencher l'animation
      if (unreadCount > prevCount) {
        setIsNewNotification(true);
        const timer = setTimeout(() => {
          setIsNewNotification(false);
        }, 3000); // Arrête l'animation après 3 secondes
        
        return () => clearTimeout(timer);
      }
      
      setPrevCount(unreadCount);
    }
  }, [unreadCount, isLoading, prevCount]);

  useEffect(() => {
    console.log(`NotificationIcon - unreadCount: ${unreadCount}, localCount: ${localCount}, isLoading: ${isLoading}`);
  }, [unreadCount, localCount, isLoading]);
  
  // Détermine s'il y a des notifications non lues
  const hasUnread = forceBadge || (!isLoading && localCount > 0);
  
  // Animations pour l'icône
  const pulseAnimation = {
    scale: [1, 1.1, 1],
    transition: { 
      duration: 2, 
      repeat: Infinity,
      repeatType: "reverse" as const
    }
  };
  
  const ringAnimation = {
    rotate: [0, -10, 10, -10, 10, 0],
    transition: { duration: 0.5, ease: "easeInOut" }
  };

  // Taille et styles du badge en fonction de la variante
  const badgeSize = variant === 'large' ? 'h-7 w-7' : 'h-6 w-6';
  const badgePosition = variant === 'large' ? '-top-3 -right-3' : '-top-2 -right-2';
  const badgeTextSize = variant === 'large' ? 'text-sm font-bold' : 'text-xs font-bold';
  
  return (
    <div className="relative inline-block">
      <Link href="/notifications">
        <Button
          variant="ghost"
          size="icon"
          className={`${className} transition-all duration-300 ${isNewNotification ? 'scale-110' : ''}`}
          aria-label={`${localCount} notifications non lues`}
        >
          <motion.div
            animate={isNewNotification ? ringAnimation : (hasUnread ? pulseAnimation : {})}
          >
            <Bell className={`
              ${hasUnread ? 'text-[#004d25]' : 'text-gray-500'} 
              ${iconClassName}
            `} />
          </motion.div>
        </Button>
      </Link>
      
      {/* Badge de notification - Affiché uniquement s'il y a des notifications non lues */}
      {hasUnread && (
        <div 
          className={`
            absolute ${badgePosition} flex ${badgeSize} items-center justify-center 
            rounded-full ${badgeTextSize} font-medium text-white 
            bg-red-500
            shadow-sm
          `}
          style={{
            pointerEvents: 'none',
            zIndex: 100,
            border: '2px solid white'
          }}
        >
          {localCount}
        </div>
      )}
    </div>
  );
};

export default NotificationIcon;