@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --waabo-green: 150 100% 15%;
    --waabo-orange: 24 100% 50%;
    --waabo-yellow: 42 100% 50%;
    --waabo-red: 0 100% 40%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-gray-50 text-foreground;
  }

  /* Safe area support for mobile devices */
  .safe-area-app {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    min-height: 100vh;
    min-height: 100dvh;
    /* Dynamic viewport height for mobile */
  }

  /* Safe area layout for individual pages */
  .safe-area-layout {
    min-height: 100vh;
    min-height: 100dvh;
  }

  /* Safe area header - for fixed headers that need safe area top padding */
  .safe-area-header {
    padding-top: env(safe-area-inset-top);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Safe area content - for main content that needs safe area padding */
  .safe-area-content {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Ensure the app container respects safe areas */
  #root {
    min-height: 100vh;
    min-height: 100dvh;
  }
}

/* Custom styling for tracking steps */
.tracking-step-number {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
  font-size: 0.875rem;
}

/* All steps styling - all circles are green now */
.tracking-step .tracking-step-number {
  background-color: #004d25;
  color: white;
  border: none;
}

/* Active step (current) */
.tracking-step.active .tracking-step-number {
  background-color: #004d25;
  color: white;
  border: none;
  box-shadow: 0 0 0 4px rgba(0, 77, 37, 0.2);
}

.tracking-step.active .tracking-step-label {
  @apply text-neutral-900 font-semibold;
}

/* Completed steps */
.tracking-step.completed .tracking-step-number {
  background-color: #004d25;
  color: white;
  border: none;
}

.tracking-step.completed .tracking-step-label {
  @apply text-neutral-800 font-medium;
}

/* The last step (delivered) is also green now */
.tracking-step.last-step.active .tracking-step-number {
  background-color: #004d25;
  color: white;
  border: none;
}

/* Labels under circles */
.tracking-step-label {
  margin-top: 0.5rem;
  font-size: 0.7rem;
  text-align: center;
  width: 4.5rem;
  color: #6b7280;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
  line-height: 1.2;
}

/* Splash screen animation */
@keyframes pulse-logo {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.05);
    opacity: 1;
  }

  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.splash-logo-animation {
  animation: pulse-logo 2s infinite ease-in-out;
}