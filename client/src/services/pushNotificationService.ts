import { Capacitor } from '@capacitor/core';
import { 
  PushNotifications,
  ActionPerformed,
  PushNotificationSchema,
  Token,
  PushNotificationActionPerformed
} from '@capacitor/push-notifications';

/**
 * Service pour gérer les notifications push dans l'application mobile
 * Utilise le plugin @capacitor/push-notifications pour interagir avec les API natives
 */
class PushNotificationService {
  private isNative: boolean;
  private initialized: boolean = false;
  private registrationId: string | null = null;
  private onNotificationReceivedCallback: ((notification: PushNotificationSchema) => void) | null = null;
  private onNotificationActionPerformedCallback: ((notification: PushNotificationActionPerformed) => void) | null = null;

  constructor() {
    // Vérifier si nous sommes dans un environnement natif (mobile)
    this.isNative = Capacitor.isNativePlatform();
  }

  /**
   * Initialise le service de notifications push
   * Demande les autorisations et enregistre les écouteurs d'événements
   */
  public async initialize(): Promise<boolean> {
    if (!this.isNative || this.initialized) {
      return false;
    }

    try {
      // Demander l'autorisation pour envoyer des notifications
      const permissionStatus = await PushNotifications.requestPermissions();
      if (permissionStatus.receive !== 'granted') {
        console.warn('Les autorisations de notification push n\'ont pas été accordées');
        return false;
      }

      // Enregistrer l'appareil pour les notifications push
      await PushNotifications.register();

      // Écouter les événements de notifications
      this.addListeners();
      
      this.initialized = true;
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'initialisation des notifications push:', error);
      return false;
    }
  }

  /**
   * Ajoute les écouteurs d'événements pour les notifications push
   */
  private addListeners(): void {
    // Événement déclenché lors de l'enregistrement réussi
    PushNotifications.addListener('registration', (token: Token) => {
      console.log('Token d\'enregistrement push:', token.value);
      this.registrationId = token.value;
      
      // Envoyer le token au serveur
      this.sendTokenToServer(token.value);
    });

    // Événement déclenché en cas d'erreur
    PushNotifications.addListener('registrationError', (error: any) => {
      console.error('Erreur d\'enregistrement push:', error);
    });

    // Événement déclenché lors de la réception d'une notification
    PushNotifications.addListener('pushNotificationReceived', (notification: PushNotificationSchema) => {
      console.log('Notification reçue:', notification);
      // Si un callback est défini, l'appeler
      if (this.onNotificationReceivedCallback) {
        this.onNotificationReceivedCallback(notification);
      }
    });

    // Événement déclenché lorsqu'une action est effectuée sur une notification
    PushNotifications.addListener('pushNotificationActionPerformed', (action: ActionPerformed) => {
      console.log('Action effectuée sur une notification:', action);
      // Si un callback est défini, l'appeler
      if (this.onNotificationActionPerformedCallback) {
        this.onNotificationActionPerformedCallback(action);
      }
    });
  }

  /**
   * Envoie le token d'enregistrement au serveur pour l'associer à l'utilisateur
   */
  private async sendTokenToServer(token: string): Promise<void> {
    try {
      const response = await fetch('/api/push-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!response.ok) {
        console.error('Erreur lors de l\'envoi du token au serveur:', await response.text());
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi du token au serveur:', error);
    }
  }

  /**
   * Définit le callback à appeler lorsqu'une notification est reçue
   */
  public onNotificationReceived(callback: (notification: PushNotificationSchema) => void): void {
    this.onNotificationReceivedCallback = callback;
  }

  /**
   * Définit le callback à appeler lorsqu'une action est effectuée sur une notification
   */
  public onNotificationActionPerformed(callback: (action: PushNotificationActionPerformed) => void): void {
    this.onNotificationActionPerformedCallback = callback;
  }

  /**
   * Récupère le token d'enregistrement actuel
   */
  public getRegistrationId(): string | null {
    return this.registrationId;
  }

  /**
   * Vérifie si les notifications push sont disponibles
   */
  public isPushAvailable(): boolean {
    return this.isNative;
  }

  /**
   * Vérifie si le service a été initialisé
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Exporter une instance singleton du service
export const pushNotificationService = new PushNotificationService();