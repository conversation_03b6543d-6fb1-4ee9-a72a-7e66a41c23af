import { Capacitor } from '@capacitor/core';
import { FirebaseAuthentication } from '@capacitor-firebase/authentication';

// Interface pour stocker les informations de l'utilisateur Apple
export interface AppleUserInfo {
  uid: string;
  email?: string;
  displayName?: string;
  firstName?: string;
  lastName?: string;
}

/**
 * Service pour gérer l'authentification Apple via Capacitor pour les applications mobiles natives
 */
class CapacitorAppleAuthService {
  private isNative: boolean;

  constructor() {
    // Vérifier si nous sommes dans un environnement natif
    this.isNative = Capacitor.isNativePlatform();
  }

  /**
   * Vérifie si nous sommes sur une plateforme native (iOS/Android)
   */
  public isNativePlatform(): boolean {
    return this.isNative;
  }

  /**
   * Initialise le SDK Firebase
   */
  public async initialize(): Promise<void> {
    // Pas besoin d'initialisation spécifique pour Firebase Authentication
    // La configuration est gérée par Capacitor
    console.log('Firebase Authentication prêt pour Apple Sign In');
  }

  /**
   * Connexion avec Apple
   */
  public async login(): Promise<{
    idToken: string;
    userInfo: AppleUserInfo;
  } | null> {
    if (!this.isNative) return null;

    try {
      // Effectuer la connexion Apple native directement dans l'application
      const result = await FirebaseAuthentication.signInWithApple();
      
      if (!result || !result.user || !result.credential?.idToken) {
        console.error('Erreur: Token Apple manquant après la connexion');
        return null;
      }

      // Extraire les informations de l'utilisateur
      const userInfo: AppleUserInfo = {
        uid: result.user.uid,
        email: result.user.email || undefined,
        displayName: result.user.displayName || undefined,
        // Apple ne fournit pas toujours ces informations, donc on utilise des valeurs par défaut
        firstName: undefined,
        lastName: undefined
      };
      
      // Si des informations additionnelles sont disponibles, extraire les noms
      if (result.additionalUserInfo?.profile) {
        const profile = result.additionalUserInfo.profile as any;
        if (typeof profile.givenName === 'string') {
          userInfo.firstName = profile.givenName;
        }
        if (typeof profile.familyName === 'string') {
          userInfo.lastName = profile.familyName;
        }
      }
      
      return {
        idToken: result.credential.idToken,
        userInfo
      };
    } catch (error) {
      console.error('Erreur lors de la connexion Apple:', error);
      return null;
    }
  }

  /**
   * Déconnexion
   */
  public async logout(): Promise<void> {
    if (!this.isNative) return;

    try {
      await FirebaseAuthentication.signOut();
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
      throw error;
    }
  }
}

// Exporter une instance du service
export const capacitorAppleAuth = new CapacitorAppleAuthService();