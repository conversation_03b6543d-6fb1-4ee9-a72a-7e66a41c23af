import { Capacitor } from '@capacitor/core';
import { FirebaseAuthentication } from '@capacitor-firebase/authentication';
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

// Interface pour stocker les informations de l'utilisateur Google
export interface GoogleUserInfo {
  uid: string;
  email?: string;
  displayName?: string;
  photoUrl?: string;
  firstName?: string;
  lastName?: string;
}

/**
 * Service pour gérer l'authentification Google via Capacitor pour les applications mobiles natives
 */
class CapacitorGoogleAuthService {
  private isNative: boolean;

  constructor() {
    // Vérifier si nous sommes dans un environnement natif
    this.isNative = Capacitor.isNativePlatform();
  }

  /**
   * Vérifie si nous sommes sur une plateforme native (iOS/Android)
   */
  public isNativePlatform(): boolean {
    return this.isNative;
  }

  /**
   * Initialise le SDK Firebase
   */
  public async initialize(): Promise<void> {
    // Pas besoin d'initialisation spécifique pour Firebase Authentication
    // La configuration est gérée par Capacitor
    console.log('Firebase Authentication prêt à être utilisé');
  }

  /**
   * Vérifie si l'utilisateur est connecté à Google
   */
  public async getCurrentToken(): Promise<string | null> {
    if (!this.isNative) return null;

    try {
      const result = await FirebaseAuthentication.getCurrentUser();

      if (result && result.user) {
        // Obtenir le token ID
        const tokenResult = await FirebaseAuthentication.getIdToken();
        return tokenResult.token;
      }

      return null;
    } catch (error) {
      console.error('Erreur lors de la vérification du token Google:', error);
      return null;
    }
  }



  /**
   * Connexion avec Google via Firebase
   */
  public async login(): Promise<{
    idToken: string;
    userInfo: GoogleUserInfo;
  } | null> {
    if (!this.isNative) return null;

    try {
      
      const result = await FirebaseAuthentication.signInWithGoogle();

      console.log('=== GOOGLE LOGIN RESULT ===');
      console.log('Raw result:', result);
      console.log('Result type:', typeof result);
      console.log('Result keys:', result ? Object.keys(result) : 'null');

      if (result) {
        console.log('=== DETAILED RESULT ANALYSIS ===');
        console.log('result.user:', result.user);
        console.log('result.user type:', typeof result.user);
        console.log('result.user keys:', result.user ? Object.keys(result.user) : 'null');
        console.log('result.credential:', result.credential);
        console.log('result.credential type:', typeof result.credential);
        console.log('result.credential keys:', result.credential ? Object.keys(result.credential) : 'null');

        if (result.user) {
          console.log('User details:');
          console.log('- uid:', result.user.uid);
          console.log('- email:', result.user.email);
          console.log('- displayName:', result.user.displayName);
          console.log('- photoUrl:', result.user.photoUrl);
        }

        if (result.credential) {
          console.log('Credential details:');
          console.log('- idToken exists:', !!result.credential.idToken);
          console.log('- idToken length:', result.credential.idToken?.length);
          console.log('- accessToken exists:', !!result.credential.accessToken);
        }
        console.log('=== END DETAILED ANALYSIS ===');
      }

      if (!result || !result.user || !result.credential?.idToken) {
        console.error('ERROR: Missing required data in Google login result');
        console.error('result exists:', !!result);
        console.error('result.user exists:', !!(result && result.user));
        console.error('result.credential exists:', !!(result && result.credential));
        console.error('result.credential.idToken exists:', !!(result && result.credential && result.credential.idToken));
        return null;
      }

      // Extraire les informations de l'utilisateur
      const userInfo: GoogleUserInfo = {
        uid: result.user.uid,
        email: result.user.email || undefined,
        displayName: result.user.displayName || undefined,
        photoUrl: result.user.photoUrl || undefined,
        firstName: this.extractFirstName(result.user.displayName),
        lastName: this.extractLastName(result.user.displayName)
      };

      const authResult = {
        idToken: result.credential.idToken,
        userInfo
      };

      console.log('=== FINAL AUTH RESULT ===');
      console.log('authResult:', authResult);
      console.log('=== GOOGLE LOGIN DEBUG END ===');

      return authResult;
    } catch (error) {
      console.error('=== GOOGLE LOGIN ERROR ===');
      console.error('Error type:', typeof error);
      console.error('Error name:', (error as Error)?.name);
      console.error('Error message:', (error as Error)?.message);
      console.error('Error stack:', (error as Error)?.stack);
      console.error('Full error object:', error);
      console.error('=== END GOOGLE LOGIN ERROR ===');
      return null;
    }
  }

  /**
   * Extrait le prénom à partir du nom complet
   */
  private extractFirstName(displayName: string | null): string | undefined {
    if (!displayName) return undefined;

    const nameParts = displayName.trim().split(' ');
    return nameParts[0] || undefined;
  }

  /**
   * Extrait le nom de famille à partir du nom complet
   */
  private extractLastName(displayName: string | null): string | undefined {
    if (!displayName) return undefined;

    const nameParts = displayName.trim().split(' ');
    if (nameParts.length > 1) {
      // Prendre tout ce qui est après le premier mot comme nom de famille
      return nameParts.slice(1).join(' ');
    }

    return undefined;
  }

  /**
   * Déconnexion de Google
   */
  public async logout(): Promise<void> {
    if (!this.isNative) return;

    try {
      await FirebaseAuthentication.signOut();
    } catch (error) {
      console.error('Erreur lors de la déconnexion Google:', error);
      throw error;
    }
  }
}

// Exporter une instance du service
export const capacitorGoogleAuth = new CapacitorGoogleAuthService();