import { Capacitor, CapacitorHttp } from '@capacitor/core';
import { FacebookLogin } from '@capacitor-community/facebook-login';

// Interface pour stocker les informations de l'utilisateur Facebook
export interface FacebookUserInfo {
  id: string;
  email?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  picture?: {
    data: {
      url: string;
    }
  };
}

/**
 * Service pour gérer l'authentification Facebook via Capacitor pour les applications mobiles natives
 */
class CapacitorFacebookAuthService {
  private isNative: boolean;

  constructor() {
    // Vérifier si nous sommes dans un environnement natif
    this.isNative = Capacitor.isNativePlatform();
  }

  /**
   * Vérifie si nous sommes sur une plateforme native (iOS/Android)
   */
  public isNativePlatform(): boolean {
    return this.isNative;
  }

  /**
   * Initialise le SDK Facebook
   */
  public async initialize(): Promise<void> {
    if (!this.isNative) return;

    try {
      // Initialiser le SDK Facebook avec l'ID de l'application
      await FacebookLogin.initialize({
        appId: '3606778226281282',
      });
      console.log('Facebook SDK initialisé avec succès sur la plateforme native');
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du SDK Facebook:', error);
      throw error;
    }
  }

  /**
   * Vérifie si l'utilisateur est connecté à Facebook
   */
  public async getCurrentToken(): Promise<string | null> {
    if (!this.isNative) return null;

    try {
      const result = await FacebookLogin.getCurrentAccessToken();
      return result.accessToken?.token || null;
    } catch (error) {
      console.error('Erreur lors de la vérification du token Facebook:', error);
      return null;
    }
  }

  /**
   * Connexion avec Facebook
   */
  public async login(): Promise<{
    accessToken: string;
    userInfo: FacebookUserInfo;
  } | null> {
    if (!this.isNative) return null;

    try {
      // Vérifier si déjà connecté
      const currentToken = await this.getCurrentToken();
      if (currentToken) {
        console.log('Utilisateur déjà connecté, utilisation du token existant');
        // Pour les tokens existants, utiliser des infos basiques
        const userInfo: FacebookUserInfo = {
          id: 'existing_user',
          name: 'Facebook User',
          email: undefined,
          firstName: undefined,
          lastName: undefined
        };
        return {
          accessToken: currentToken,
          userInfo
        };
      }

      // Effectuer la connexion Facebook native
      console.log('Démarrage de la connexion Facebook...');
      const result = await FacebookLogin.login({
        permissions: ['email', 'public_profile']
      });

      console.log('Résultat de la connexion Facebook:', result);

      if (!result || !result.accessToken || !result.accessToken.token) {
        console.error('Erreur: Token Facebook manquant après la connexion');
        return null;
      }

      // Utiliser les informations utilisateur directement du résultat Facebook
      const userInfo: FacebookUserInfo = {
        id: result.accessToken.userId || 'unknown',
        name: (result.accessToken as any).name || 'Facebook User',
        email: (result.accessToken as any).email || undefined,
        firstName: undefined,
        lastName: undefined
      };

      console.log('Informations utilisateur Facebook depuis la réponse:', userInfo);

      return {
        accessToken: result.accessToken.token,
        userInfo
      };
    } catch (error) {
      console.error('Erreur lors de la connexion Facebook:', error);
      return null;
    }
  }

  /**
   * Récupère les informations de l'utilisateur Facebook
   */
  public async getUserInfo(token: string): Promise<FacebookUserInfo> {
    try {
      console.log('=== FACEBOOK USER INFO REQUEST ===');
      console.log('Access token length:', token.length);
      console.log('Access token preview:', token.substring(0, 50) + '...');

      const url = `https://graph.facebook.com/me?fields=id,name,email,first_name,last_name,picture.type(large)&access_token=${token}`;
      console.log('Facebook Graph API URL:', url.substring(0, 100) + '...');

      // Use CapacitorHttp for iOS compatibility
      const response = await CapacitorHttp.request({
        url: url,
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      console.log('Facebook API Response Status:', response.status);
      console.log('Facebook API Response Data:', response.data);

      if (response.status !== 200) {
        console.error('Facebook API Error Response:', response.data);
        throw new Error(`Erreur API Facebook: ${response.status} - ${JSON.stringify(response.data)}`);
      }

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des informations Facebook:', error);
      throw error;
    }
  }

  /**
   * Déconnexion de Facebook
   */
  public async logout(): Promise<void> {
    if (!this.isNative) return;

    try {
      await FacebookLogin.logout();
    } catch (error) {
      console.error('Erreur lors de la déconnexion Facebook:', error);
      throw error;
    }
  }
}

// Exporter une instance du service
export const capacitorFacebookAuth = new CapacitorFacebookAuthService();