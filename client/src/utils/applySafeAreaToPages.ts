/**
 * Utility to apply safe area classes to all page components
 * This ensures consistent safe area handling across the entire app
 */

export const SAFE_AREA_CLASSES = {
  // Main container for pages
  layout: 'safe-area-layout',
  
  // For fixed headers that need safe area top padding
  header: 'safe-area-header',
  
  // For main content that needs safe area padding
  content: 'safe-area-content',
  
  // Combined classes for common patterns
  pageContainer: 'min-h-screen flex flex-col safe-area-layout',
  fixedHeader: 'bg-white shadow-sm fixed top-0 left-0 right-0 z-50 safe-area-header',
  staticHeader: 'bg-white shadow-sm safe-area-header',
  mainContent: 'flex-grow safe-area-content',
} as const;

/**
 * Get safe area classes for different page elements
 */
export const getSafeAreaClasses = (element: keyof typeof SAFE_AREA_CLASSES) => {
  return SAFE_AREA_CLASSES[element];
};

/**
 * Pages that need safe area updates
 */
export const PAGES_TO_UPDATE = [
  'client/src/pages/Home.tsx',
  'client/src/pages/ProfilePage.tsx',
  'client/src/pages/ProductsPage.tsx',
  'client/src/pages/ProductDetail.tsx',
  'client/src/pages/NotificationsPage.tsx',
  'client/src/pages/SavedOrdersPage.tsx',
  'client/src/pages/ShippingMarkPage.tsx',
  'client/src/pages/auth-page.tsx',
  'client/src/pages/ForgotPasswordScreen.tsx',
  'client/src/pages/ResetPasswordScreen.tsx',
  'client/src/pages/EmailVerificationPage.tsx',
] as const;
