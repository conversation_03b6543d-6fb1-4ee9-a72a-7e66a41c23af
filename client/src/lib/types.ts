// User-related types
export interface User {
  id: number;
  username: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  phoneVerified?: boolean;
  phoneVerificationToken?: string;
  provider?: string;
  providerId?: string;
  emailVerified?: boolean;
  emailVerificationToken?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Order status types
export type OrderStatus = 
  | 'ORDER_CONFIRMED' 
  | 'PROCESSING' 
  | 'IN_TRANSIT' 
  | 'ARRIVED' 
  | 'DELIVERED';

export interface Order {
  id: number;
  reference: string;
  userId?: number;
  status: OrderStatus;
  estimatedDelivery?: Date;
  latestUpdate?: string;
  latestUpdateTime?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Product-related types
export interface Product {
  id: number;
  name: string;
  description: string;
  price: string;
  imageUrl: string;
  imageUrls?: string[]; // Array d'URLs d'images
  hasFreeshipping: boolean;
  features: string[];
  specifications: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

// Notification-related types
export interface Notification {
  id: number;
  title: string;
  body: string;
  sentAt: Date;
  sent_at?: string; // Format snake_case pour compatibilité avec l'API
  targetUserId?: number;
  target_user_id?: number; // Format snake_case pour compatibilité avec l'API
  isGlobal: boolean;
  is_global?: boolean; // Format snake_case pour compatibilité avec l'API
  isRead?: boolean;
  is_read?: boolean; // Format snake_case pour compatibilité avec l'API
}

// Step interface for tracking progress
export interface TrackingStep {
  number: number;
  label: string;
  status: 'completed' | 'active' | 'pending';
  timestamp?: Date;
}

// Popup notification types
export interface PopupNotification {
  id: number;
  title: string;
  content: string;
  imageUrl?: string;
  backgroundColor: string;
  textColor: string;
  buttonColor: string;
  buttonTextColor: string;
  showOnce: boolean;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  buttonText: string;
  buttonLink?: string;
  titleFontSize: string;
  contentFontSize: string;
  borderRadius: string;
  width: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// App settings types
export interface AppSetting {
  id: number;
  key: string;
  value: string;
  description?: string;
  updatedAt: Date;
}
