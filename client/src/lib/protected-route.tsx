import { FC } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Redirect, Route } from "wouter";

export interface ProtectedRouteProps {
  path: string;
  component: () => React.ReactNode;
}

export const ProtectedRoute: FC<ProtectedRouteProps> = ({
  path,
  component: Component,
}) => {
  const { user, isLoading } = useAuth();

  return (
    <Route path={path}>
      {() => {
        if (isLoading) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
            </div>
          );
        }

        if (!user) {
          return <Redirect to="/auth" />;
        }

        return <Component />;
      }}
    </Route>
  );
};

export const AdminRoute: FC<ProtectedRouteProps> = ({
  path,
  component: Component,
}) => {
  const { user, isLoading } = useAuth();

  return (
    <Route path={path}>
      {() => {
        if (isLoading) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
            </div>
          );
        }

        if (!user) {
          return <Redirect to="/auth" />;
        }

        // Vérifier si l'utilisateur est un administrateur
        if (user.email !== "<EMAIL>") {
          return <Redirect to="/" />;
        }

        return <Component />;
      }}
    </Route>
  );
};