import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<any> {
  // Add a proper prefix to the URL if it doesn't already have it
  const apiUrl = url.startsWith('http') ? url : url.startsWith('/') ? url : `/${url}`;

  // Prepare headers
  const headers: Record<string, string> = {};

  if (data) {
    headers["Content-Type"] = "application/json";
  }

  // Add JWT token if available (for mobile)
  const jwtToken = localStorage.getItem('waabo_jwt_token');
  console.log('🔑 JWT Token from localStorage:', jwtToken ? 'EXISTS' : 'NOT FOUND');
  if (jwtToken) {
    headers["Authorization"] = `Bearer ${jwtToken}`;
    console.log('🔑 Authorization header set:', `Bearer ${jwtToken.substring(0, 20)}...`);
  }

  const res = await fetch(apiUrl, {
    method,
    headers,
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include", // Still include for web session fallback
  });

  await throwIfResNotOk(res);
  
  // Vérifier si la réponse est vide ou n'est pas JSON
  const contentType = res.headers.get('content-type');
  if (contentType && contentType.includes('application/json') && res.status !== 204) {
    try {
      return await res.json();
    } catch (error) {
      console.error('Erreur lors du parsing JSON:', error);
      return {}; // Retourner un objet vide en cas d'erreur de parsing
    }
  }
  
  // Retourner un objet vide pour les réponses non-JSON ou vides
  return {};
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Ensure the URL is properly formatted
    const url = queryKey[0] as string;
    const apiUrl = url.startsWith('http') ? url : url.startsWith('/') ? url : `/${url}`;
    
    const res = await fetch(apiUrl, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    
    // Vérifier si la réponse est vide ou n'est pas JSON
    const contentType = res.headers.get('content-type');
    if (contentType && contentType.includes('application/json') && res.status !== 204) {
      try {
        return await res.json();
      } catch (error) {
        console.error('Erreur lors du parsing JSON:', error);
        return {}; // Retourner un objet vide en cas d'erreur de parsing
      }
    }
    
    // Retourner un objet vide pour les réponses non-JSON ou vides
    return {};
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
