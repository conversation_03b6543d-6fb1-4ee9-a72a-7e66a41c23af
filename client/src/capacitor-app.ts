import { SplashScreen } from '@capacitor/splash-screen';

export const hideAppSplashScreen = async () => {
  // Masquer l'écran de démarrage après l'initialisation de l'application
  await SplashScreen.hide();
};

export const showAppSplashScreen = async () => {
  // Afficher l'écran de démarrage
  await SplashScreen.show({
    autoHide: false
  });
};

export const initializeCapacitor = async () => {
  // Initialiser les plugins si l'application est exécutée dans un environnement natif
  if ('Capacitor' in window) {
    try {
      // On pourrait ajouter d'autres initialisations de plugins ici
      await hideAppSplashScreen();
      console.log('Capacitor initialisé avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de Capacitor:', error);
    }
  } else {
    console.log('Application exécutée dans un navigateur web standard');
  }
};