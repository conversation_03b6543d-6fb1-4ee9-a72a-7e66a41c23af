import { Switch, Route } from "wouter";
import SplashScreen from "@/pages/SplashScreen";
import ForgotPasswordScreen from "@/pages/ForgotPasswordScreen";
import ResetPasswordScreen from "@/pages/ResetPasswordScreen";
import EmailVerificationPage from "@/pages/EmailVerificationPage";
import Home from "@/pages/Home";
import ProductDetail from "@/pages/ProductDetail";
import ProductsPage from "@/pages/ProductsPage";
import ProfilePage from "@/pages/ProfilePage";
import NotificationsPage from "@/pages/NotificationsPage";
import SavedOrdersPage from "@/pages/SavedOrdersPage";
import ShippingMarkPage from "@/pages/ShippingMarkPage";
import StaticPage from "@/pages/StaticPage";
import AdminDashboard from "@/pages/Admin/Dashboard";
import OrderManagement from "@/pages/Admin/OrderManagement";
import OrderStatusManagement from "@/pages/Admin/OrderStatusManagement";
import ProductManagement from "@/pages/Admin/ProductManagement";
import NotificationManagement from "@/pages/Admin/NotificationManagement";
import PopupNotificationManagement from "@/pages/Admin/PopupNotificationManagement";
import UserManagement from "@/pages/Admin/UserManagement";
import ContactMessages from "@/pages/Admin/ContactMessages";
import StaticPagesList from "@/pages/Admin/StaticPagesList";
import EditStaticPage from "@/pages/Admin/EditStaticPage";
import SettingsManagement from "@/pages/Admin/SettingsManagement";
import WarehouseSettingsPage from "@/pages/Admin/WarehouseSettingsPage";
import NotFound from "@/pages/not-found";
import AuthPage from "@/pages/auth-page";
import FacebookHelpPage from "@/pages/FacebookHelpPage";
import { ProtectedRoute, AdminRoute } from "@/lib/protected-route";
import { useState, useEffect, useCallback } from "react";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { AuthProvider, useAuth } from "@/hooks/use-auth";
import { PopupNotificationProvider, usePopupNotifications } from "@/hooks/use-popup-notifications";
import { useMobileUser } from "@/hooks/use-mobile-user";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { PopupNotification as PopupNotificationComponent } from "@/components/ui/popup-notification";

// Composant qui gère l'affichage des popups quand elles sont disponibles
function ActivePopupNotification() {
  const { currentPopup, closePopup, isPopupOpen } = usePopupNotifications();
  const { user } = useAuth();

  const handleClose = useCallback(() => {
    if (currentPopup && user) {
      // Marquer manuellement cette popup comme vue pour éviter qu'elle ne réapparaisse
      fetch(`/api/popup-notifications/${currentPopup.id}/seen`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      })
        .then(() => {
          console.log(`Popup #${currentPopup.id} marked as seen via direct API call`);
          // Fermer la popup uniquement après avoir confirmé qu'elle a été marquée comme vue
          closePopup();
        })
        .catch(err => {
          console.error("Error marking popup as seen:", err);
          // Fermer quand même en cas d'erreur
          closePopup();
        });
    } else {
      // Si pas d'utilisateur, fermer simplement
      closePopup();
    }
  }, [currentPopup, user, closePopup]);

  return (
    <>
      {isPopupOpen && currentPopup && (
        <PopupNotificationComponent
          id={currentPopup.id}
          title={currentPopup.title}
          content={currentPopup.content}
          backgroundColor={currentPopup.backgroundColor}
          textColor={currentPopup.textColor}
          buttonColor={currentPopup.buttonColor}
          buttonTextColor={currentPopup.buttonTextColor}
          titleFontSize={currentPopup.titleFontSize}
          contentFontSize={currentPopup.contentFontSize}
          buttonText={currentPopup.buttonText}
          buttonLink={currentPopup.buttonLink}
          borderRadius={currentPopup.borderRadius}
          width={currentPopup.width}
          imageUrl={currentPopup.imageUrl}
          onClose={handleClose}
        />
      )}
    </>
  );
};

function App() {
  const [showSplash, setShowSplash] = useState(true);
  const [location, setLocation] = useState(window.location.search);
  const { toast } = useToast(); // Utiliser le hook useToast

  // Restore user từ localStorage cho mobile apps
  useMobileUser();

  // Effet pour détecter les paramètres d'URL après l'authentification
  useEffect(() => {
    // Fonction pour analyser les paramètres de requête URL
    const getQueryParams = (search: string) => {
      const params = new URLSearchParams(search);
      const result: Record<string, string> = {};

      // Convertir les entrées en un format sécurisé pour TS
      Array.from(params.entries()).forEach(([key, value]) => {
        result[key] = value;
      });

      return result;
    };

    // Lire les paramètres de l'URL
    const params = getQueryParams(location);

    // Si l'authentification a réussi
    if (params.login_success === 'true') {
      const provider = params.provider || 'social';
      const providerName = provider === 'facebook' ? 'Facebook' : provider === 'google' ? 'Google' : 'OAuth';

      // Afficher un toast de bienvenue
      toast({
        title: 'Connexion réussie',
        description: `Vous êtes maintenant connecté avec ${providerName}`,
        variant: 'default',
      });

      // Nettoyer l'URL pour supprimer les paramètres
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [location, toast]);

  // Le SplashScreen gère lui-même son timing et appelle onFinish quand il est prêt
  if (showSplash) {
    return (
      <div className="safe-area-app">
        <SplashScreen onFinish={() => setShowSplash(false)} />
      </div>
    );
  }

  return (
    <div className="safe-area-app">
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <PopupNotificationProvider>
            <Switch>
              {/* Public routes */}
              <Route path="/auth" component={AuthPage} />
              <Route path="/forgot-password" component={ForgotPasswordScreen} />
              <Route path="/reset-password" component={ResetPasswordScreen} />
              <Route path="/verify-email" component={EmailVerificationPage} />
              <Route path="/facebook-help" component={FacebookHelpPage} />

              {/* Protected routes */}
              <ProtectedRoute path="/" component={() => <Home />} />
              <ProtectedRoute path="/home" component={() => <Home />} />
              <ProtectedRoute path="/products" component={() => <ProductsPage />} />
              <ProtectedRoute path="/product/:id" component={() => <ProductDetail />} />
              <ProtectedRoute path="/profile" component={() => <ProfilePage />} />
              <ProtectedRoute path="/notifications" component={() => <NotificationsPage />} />
              <ProtectedRoute path="/saved-orders" component={() => <SavedOrdersPage />} />
              <ProtectedRoute path="/shipping-mark" component={() => <ShippingMarkPage />} />

              {/* Admin routes - accessible <NAME_EMAIL> */}
              <AdminRoute path="/admin" component={() => <AdminDashboard />} />
              <AdminRoute path="/admin/orders" component={() => <OrderManagement />} />
              <AdminRoute path="/admin/order-statuses" component={() => <OrderStatusManagement />} />
              <AdminRoute path="/admin/products" component={() => <ProductManagement />} />
              <AdminRoute path="/admin/notifications" component={() => <NotificationManagement />} />
              <AdminRoute path="/admin/popup-notifications" component={() => <PopupNotificationManagement />} />
              <AdminRoute path="/admin/users" component={() => <UserManagement />} />
              <AdminRoute path="/admin/contact-messages" component={() => <ContactMessages />} />
              <AdminRoute path="/admin/static-pages" component={StaticPagesList} />
              <AdminRoute path="/admin/static-pages/edit/:slug" component={EditStaticPage} />
              <AdminRoute path="/admin/settings" component={SettingsManagement} />
              <AdminRoute path="/admin/warehouse-settings" component={() => <WarehouseSettingsPage />} />

              {/* Static pages - doit être avant la page 404 mais après les routes spécifiques */}
              <Route path="/about" component={() => <StaticPage />} />
              <Route path="/support" component={() => <StaticPage />} />
              <Route path="/contact" component={() => <StaticPage />} />
              <Route path="/privacy" component={() => <StaticPage />} />
              <Route path="/terms" component={() => <StaticPage />} />
              <Route path="/faq" component={() => <StaticPage />} />

              {/* Fallback to 404 */}
              <Route component={NotFound} />
            </Switch>
            <ActivePopupNotification />
            <Toaster />
          </PopupNotificationProvider>
        </AuthProvider>
      </QueryClientProvider>
    </div>
  );
}

export default App;
