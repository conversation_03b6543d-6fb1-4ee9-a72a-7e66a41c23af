import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Notification } from '@/lib/types';
import { useAuth } from '@/hooks/use-auth';
import { apiRequest, queryClient } from '@/lib/queryClient';

export function useNotifications() {
  const { user } = useAuth();
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);

  // Récupérer les notifications depuis le serveur
  const { data: notifications = [], isLoading } = useQuery<Notification[]>({
    queryKey: user?.id ? [`/api/notifications/user/${user.id}`] : [],
    queryFn: async () => {
      if (!user?.id) return [];
      try {
        const res = await fetch(`/api/notifications/user/${user.id}`);
        if (!res.ok) {
          throw new Error('Erreur lors de la récupération des notifications');
        }
        const data = await res.json();
        console.log("Notifications récupérées:", data.length, data);
        return data;
      } catch (error) {
        console.error("Erreur lors de la récupération des notifications:", error);
        return [];
      }
    },
    enabled: !!user?.id,
    refetchInterval: 10000, // Actualiser toutes les 10 secondes
  });

  // Mettre à jour le compteur de notifications non lues
  useEffect(() => {
    if (notifications && Array.isArray(notifications)) {
      // Filtrer les notifications non lues (personnelles et globales)
      const unread = notifications.filter(n => {
        // Une notification est considérée comme "pour cet utilisateur" si:
        // - Elle est globale (targetUserId est null)
        // - OU elle est spécifiquement adressée à cet utilisateur
        const isForThisUser = n.targetUserId === null || n.targetUserId === user?.id;
        // Et elle n'a pas été lue
        return isForThisUser && !n.isRead;
      });
      
      console.log("[use-notifications] Toutes les notifications:", notifications.length);
      console.log("[use-notifications] Détail des notifications:", 
        notifications.map(n => ({ id: n.id, title: n.title, isRead: n.isRead, isGlobal: n.targetUserId === null }))
      );
      console.log("[use-notifications] Notifications non lues:", unread.length, unread);
      
      // Ajout d'instructions de debug supplémentaires
      const isReadStructure = notifications.map(n => n.isRead);
      console.log("[use-notifications] Structure isRead:", isReadStructure);
      
      setUnreadCount(unread.length);
      setInitialLoadComplete(true);
    }
  }, [notifications, user?.id]);

  // Configurer WebSocket pour les notifications en temps réel
  useEffect(() => {
    if (!user?.id) return;

    // Créer une nouvelle connexion WebSocket
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket connecté');
      // Authentifier la connexion avec l'ID de l'utilisateur
      ws.send(JSON.stringify({ type: 'auth', userId: user.id }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        
        if (message.type === 'notification') {
          const newNotification = message.data;
          
          // Augmenter le compteur de notifications non lues
          setUnreadCount(prev => prev + 1);
          
          // Mettre à jour le cache React Query
          queryClient.setQueryData(
            [`/api/notifications/user/${user.id}`],
            (oldData: Notification[] | undefined) => {
              // Vérifier si la notification existe déjà dans le cache
              const exists = oldData?.some(n => n.id === newNotification.id);
              if (exists || !oldData) return oldData;
              
              // Ajouter la nouvelle notification au cache
              return [...oldData, newNotification];
            }
          );
        }
      } catch (error) {
        console.error('Erreur lors du traitement du message WebSocket:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('Erreur WebSocket:', error);
    };

    ws.onclose = () => {
      console.log('WebSocket déconnecté');
    };

    setWebsocket(ws);

    // Nettoyer lors du démontage
    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [user?.id]);

  // Marquer une notification comme lue
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: number) => {
      await apiRequest('PATCH', `/api/notifications/${notificationId}/read`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/notifications/user/${user?.id}`] });
    },
  });

  // Fonction pour marquer une notification comme lue
  const markAsRead = useCallback((notificationId: number) => {
    markAsReadMutation.mutate(notificationId);
    // Réduire le compteur de notifications non lues immédiatement pour une meilleure UX
    setUnreadCount(prev => Math.max(0, prev - 1));
  }, [markAsReadMutation]);

  // Trier les notifications par date (plus récentes en haut)
  const sortedNotifications = [...notifications].sort((a, b) => {
    // Convertir les dates en objets Date
    const dateA = new Date(a.sentAt || 0);
    const dateB = new Date(b.sentAt || 0);
    // Tri décroissant (plus récent d'abord)
    return dateB.getTime() - dateA.getTime();
  });

  return {
    // Ne retourner que les notifications non lues, triées par date
    notifications: sortedNotifications.filter(n => !n.isRead),
    // Toutes les notifications triées par date
    allNotifications: sortedNotifications,
    unreadCount,
    isLoading: isLoading || !initialLoadComplete,
    markAsRead,
  };
}