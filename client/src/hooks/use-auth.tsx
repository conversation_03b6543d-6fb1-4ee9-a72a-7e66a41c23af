import { createContext, ReactNode, useContext } from "react";
import {
  useQuery,
  useMutation,
  UseMutationResult,
} from "@tanstack/react-query";
import { User } from "@/lib/types";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  loginMutation: UseMutationResult<User, Error, LoginData>;
  logoutMutation: UseMutationResult<void, Error, void>;
  registerMutation: UseMutationResult<User, Error, RegisterData>;
  refreshUserData: () => Promise<User | null>;
};

type LoginData = {
  email: string; // Ce champ contient soit l'email, soit le nom d'utilisateur
  password: string;
};

type RegisterData = {
  firstName?: string;
  lastName?: string;
  username?: string;
  email: string;
  phone?: string;
  password: string;
};

export const AuthContext = createContext<AuthContextType | null>(null);
export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const {
    data: user = null,
    error,
    isLoading,
  } = useQuery<User | null, Error>({
    queryKey: ["/api/user"],
    queryFn: async () => {
      try {
        // Try server first (web mode with session)
        console.log('Trying to fetch user from server...');
        return await apiRequest("GET", "/api/user");
      } catch (error) {
        console.error("Server auth failed:", error);

        // Fallback: check persistent storage (mobile mode)
        console.log('Checking persistent storage for user data...');
        const savedUser = localStorage.getItem('waabo_user');
        if (savedUser) {
          try {
            const user = JSON.parse(savedUser);
            console.log('User restored from persistent storage:', user);
            queryClient.setQueryData(["/api/user"], user);
            return user;
          } catch (parseError) {
            console.error('Error parsing saved user:', parseError);
            localStorage.removeItem('waabo_user'); // Clean up invalid data
          }
        }

        console.log('No user found in persistent storage');
        return null;
      }
    },
  });

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData) => {
      return await apiRequest("POST", "/api/auth/login", credentials);
    },
    onSuccess: (user: User) => {
      queryClient.setQueryData(["/api/user"], user);
      toast({
        title: "Connexion réussie",
        description: "Bienvenue sur Waabo Tracking",
        variant: "default",
      });
      // Rediriger vers la page d'accueil après connexion réussie
      window.location.href = '/';
    },
    onError: (error: Error) => {
      toast({
        title: "Erreur de connexion",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (userData: RegisterData) => {
      return await apiRequest("POST", "/api/auth/register", userData);
    },
    onSuccess: (user: User) => {
      queryClient.setQueryData(["/api/user"], user);
      toast({
        title: "Inscription réussie",
        description: "Bienvenue sur Waabo Tracking",
        variant: "default",
      });
      // Rediriger vers la page d'accueil après inscription réussie
      window.location.href = '/';
    },
    onError: (error: Error) => {
      toast({
        title: "Erreur d'inscription",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest("POST", "/api/auth/logout");
    },
    onSuccess: () => {
      // Clear React Query cache
      queryClient.setQueryData(["/api/user"], null);

      // Clear ALL persistent storage items (mobile)
      localStorage.removeItem('waabo_user');
      localStorage.removeItem('waabo_jwt_token');
      localStorage.removeItem('authToken');

      // Clear Zustand auth storage
      localStorage.removeItem('waabo-auth-storage');

      console.log('All authentication data cleared from localStorage');

      toast({
        title: "Déconnexion réussie",
        description: "Vous avez été déconnecté avec succès",
        variant: "default",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Erreur de déconnexion",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Fonction pour rafraîchir les données de l'utilisateur
  const refreshUserData = async (): Promise<User | null> => {
    try {
      const refreshedUser = await apiRequest("GET", "/api/user");
      queryClient.setQueryData(["/api/user"], refreshedUser);
      return refreshedUser;
    } catch (error) {
      console.error("Erreur lors du rafraîchissement des données utilisateur:", error);
      return null;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        error,
        loginMutation,
        logoutMutation,
        registerMutation,
        refreshUserData
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth doit être utilisé à l'intérieur d'un AuthProvider");
  }
  return context;
}