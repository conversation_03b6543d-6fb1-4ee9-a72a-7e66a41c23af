import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook để restore user data từ localStorage vào React Query cache
 * Chỉ dành cho mobile apps
 */
export function useMobileUser() {
  const queryClient = useQueryClient();

  useEffect(() => {
    // Check if we have saved user data in localStorage
    const savedUser = localStorage.getItem('waabo_user');
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        console.log('Mobile: Restoring user from localStorage to React Query cache:', user);
        
        // Set user data vào React Query cache
        queryClient.setQueryData(["/api/user"], user);
        
        console.log('Mobile: User data restored successfully');
      } catch (error) {
        console.error('Error parsing saved user data:', error);
        // Clean up invalid data
        localStorage.removeItem('waabo_user');
      }
    }
  }, [queryClient]);
}
