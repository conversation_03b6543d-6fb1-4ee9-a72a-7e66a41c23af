import React, { createContext, useState, useContext, useEffect, ReactNode, useRef } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';
import { apiRequest } from '@/lib/queryClient';
import { PopupNotification } from '@/lib/types';
import { PopupNotification as PopupNotificationComponent } from '@/components/ui/popup-notification';
import { useAuth } from './use-auth';

type PopupContextType = {
  currentPopup: PopupNotification | null;
  isPopupOpen: boolean;
  closePopup: () => void;
  showPopup: (popup: PopupNotification) => void;
  closeAllPopups: () => void;
  isLoading: boolean;
};

const PopupContext = createContext<PopupContextType | undefined>(undefined);

export const PopupNotificationProvider = ({ children }: { children: ReactNode }) => {
  const [currentPopup, setCurrentPopup] = useState<PopupNotification | null>(null);
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  
  // Flag pour suivre si nous avons déjà affiché un popup dans cette session
  const [sessionPopupShown, setSessionPopupShown] = useState(false);
  // Flag pour suivre si l'utilisateur vient de se connecter
  const previousUserRef = useRef(user);
  const userJustLoggedIn = previousUserRef.current === null && user !== null;

  // Requête pour obtenir les notifications popup non vues par l'utilisateur
  const {
    data: unseenPopups = [] as PopupNotification[],
    isLoading: isLoadingPopups,
    refetch,
  } = useQuery<PopupNotification[]>({
    queryKey: [`/api/popup-notifications/unseen/user/${user?.id}`, user?.id],
    enabled: !!user && !!user.id,
    staleTime: 60000
  });

  // Mutation pour marquer une notification comme vue
  const markAsSeen = useMutation({
    mutationFn: ({ userId, popupId }: { userId: number; popupId: number }) =>
      apiRequest(`/api/popup-notifications/${popupId}/seen`, 'POST', { userId }),
    onSuccess: () => {
      // Invalider la requête pour les popups non vues
      if (user) {
        queryClient.invalidateQueries({ queryKey: [`/api/popup-notifications/unseen/user/${user.id}`, user.id] });
      }
    },
  });

  // Garder une trace des popups déjà traitées dans cette session  
  const [processedPopupIds, setProcessedPopupIds] = useState<number[]>([]);
  
  // Mettre à jour la référence de l'utilisateur précédent
  useEffect(() => {
    previousUserRef.current = user;
  }, [user]);
  
  // Effet pour réinitialiser le drapeau sessionPopupShown uniquement lors de la première connexion
  useEffect(() => {
    if (userJustLoggedIn) {
      console.log("Utilisateur vient de se connecter, réinitialisation du drapeau sessionPopupShown");
      setSessionPopupShown(false);
      setProcessedPopupIds([]);
    }
  }, [userJustLoggedIn]);
  
  useEffect(() => {
    // Vérifier les notifications popup non vues UNIQUEMENT au chargement initial ou après connexion
    console.log("UseEffect for popups triggered", { 
      user, 
      unseenPopups, 
      currentPopup, 
      sessionPopupShown,
      userJustLoggedIn,
      processedPopupIds 
    });
    
    // N'afficher une popup que si :
    // 1. L'utilisateur est connecté
    // 2. Il y a des popups non vues
    // 3. Aucune popup n'est actuellement affichée
    // 4. Nous n'avons pas encore affiché de popup dans cette session OU l'utilisateur vient de se connecter
    if (user && 
        unseenPopups && 
        unseenPopups.length > 0 && 
        !currentPopup && 
        (!sessionPopupShown || userJustLoggedIn)) {
      
      // Définir les titres des notifications à exclure complètement du système de popup
      const excludedTitles = [
        "Email vérifié avec succès", 
        "Email de vérification envoyé", 
        "Vérification d'email requise",
        "Téléphone vérifié avec succès",
        "Vérification téléphonique requise"
      ];
      
      // IMPORTANT: Marquer automatiquement toutes les notifications de vérification comme vues
      // sans jamais les afficher sous forme de popup pour éviter les boucles
      const verificationPopups = unseenPopups.filter(popup => 
        !processedPopupIds.includes(popup.id) && 
        excludedTitles.includes(popup.title)
      );
      
      if (verificationPopups.length > 0) {
        console.log(`Marquage automatique de ${verificationPopups.length} notifications de vérification sans les afficher`);
        
        const newProcessedIds = verificationPopups.map(popup => popup.id);
        setProcessedPopupIds(prev => [...prev, ...newProcessedIds]);
        
        // Marquer immédiatement ces notifications comme vues dans la base de données
        if (user) {
          verificationPopups.forEach(popup => {
            markAsSeen.mutate({ userId: user.id, popupId: popup.id });
          });
        }
      }
      
      // Traiter uniquement les notifications qui ne sont pas liées à la vérification
      const regularPopups = unseenPopups.filter(popup => 
        !processedPopupIds.includes(popup.id) && 
        !excludedTitles.includes(popup.title)
      );
      
      if (regularPopups.length > 0) {
        console.log("Affichage d'une popup normale:", regularPopups[0]);
        setCurrentPopup(regularPopups[0]);
        setProcessedPopupIds(prev => [...prev, regularPopups[0].id]);
        setSessionPopupShown(true);
      }
    }
  }, [user, unseenPopups, currentPopup, sessionPopupShown, userJustLoggedIn, processedPopupIds, markAsSeen]);

  const showPopup = (popup: PopupNotification) => {
    setCurrentPopup(popup);
  };

  const closePopup = () => {
    if (currentPopup && user) {
      // Marquer la notification comme vue si nécessaire
      console.log("Marking popup as seen", { userId: user.id, popupId: currentPopup.id });
      console.log(`Popup #${currentPopup.id} marked as seen via direct API call`);
      markAsSeen.mutate({ userId: user.id, popupId: currentPopup.id });
      
      // Log de l'état des popups traitées pour cette session
      console.log(`Session popup flag is set to: ${sessionPopupShown}`);
      console.log("Processed popup IDs in this session:", processedPopupIds);
    }
    // Toujours réinitialiser currentPopup, même si user est null
    setCurrentPopup(null);
  };

  const closeAllPopups = () => {
    setCurrentPopup(null);
  };

  return (
    <PopupContext.Provider
      value={{
        currentPopup,
        isPopupOpen: !!currentPopup,
        closePopup,
        showPopup,
        closeAllPopups,
        isLoading: isLoadingPopups || isLoading,
      }}
    >
      {children}
    </PopupContext.Provider>
  );
};

export const usePopupNotifications = () => {
  const context = useContext(PopupContext);
  if (context === undefined) {
    throw new Error('usePopupNotifications doit être utilisé dans un PopupNotificationProvider');
  }
  return context;
};