import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Type pour les informations utilisateur
interface User {
  id: number;
  username: string;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  profileImageUrl?: string | null;
}

// Interface pour le state du store d'authentification
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  logout: () => void;
}

// Création du store d'authentification
export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      
      // Définir l'utilisateur
      setUser: (user: User | null) => set({ 
        user, 
        isAuthenticated: !!user 
      }),
      
      // Définir le token
      setToken: (token: string | null) => set({ token }),
      
      // Déconnecter l'utilisateur
      logout: () => set({ 
        user: null, 
        token: null, 
        isAuthenticated: false 
      }),
    }),
    {
      name: 'waabo-auth-storage',
      // Ne stocker que ces propriétés
      partialize: (state) => ({ token: state.token }),
    }
  )
);

// Hook pour accéder à l'état d'authentification
export const useAuth = () => {
  const { user, isAuthenticated } = useAuthStore();
  return { user, isAuthenticated };
};