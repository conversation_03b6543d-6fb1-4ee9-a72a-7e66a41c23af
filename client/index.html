<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>Waabo Express</title>
    <meta name="description" content="Waabo - Solutions de logistique e-commerce spécialisées dans l'expédition internationale entre la Chine et le Burkina Faso" />
    <meta name="theme-color" content="#004d25" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Waabo">
    
    <!-- Police Roboto pour l'écran de démarrage -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Liens pour PWA -->
    <link rel="manifest" href="/src/manifest.json" />
    <link rel="icon" type="image/svg+xml" href="/src/assets/favicon.v3.svg" />
    <link rel="apple-touch-icon" href="/src/assets/waabologo.v2.png">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Enregistrement du service worker -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/src/service-worker.js')
            .then(function(registration) {
              console.log('Service Worker enregistré avec succès:', registration.scope);
            })
            .catch(function(error) {
              console.log('Échec de l\'enregistrement du Service Worker:', error);
            });
        });
      }
    </script>
  </body>
</html>