# Guide d'intégration Apple ID pour l'application mobile Waabo

Ce guide explique comment configurer et utiliser la connexion Apple ID directe (sans popup) dans l'application mobile Waabo.

## Prérequis

1. Un compte développeur Apple
2. Une App ID configurée dans le portail développeur Apple
3. Une clé privée pour Sign in with Apple
4. La configuration Firebase pour l'authentification native

## Configuration côté Apple Developer

1. **Créer une App ID** (si ce n'est pas déjà fait) :
   - Rendez-vous sur [Apple Developer Portal](https://developer.apple.com/account/resources/identifiers/list)
   - Créez une nouvelle App ID avec le bundle ID `com.waabo.app`
   - Activez la fonctionnalité "Sign in with Apple"

2. **Configurer les Services** :
   - Dans le portail développeur Apple, accédez à "Certificates, Identifiers & Profiles"
   - Allez dans la section "Services IDs"
   - Créez un nouveau Service ID (par exemple, `com.waabo.app.service`)
   - <PERSON>z "Sign in with Apple" et configurez les domaines autorisés et les URL de redirection
     - Domaine : `waabo-app.com`
     - URL de redirection : `https://waabo-app.com/api/auth/apple/callback`

3. **Générer les clés de signature** :
   - Dans le portail développeur Apple, accédez à "Keys"
   - Créez une nouvelle clé privée avec la fonctionnalité "Sign in with Apple"
   - Téléchargez la clé privée (format .p8) et notez le Key ID

## Configuration Firebase

1. **Configurer Firebase Authentication** :
   - Dans la console Firebase, accédez à "Authentication"
   - Activez le fournisseur d'authentification "Apple"
   - Configurez les informations requises :
     - Service ID Apple
     - Team ID Apple
     - Key ID
     - Clé privée
     - Domaines autorisés

2. **Télécharger les fichiers de configuration** :
   - Pour iOS : téléchargez le fichier `GoogleService-Info.plist`
   - Pour Android : téléchargez le fichier `google-services.json`

## Configuration de l'application

### Android (après avoir généré l'application Android)

1. **Placer le fichier de configuration** :
   - Copiez le fichier `google-services.json` dans le dossier `android/app/`

2. **Mettre à jour capacitor.config.ts** :
   - Assurez-vous que la configuration Firebase inclut Apple comme fournisseur d'authentification
   ```typescript
   plugins: {
     FirebaseAuthentication: {
       skipNativeAuth: false,
       providers: ["google.com", "apple.com"]
     }
   }
   ```

### iOS (après avoir généré l'application iOS)

1. **Placer le fichier de configuration** :
   - Copiez le fichier `GoogleService-Info.plist` dans le dossier `ios/App/App/`
   - Ouvrez le projet dans Xcode et ajoutez le fichier au projet

2. **Activer Sign in with Apple dans Xcode** :
   - Ouvrez le projet dans Xcode
   - Allez dans "Signing & Capabilities"
   - Ajoutez la capacité "Sign in with Apple"

3. **Mettre à jour Info.plist** :
   - Ajoutez les URL Schemes nécessaires dans `ios/App/App/Info.plist`

## Variables d'environnement (sur votre serveur Replit)

Assurez-vous que les variables d'environnement suivantes sont définies :

1. `APPLE_CLIENT_ID` : L'identifiant client de votre service Sign in with Apple
2. `APPLE_TEAM_ID` : L'identifiant d'équipe Apple Developer
3. `APPLE_KEY_ID` : L'identifiant de la clé privée générée
4. `APPLE_PRIVATE_KEY` : Le contenu de la clé privée Apple (format .p8)

## Fonctionnement de l'authentification

L'authentification Apple dans l'application mobile se fait en deux étapes :

1. **Authentification côté client** :
   - L'utilisateur appuie sur le bouton "Continuer avec Apple"
   - L'application utilise Firebase Authentication via Capacitor pour initier l'authentification native
   - Pas de popup, l'interface native d'Apple s'affiche directement
   - L'API retourne un token ID et les informations utilisateur

2. **Vérification côté serveur** :
   - Le token ID et les informations utilisateur sont envoyés au serveur
   - Le serveur vérifie le token ID auprès d'Apple
   - Si le token est valide, le serveur crée ou met à jour l'utilisateur dans la base de données
   - Le serveur génère un JWT pour l'authentification dans l'application

## Structure du code

L'implémentation est organisée comme suit :

1. **Backend** :
   - `server/app-apple-auth.ts` : Gère l'authentification côté serveur pour les applications mobiles
   - `server/routes.ts` : Définit la route API pour l'authentification Apple

2. **Frontend** :
   - `client/src/services/capacitorAppleAuth.ts` : Service pour l'authentification Apple sur les appareils mobiles
   - `client/src/components/AppAppleLogin.tsx` : Composant du bouton de connexion Apple pour applications
   - `client/src/components/SocialLoginButtons.tsx` : Intègre tous les boutons de connexion sociale et détecte si l'utilisateur est sur mobile

## Dépannage

### Problèmes courants iOS

1. **Authentification non disponible** : Vérifiez que "Sign in with Apple" est activé dans les capacités Xcode.

2. **Token invalide** : Assurez-vous que les variables d'environnement pour Apple sont correctement configurées.

3. **Échec d'authentification native** : Vérifiez les logs de l'application pour voir si Firebase est correctement configuré.

### Problèmes courants Android

1. **Authentification non disponible** : L'authentification Apple sur Android est gérée par les services Firebase. Vérifiez que le fichier de configuration Firebase est présent et contient la configuration Apple.

## Références

- [Documentation Apple Sign In avec Firebase](https://firebase.google.com/docs/auth/ios/apple)
- [Documentation Capacitor Firebase Authentication](https://github.com/capawesome-team/capacitor-firebase/tree/main/packages/authentication)
- [Documentation Apple Sign in](https://developer.apple.com/sign-in-with-apple/get-started/)
- [Documentation Firebase Authentication](https://firebase.google.com/docs/auth)