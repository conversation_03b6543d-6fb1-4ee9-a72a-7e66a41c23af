# Facebook Integration Guide for Waabo Mobile App

This guide explains how to configure and use Facebook authentication in the Waabo mobile app.

## Prerequisites

1. A Facebook developer account and a Facebook app created on [Facebook for Developers](https://developers.facebook.com/)
2. Facebook App ID
3. Android signing certificate (for Android)
4. Apple Developer account (for iOS)

## Facebook Configuration

1. **Facebook for Developers Console**:
   - Go to [Facebook for Developers](https://developers.facebook.com/)
   - Create or select your app
   - Add the "Facebook Login" product to your app

2. **Android Configuration**:
   - In Facebook Login settings, add your app package: `com.waabo.app`
   - Add debug and production signing fingerprints
   - Add login activity: `com.waabo.app.MainActivity`

3. **iOS Configuration**:
   - In Facebook Login settings, add Bundle ID: `com.waabo.app`
   - Configure URL Scheme: `fb360670239875225` (replace with your App ID)
   - Enable "Single Sign On"
   - Add associated domains: `waabo-app.com`

## App Configuration

### Android (after generating the Android app)

1. Open the file `android/app/src/main/AndroidManifest.xml` and add:

```xml
<application ...>
    <!-- Existing activities here -->
    
    <meta-data android:name="com.facebook.sdk.ApplicationId" 
        android:value="@string/facebook_app_id"/>
    <meta-data android:name="com.facebook.sdk.ClientToken" 
        android:value="@string/facebook_client_token"/>
    
    <activity android:name="com.facebook.FacebookActivity"
        android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
        android:label="@string/app_name" />
        
    <activity android:name="com.facebook.CustomTabActivity" android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="@string/fb_login_protocol_scheme" />
        </intent-filter>
    </activity>
</application>
```

2. Create or modify the file `android/app/src/main/res/values/strings.xml`:

```xml
<resources>
    <string name="app_name">Waabo</string>
    <string name="facebook_app_id">360670239875225</string>
    <string name="fb_login_protocol_scheme">fb360670239875225</string>
    <string name="facebook_client_token">YOUR_CLIENT_TOKEN</string>
</resources>
```

### iOS (after generating the iOS app)

1. Open the file `ios/App/App/Info.plist` and add:

```xml
<dict>
  <!-- Other configurations -->
  
  <key>CFBundleURLTypes</key>
  <array>
    <dict>
      <key>CFBundleURLSchemes</key>
      <array>
        <string>fb360670239875225</string>
      </array>
    </dict>
  </array>
  
  <key>FacebookAppID</key>
  <string>360670239875225</string>
  
  <key>FacebookClientToken</key>
  <string>YOUR_CLIENT_TOKEN</string>
  
  <key>FacebookDisplayName</key>
  <string>Waabo</string>
  
  <key>LSApplicationQueriesSchemes</key>
  <array>
    <string>fbapi</string>
    <string>fbapi20130214</string>
    <string>fbapi20130410</string>
    <string>fbapi20130702</string>
    <string>fbapi20131010</string>
    <string>fbapi20131219</string>
    <string>fbapi20140410</string>
    <string>fbapi20140116</string>
    <string>fbapi20150313</string>
    <string>fbapi20150629</string>
    <string>fbapi20160328</string>
    <string>fbauth</string>
    <string>fbauth2</string>
    <string>fbshareextension</string>
  </array>
</dict>
```

## Usage in the App

The necessary code to use Facebook login has already been implemented in the app:

1. The `@capacitor-community/facebook-login` plugin is installed.
2. The `capacitorFacebookAuth` service has been created to handle authentication.
3. The `AppFacebookLogin` component allows users to log in within the app.
4. A server API (`/api/auth/facebook-app`) handles server-side authentication.

The app automatically detects whether the user is on mobile or web and displays the appropriate Facebook login button.

## Troubleshooting

### Common Android Issues

1. **Signature Error**: Make sure the signing fingerprint matches the one configured in the Facebook console.
   ```bash
   keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore | openssl sha1 -binary | openssl base64
   ```

2. **Facebook SDK Version Issue**: If you encounter errors, verify that the Facebook SDK version used by Capacitor is compatible with your app.

### Common iOS Issues

1. **URL Scheme**: Verify that the URL Scheme is correctly configured in Info.plist.

2. **Permissions**: iOS 14+ requires special permissions for tracking. Make sure your Info.plist contains:
   ```xml
   <key>NSUserTrackingUsageDescription</key>
   <string>This permission allows Waabo to provide you with a personalized experience.</string>
   ```

## References

- [Capacitor Facebook Login Documentation](https://github.com/capacitor-community/facebook-login)
- [Facebook Login Documentation for Android](https://developers.facebook.com/docs/facebook-login/android)
- [Facebook Login Documentation for iOS](https://developers.facebook.com/docs/facebook-login/ios)